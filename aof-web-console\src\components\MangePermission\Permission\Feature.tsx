import { showCustomNotification } from '@/common/Notification'
import CheckboxPermission from '@/components/CheckboxPermission'
import { MESSAGE_STATUS } from '@/constants'
import {
  addMultiplePermissionGroup,
  addPermissionGroup,
  deleteMultiplePermissionGroup,
  getFeatures,
  getListFeatureGrantPerson
} from '@/services/auth'
import { Feature, PermissionGroup } from '@/types/user'
import { Button, Col, Row } from 'antd'
import { useEffect, useState } from 'react'

const FeatureGroup = ({
  state,
  uuidPermissionGroup,
  permissionGroup,
  uuidP,
  setIsDisable,
  setIsLoading,
  refreshData,
  setUuidPermission
}: {
  state: any
  uuidPermissionGroup: string
  permissionGroup: PermissionGroup
  uuidP?: string
  setIsDisable?: (value: boolean) => void
  setIsLoading: (value: boolean) => void
  refreshData?: () => void
  setUuidPermission: (value: string) => void
}) => {
  const [listFeaturesParent, setListFeaturesParent] = useState<Feature[]>([])
  const [listFeaturesChild, setListFeaturesChild] = useState<Feature[]>([])
  const [uuidFeatureActions, setUuidFeatureActions] = useState<string[]>([])

  useEffect(() => {
    getData()
    if (uuidP) getFeaturesPerson()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  useEffect(() => {
    if (permissionGroup?.featureActions) {
      const data = permissionGroup.featureActions.map((item) => item.uuidFeatureAction)
      setUuidFeatureActions(data)
    }
  }, [permissionGroup])

  const getData = async () => {
    setIsLoading(true)
    try {
      const res = await getFeatures()
      if (res.message === MESSAGE_STATUS.SUCCESS) {
        const parentFeatures: Feature[] = []
        const childFeatures: Feature[] = []

        res.object.data.forEach((item: Feature) => {
          if (item.uuidParentFeature === null) {
            parentFeatures.push(item)
          } else {
            childFeatures.push(item)
          }
        })

        setListFeaturesParent(parentFeatures)
        setListFeaturesChild(childFeatures)
      }
    } catch {
      showCustomNotification({
        status: 'error',
        message: 'Thất bại',
        description: 'Có lỗi xảy ra!'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const getFeaturesPerson = async () => {
    try {
      if (!uuidP) return
      const res = await getListFeatureGrantPerson(uuidP)
      if (res.message === MESSAGE_STATUS.SUCCESS) {
        setUuidFeatureActions(res.object.data.map((f: { uuidFeatureAction: any }) => f.uuidFeatureAction))
      }
    } catch {
      showCustomNotification({
        status: 'error',
        message: 'Thất bại',
        description: 'Có lỗi xảy ra!'
      })
    }
  }

  const handleSave = async () => {
    setIsLoading(true)
    try {
      const data = {
        groupName: state.groupName,
        description: state.description,
        uuidFeatureActions
      }
      const res = await addPermissionGroup(data)
      if (res.message === MESSAGE_STATUS.SUCCESS) {
        setIsDisable?.(false)
        setUuidPermission(res.object.uuidPermissionGroup)
        showCustomNotification({
          status: 'success',
          message: 'Thành công',
          description: 'Thêm mới nhóm quyền thành công!'
        })
      }
    } catch (err: any) {
      if (err.response?.data?.messageFields?.[0].fieldName === 'uuidFeatureActions') {
        showCustomNotification({
          status: 'error',
          message: 'Thất bại',
          description: 'Vui lòng chọn ít nhất 1 quyền để khởi tạo!'
        })
      } else {
        showCustomNotification({
          status: 'error',
          message: 'Thất bại',
          description: 'Có lỗi xảy ra!'
        })
      }
    } finally {
      setIsLoading(false)
    }
  }

  const handleUpdate = async () => {
    setIsLoading(true)
    try {
      if (!permissionGroup.featureActions) return
      const dataAdd = uuidFeatureActions
        .filter((item) => !permissionGroup.featureActions?.some((i) => i.uuidFeatureAction === item))
        .map((item) => ({
          uuidFeatureAction: item,
          uuidPermissionGroup: uuidPermissionGroup
        }))
      const dataRemove = permissionGroup.featureActions
        .filter((item) => !uuidFeatureActions.includes(item.uuidFeatureAction))
        .map((item) => ({
          uuidFeatureAction: item.uuidFeatureAction,
          uuidPermissionGroup: uuidPermissionGroup
        }))
      let resAdd, resDel
      if (dataAdd.length > 0) {
        resAdd = await addMultiplePermissionGroup(dataAdd)
      }
      if (dataRemove.length > 0) {
        resDel = await deleteMultiplePermissionGroup(dataRemove)
      }
      if (dataAdd.length === 0 && dataRemove.length === 0) {
        showCustomNotification({
          status: 'info',
          message: 'Chưa có thay đổi!',
          description: 'Vui lòng chọn chức năng để lưu!'
        })
      }

      if (resAdd?.message === MESSAGE_STATUS.SUCCESS || resDel?.message === MESSAGE_STATUS.SUCCESS) {
        refreshData?.()
        showCustomNotification({
          status: 'success',
          message: 'Thành công',
          description: 'Cập nhật nhóm quyền thành công!'
        })
      }
    } catch {
      showCustomNotification({
        status: 'error',
        message: 'Thất bại',
        description: 'Có lỗi xảy ra!'
      })
    } finally {
      setIsLoading(false)
    }
  }
  const isDisabled = uuidP || permissionGroup.allowModification === 0 ? true : false

  return (
    <div>
      <Row gutter={[56, 40]}>
        {listFeaturesParent.map((parent: Feature, index) => {
          const filteredChildFeatures = listFeaturesChild.filter(
            (childItem) => childItem.uuidParentFeature === parent.uuidFeature
          )

          return (
            <Col xs={24} md={12} xl={8} key={parent.uuidFeature}>
              <div className='flex flex-col gap-5'>
                <h2 className='text-lg font-semibold'>
                  {index + 1}. {parent.featureName}
                </h2>
                {filteredChildFeatures.map((childItem) => (
                  <CheckboxPermission
                    showTitle={true}
                    key={childItem.uuidFeature}
                    title={childItem.featureName}
                    data={childItem.featureActions}
                    isChange={!isDisabled}
                    uuidFeatureActions={uuidFeatureActions}
                    setUuidFeatureActions={setUuidFeatureActions}
                  />
                ))}
                {parent.featureActions?.length > 0 && (
                  <CheckboxPermission
                    title=''
                    data={parent.featureActions}
                    isChange={!isDisabled}
                    uuidFeatureActions={uuidFeatureActions}
                    setUuidFeatureActions={setUuidFeatureActions}
                  />
                )}
              </div>
            </Col>
          )
        })}
      </Row>
      <Button
        type='primary'
        className='mt-5'
        size='large'
        style={{ width: '128px' }}
        hidden={isDisabled}
        onClick={uuidPermissionGroup ? handleUpdate : handleSave}
      >
        Lưu
      </Button>
    </div>
  )
}

export default FeatureGroup
