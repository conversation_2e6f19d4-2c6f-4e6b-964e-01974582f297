import PermissionGroupTable from '@/components/Tables/Permission/GroupTable'
import PermissionPersonTable from '@/components/Tables/Permission/PersonTable'
import { enumToOptions } from '@/helpers'
import LayoutCotent from '@/layouts/LayoutCotent'
import useLayoutStore from '@/stores/layout'
import { useSegmentStore } from '@/stores/useSegmentStore'
import { ResponseList } from '@/types/common'
import { initialProperty, pageSize, PremissionType } from '@/types/components'
import { Pagination, Segmented, Select, Spin } from 'antd'
import { useState } from 'react'

const ManageFeature = () => {
  const { isMobile } = useLayoutStore()
  const { activeList, setActiveList } = useSegmentStore()

  const [dataSource, setDataSource] = useState<ResponseList<any>>()
  const [pageProperty, setPageProperty] = useState<{
    page: number
    maxSize: number
    totalElement: number
  }>(initialProperty)
  const [isLoading, setIsLoading] = useState<boolean>(false)

  const typeOptions = enumToOptions(PremissionType)

  return (
    <>
      <LayoutCotent title={['Phân quyền']}>
        <Spin spinning={isLoading}>
          <Segmented
            value={activeList}
            onChange={setActiveList}
            size='large'
            options={typeOptions}
            className='mb-5 border max-sm:flex max-sm:[&_.ant-segmented-item]:w-1/2'
          />
          {activeList === PremissionType['Danh sách nhóm quyền'] ? (
            <PermissionGroupTable
              page={pageProperty.page}
              maxSize={pageProperty.maxSize}
              dataSource={dataSource}
              setDataSource={setDataSource}
              setIsLoading={setIsLoading}
            />
          ) : (
            <PermissionPersonTable
              page={pageProperty.page}
              maxSize={pageProperty.maxSize}
              dataSource={dataSource}
              isLoading={isLoading}
              setDataSource={setDataSource}
              setIsLoading={setIsLoading}
            />
          )}

          {dataSource?.data && dataSource.data.length > 0 && (
            <div className='my-4 flex max-sm:flex-col max-sm:items-center items-center justify-between'>
              <div className='flex items-center gap-2'>
                <p className='text-xs font-normal text-gray-400'>Số bản ghi mỗi trang </p>
                <Select
                  size='small'
                  options={pageSize}
                  value={pageProperty.maxSize}
                  onChange={(e) => setPageProperty({ ...pageProperty, maxSize: e, page: 1 })}
                />
                <p className='text-xs font-normal text-gray-400 hidden sm:block'>
                  trên tổng {dataSource.totalElement} dữ liệu{' '}
                </p>
              </div>
              <Pagination
                onChange={(page) => {
                  setPageProperty({ ...pageProperty, page })
                }}
                align='end'
                showSizeChanger={isMobile}
                current={dataSource.page}
                defaultCurrent={1}
                pageSize={dataSource.maxSize}
                defaultPageSize={10}
                total={dataSource.totalElement}
              />
            </div>
          )}
        </Spin>
      </LayoutCotent>
    </>
  )
}

export default ManageFeature
