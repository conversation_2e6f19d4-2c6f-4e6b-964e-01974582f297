import { Button, Divider, Form, Input, Modal } from 'antd'
import { ModalProps } from '@/types/common'
import { useEffect, useState } from 'react'
import { useForm } from 'antd/es/form/Form'
import { runes } from 'runes2'

interface PermissionGroupProps extends ModalProps {
  permissrionData: any
}

const PermissionGroupModal = ({
  isModalOpen,
  isLoading,
  permissrionData,
  update,
  handleCancel,
  handleOk
}: PermissionGroupProps) => {
  const [form] = useForm()
  const [isDisabled, setIsDisabled] = useState(true)

  useEffect(() => {
    if (isModalOpen) {
      form.setFieldsValue({
        groupName: permissrionData.groupName,
        discription: permissrionData.description
      })
      checkDisabled()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isModalOpen, form, permissrionData])

  const checkDisabled = () => {
    const values = form.getFieldsValue()
    const hasError = form.getFieldsError().some(({ errors }) => errors.length > 0)
    setIsDisabled(!values.groupName?.trim() || hasError)
  }

  const handleSubmit = async () => {
    const values = await form.validateFields()
    handleOk?.({ groupName: values.groupName.trim(), description: values.discription.trim() })
    form.resetFields()
  }

  const handleCancelModal = () => {
    form.resetFields()
    handleCancel?.(undefined)
  }

  const footerModal = (
    <div className='flex items-center justify-center gap-4 p-2 pt-4'>
      <Button
        onClick={handleCancelModal}
        size='large'
        variant='outlined'
        color='primary'
        className='min-w-[126px] bg-neutral-9 hover:opacity-70'
      >
        Hủy
      </Button>
      <Button
        loading={isLoading}
        disabled={isDisabled}
        onClick={handleSubmit}
        type='primary'
        size='large'
        className='min-w-[126px]'
      >
        {update ? 'Lưu' : 'Tiếp tục'}
      </Button>
    </div>
  )

  return (
    <Modal
      open={isModalOpen}
      onCancel={handleCancelModal}
      footer={footerModal}
      width={582}
      closable
      title={<h2 className='mb-5 text-center text-lg font-semibold'>{update ? 'Sửa' : 'Thêm mới'} nhóm quyền</h2>}
    >
      <Form form={form} layout='vertical' requiredMark={false} onValuesChange={checkDisabled}>
        <Form.Item
          name='groupName'
          label={
            <p>
              Tên nhóm quyền <span className='text-error'>*</span>
            </p>
          }
          rules={[
            {
              validator: (_, value) =>
                value && value.trim() ? Promise.resolve() : Promise.reject(new Error('Vui lòng nhập tên nhóm quyền!'))
            }
          ]}
        >
          <Input allowClear size='large' placeholder='Nhập tên' maxLength={30} />
        </Form.Item>
        <Form.Item name='discription' label='Mô tả'>
          <Input.TextArea
            size='large'
            placeholder='Nhập mô tả'
            style={{ resize: 'none' }}
            rows={3}
            count={{
              show: true,
              max: 100,
              strategy: (txt) => runes(txt).length,
              exceedFormatter: (txt, { max }) => runes(txt).slice(0, max).join('')
            }}
          />
        </Form.Item>
      </Form>

      <Divider className='mb-0' />
    </Modal>
  )
}

export default PermissionGroupModal
