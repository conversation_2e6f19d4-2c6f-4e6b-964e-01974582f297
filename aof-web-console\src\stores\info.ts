import { getDetailsAcademicYear, getListAcademicYears } from '@/services/academicYear'
import { MAX_SIZE_PAGE } from '@/types/components'
import { AcademicYear, Semester } from '@/types/infomation'
import { Person } from '@/types/person'
import { create } from 'zustand'

type InfoStore = {
  loadingData: boolean
  listStudents: Person[]
  academicYears: AcademicYear[]
  semesters: Semester[]
  loadAcademicYears: () => Promise<void>
  setPersons: (e: Person[]) => void
  setAcademicYears: (e: AcademicYear[]) => void
  loadSemesters: (e: string) => Promise<void>
  resetSemesters: () => void
}

const initialState = {
  loadingData: false,
  academicYears: [],
  semesters: [],
  listStudents: []
}

export const useInfoStore = create<InfoStore>((set) => ({
  ...initialState,

  loadAcademicYears: async () => {
    const res = await getListAcademicYears({ page: 1, maxSize: MAX_SIZE_PAGE })
    return set((state: any) => ({ ...state, academicYears: res.object.data }))
  },
  loadSemesters: async (uuid: string) => {
    try {
      set((state: any) => ({ ...state, loadingData: true }))
      const res = await getDetailsAcademicYear(uuid)

      return set((state: any) => ({ ...state, semesters: res.object.semesters, loadingData: false }))
    } finally {
      set((state: any) => ({ ...state, loadingData: false }))
    }
  },
  setAcademicYears: (value) => set((state) => ({ ...state, academicYears: value })),
  setPersons: (value) => set((state) => ({ ...state, listStudents: value })),
  resetSemesters: () => set((state) => ({ ...state, semesters: [] }))
}))
