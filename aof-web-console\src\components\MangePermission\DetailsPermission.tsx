import LayoutCotent from '@/layouts/LayoutCotent'
import { permissionList } from '@/types/components'
import { Segmented, Spin } from 'antd'
import { useCallback, useEffect, useState } from 'react'
import { useLocation, useNavigate, useParams } from 'react-router-dom'
import { MESSAGE_STATUS } from '@/constants'
import ModalError from '../Modals/ErrorLoadingModal'
import { useSegmentStore } from '@/stores/useSegmentStore'
import useGroupStore from '@/stores/group'
import FeatureGroup from './Permission/Feature'
import Class from './Permission/Class'
import User from './Permission/User'
import { getPermissionGroupDetail } from '@/services/auth'
import { PermissionGroup } from '@/types/user'

const DetailsPermission = () => {
  const navigate = useNavigate()
  const { state } = useLocation()
  const { uuidPermissionGroup } = useParams()

  const { activePermissionDetail, setActivePermissionDetail } = useSegmentStore()
  const { groupName, setGroupName } = useGroupStore()

  const [permissionGroup, setPermissionGroup] = useState<PermissionGroup>({} as PermissionGroup)
  const [uuidPermission, setUuidPermission] = useState('')

  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [openErrorModal, setOpenErrorModal] = useState<boolean>(false)
  const [isDisable, setIsDisable] = useState<boolean>(state ? true : false)

  useEffect(() => {
    if (uuidPermissionGroup) {
      setUuidPermission(uuidPermissionGroup)
    }
  }, [uuidPermissionGroup])

  const getDetails = useCallback(async () => {
    try {
      if (!uuidPermission) return

      setIsLoading(true)
      const res = await getPermissionGroupDetail(uuidPermission)
      if (res.message === MESSAGE_STATUS.SUCCESS) {
        setPermissionGroup(res.object)
        setGroupName(res.object.groupName)
      }
      setIsLoading(false)
    } catch {
      setOpenErrorModal(true)
    }
  }, [setGroupName, uuidPermission])

  useEffect(() => {
    getDetails()

    if (state) {
      setGroupName(state.groupName)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [getDetails])

  const view = () => {
    switch (activePermissionDetail) {
      case 'Phân quyền chức năng':
        return (
          <FeatureGroup
            state={state}
            uuidPermissionGroup={uuidPermission}
            permissionGroup={permissionGroup}
            setIsLoading={setIsLoading}
            refreshData={getDetails}
            setIsDisable={setIsDisable}
            setUuidPermission={setUuidPermission}
          />
        )
      case 'Phân quyền lớp học':
        return (
          <Class setIsLoading={setIsLoading} permissionGroup={permissionGroup} uuidPermissionGroup={uuidPermission} />
        )
      case 'Phân quyền người dùng':
        return <User uuidPermissionGroup={uuidPermission} hidden={permissionGroup.groupCode === 'student'} />
      default:
        return (
          <FeatureGroup
            state={state}
            uuidPermissionGroup={uuidPermission}
            permissionGroup={permissionGroup}
            setIsLoading={setIsLoading}
            refreshData={getDetails}
            setIsDisable={setIsDisable}
            setUuidPermission={setUuidPermission}
          />
        )
    }
  }

  return (
    <>
      <ModalError
        isModalOpen={openErrorModal}
        handleCancel={() => {
          navigate('/manage-class')
          setOpenErrorModal(false)
        }}
      />
      <LayoutCotent title={['Phân quyền', 'Danh sách nhóm quyền', groupName]} btnBack={true}>
        <Spin spinning={isLoading}>
          <div className='overflow-x-auto' style={{ scrollbarWidth: 'none' }}>
            <Segmented
              value={activePermissionDetail}
              onChange={setActivePermissionDetail}
              disabled={isDisable}
              size='large'
              options={permissionList}
              className='mb-5 border'
            />
          </div>
          {view()}
        </Spin>
      </LayoutCotent>
    </>
  )
}

export default DetailsPermission
