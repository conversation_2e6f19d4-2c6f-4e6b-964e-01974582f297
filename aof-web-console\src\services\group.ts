import { ApiResponse, ResponseList } from '@/types/common'
import { Group, GroupParams, GroupParamsSearch, GroupParamsUpdate } from '@/types/group'
import { Semester } from '@/types/infomation'
import axiosInstance from '@/utils/axios'

export type propertiesSort = 'fullName' | 'personCode' | 'email' | 'status'

//2.3. Thêm mới group.
export async function addNewGroup(params: GroupParams): Promise<ApiResponse<any>> {
  const response = await axiosInstance.post('/web-service/v1/groups', params)
  return response.data
}

//2.4. Danh sách group.
export async function getListGroups(params: GroupParamsSearch): Promise<ApiResponse<ResponseList<Group>>> {
  const response = await axiosInstance.get('/web-service/v1/groups', { params })
  return response.data
}

//2.5. Chỉnh sửa group.
export async function updateGroup(uuidGroup: string, params: GroupParamsUpdate): Promise<ApiResponse<any>> {
  const response = await axiosInstance.put(`/web-service/v1/groups/${uuidGroup}`, params)
  return response.data
}

//2.6. Xóa group.
export async function deleteGroup(uuidGroup: string): Promise<ApiResponse<any>> {
  const response = await axiosInstance.delete(`/web-service/v1/groups/${uuidGroup}`)
  return response.data
}

//2.7. Xem chi tiết group.
export async function getDetailsGroup(uuidGroup: string): Promise<ApiResponse<Group>> {
  const response = await axiosInstance.get(`/web-service/v1/groups/${uuidGroup}`)
  return response.data
}

//2.13. Thêm mới kỳ học
export async function addNewSemester(params: Semester): Promise<ApiResponse<any>> {
  const response = await axiosInstance.post('/web-service/v1/semesters', params)
  return response.data
}

//2.14. Chỉnh sửa kỳ học
export async function updateSemester(uuidSemester: string, params: Semester): Promise<ApiResponse<any>> {
  const response = await axiosInstance.put(`/web-service/v1/semesters/${uuidSemester}`, params)
  return response.data
}

//2.15. Xóa kỳ học
export async function deleteSemester(uuidSemester: string): Promise<ApiResponse<any>> {
  const response = await axiosInstance.delete(`/web-service/v1/semesters/${uuidSemester}`)
  return response.data
}
