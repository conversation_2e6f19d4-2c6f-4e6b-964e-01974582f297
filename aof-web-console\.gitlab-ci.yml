####################
# Global variables #
####################

variables:
  REGISTRY_REPO: hvtc
  SERVICE_NAME: aof-web-console
  IMAGE_JOB_DEPLOY: crelease.devops.vnpt.vn:10121/image-base/gitlab-ic:1.0.0
  IMAGE_JOB_NOTIFY: crelease.devops.vnpt.vn:10121/image-base/image-base-dev:latest
  WEBHOOK_URL: "https://api.telegram.org/bot889710780:AAF_2L49ARVw7rRF1i8ySFDNPdr9f_Fktv4/sendMessage"
  USERID: "-362307508"
  HELM_CHART_PATH_PREFIX: helm-chart-vnpt/aof-backend 

############
# Pipeline #
############
stages:
  - build
  - deploy


default:
  tags:
    - k8s

.build_image: &build_image
  stage: build
  allow_failure: false
  image: crelease.devops.vnpt.vn:10121/image-base/image-base-standard:2.0.0
  retry: 2
  script:
    - ls -al
    - cat .env
    - docker build --build-arg .env -t $DOMAIN_REGISTRY/$REGISTRY_REPO/$SERVICE_NAME-$CI_COMMIT_REF_NAME:$CI_PIPELINE_IID -f Dockerfile .
    - docker push $DOMAIN_REGISTRY/$REGISTRY_REPO/$SERVICE_NAME-$CI_COMMIT_REF_NAME:$CI_PIPELINE_IID
    - docker image rm $DOMAIN_REGISTRY/$REGISTRY_REPO/$SERVICE_NAME-$CI_COMMIT_REF_NAME:$CI_PIPELINE_IID

.deploy: &deploy
  stage: deploy
  image: $IMAGE_JOB_DEPLOY
  retry: 2
  script:
    - export IMAGE_REPO=$DOMAIN_REGISTRY/$REGISTRY_REPO/$SERVICE_NAME-$CI_COMMIT_REF_NAME
    - GITIC -url $GITOPS_REPO -cmd clone -branch $GITOPS_BRANCH
    - cd /app/source
    - /usr/bin/yq eval -i ".image.repository = \"$IMAGE_REPO\"" $CD_VALUES_FILE
    - /usr/bin/yq eval -i ".image.tag = \"$CI_PIPELINE_IID\"" $CD_VALUES_FILE
    - GITIC -cmd commit -message "Update manifest $SERVICE_NAME $CI_PIPELINE_IID" -branch $GITOPS_BRANCH
    - GITIC -cmd push -url $GITOPS_REPO -branch $GITOPS_BRANCH

build_aof_fe:
  rules:
    - if: $CI_COMMIT_BRANCH == "dev"
      variables:
        DOMAIN_REGISTRY: icr.icenter.ai
  before_script:
    - export SPECIFIC_DOCKERFILE="${CI_PROJECT_DIR}/Dockerfile"
  <<: *build_image
  tags:
    - runner-gpu87

deploy_aof_fe:
  rules:
    - if: $CI_COMMIT_BRANCH == "dev"
      variables:
        GITOPS_REPO: https://scm.devops.vnpt.vn/devops/devopsic.git
        GITOPS_BRANCH: master
        CD_VALUES_FILE: ${HELM_CHART_PATH_PREFIX}/aof-web-console/values.yaml
        DOMAIN_REGISTRY: icr.icenter.ai
  <<: *deploy
  tags:
    - runner-gpu87

