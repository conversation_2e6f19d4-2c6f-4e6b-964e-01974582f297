import LayoutCotent from '@/layouts/LayoutCotent'
import { useCallback, useEffect, useState } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import Svg from '../../Svg'
import { PlusOutlined, SyncOutlined } from '@ant-design/icons'
import { Button, DatePicker, Segmented, Spin, TimePicker } from 'antd'
import { showCustomNotification } from '@/common/Notification'
import { MESSAGE_STATUS } from '@/constants'
import { getListAttendanceRecords, getListSessionAttendances } from '@/services/session'
import { Attendance } from '@/types/session'
import AttendaceRecordTable from '../../Tables/AttendaceRecordTable'
import dayjs from 'dayjs'
import { getDetailsGroup } from '@/services/group'
import { MAX_SIZE_PAGE, TYPE_PERSON } from '@/types/components'
import { exportToExcel, mergeAttendanceData } from '@/helpers'
import { checkFeature, FF_DEV_0044, isAdmin } from '@/utils/feature-flags'
import useAuthStore from '@/stores/auth'

const { RangePicker } = TimePicker

export interface Record {
  uuidSessionAttendance: string
  isDisabled: boolean
  startTime: dayjs.Dayjs | null
  endTime: dayjs.Dayjs | null
  sessionDate?: string
  ordinalNoInDate?: number
}

const initialAttendanceRecord: Record = {
  uuidSessionAttendance: '',
  isDisabled: false,
  startTime: null,
  endTime: null,
  sessionDate: '',
  ordinalNoInDate: 0
}
const SessionAttendance = () => {
  const navigate = useNavigate()
  const [groupName, setGroupName] = useState('')
  const { uuidGroup, uuidSession } = useParams()
  const { user, featFlagsAction } = useAuthStore()

  const [attendanceRecord, setAttendanceRecord] = useState<Record>(initialAttendanceRecord)
  const [listAttendances, setListAttendances] = useState<Attendance[]>([])
  const [isReload, setIsReload] = useState<boolean>(false)
  const isFFA = (code: string) => checkFeature(code, user, featFlagsAction)

  const [isLoading, setIsLoading] = useState<boolean>(false)

  const getDetails = useCallback(async () => {
    try {
      setIsLoading(true)
      if (!uuidGroup) return
      const res = await getDetailsGroup(uuidGroup)
      if (res.message === MESSAGE_STATUS.SUCCESS) {
        setGroupName(res.object.groupName)
      }
    } catch {
      console.log('Lỗi không lấy được thông tin khóa học!')
    }
  }, [uuidGroup])

  const getAttendances = useCallback(async () => {
    try {
      setIsLoading(true)
      if (!uuidSession) return
      const res = await getListSessionAttendances(uuidSession)
      if (res.message === MESSAGE_STATUS.SUCCESS) {
        setListAttendances(res.object)
        setAttendanceRecord({
          ...attendanceRecord,
          sessionDate: dayjs(res.object[0].startTime, 'DD/MM/YYYY HH:mm:ss').format('DD/MM/YYYY'),
          ordinalNoInDate: res.object.length
        })
      }
    } catch {
      showCustomNotification({
        status: 'error',
        message: 'Thất bại!',
        description: 'Có lỗi xảy ra!'
      })
    } finally {
      setIsLoading(false)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [uuidSession])

  useEffect(() => {
    getAttendances()
    getDetails()
  }, [getAttendances, getDetails])

  const exportReport = async () => {
    try {
      const data = await getListAttendanceRecords({
        page: 1,
        maxSize: MAX_SIZE_PAGE,
        uuidSession
      }).then((res) =>
        res.object?.data.map((e) => ({
          attendanceStatus: e.attendanceStatus,
          personId: e.person?.personCode,
          personName: e.person?.fullName
        }))
      )

      const getChekinTime = await Promise.all(
        listAttendances
          .filter((e) => e.status === 2)
          .map((attendance) =>
            getListAttendanceRecords({
              page: 1,
              maxSize: MAX_SIZE_PAGE,
              uuidSession,
              uuidSessionAttendance: attendance.uuidSessionAttendance
            }).then((res) =>
              res.object?.data.map((e) => ({
                checkinTime: e.checkInRecord?.checkInTime ? dayjs(e.checkInRecord?.checkInTime) : '',
                personId: e.person?.personCode,
                uuidSessionAttendance: attendance.uuidSessionAttendance
              }))
            )
          )
      )

      const exportData = mergeAttendanceData(data, getChekinTime)

      exportToExcel(exportData, groupName, attendanceRecord.sessionDate)
    } catch {
      showCustomNotification({
        status: 'error',
        message: 'Thất bại!',
        description: 'Có lỗi xảy ra!'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const options = [
    {
      label: <div className='w-full'>Tổng hợp</div>,
      value: ''
    },
    ...listAttendances.map((attendance, index) => ({
      label: (
        <div className='flex items-center gap-2'>
          {`Điểm danh ${index + 1}`}
          {attendance.status === 1 && <Svg src='/assets/icons/status/warning.svg' className='h-5 w-5' />}
          {attendance.status === 2 && <Svg src='/assets/icons/status/done.svg' className='h-5 w-5' />}
        </div>
      ),
      value: attendance.uuidSessionAttendance
    })),
    ...(isFFA(FF_DEV_0044)
      ? [
          {
            label: (
              <div
                onClick={() =>
                  navigate(`/manage-class/${uuidGroup}/checkin`, {
                    state: { sessionDate: attendanceRecord.sessionDate, uuidSession: uuidSession, typeName: 'Lớp học' }
                  })
                }
                className='icon-segmented'
              >
                <PlusOutlined className='m-2 h-5 w-5' />
              </div>
            ),
            value: 'action'
          }
        ]
      : [])
  ]

  const changeOption = (e: any) => {
    const data = listAttendances.find((attendance) => attendance.uuidSessionAttendance === e)
    if (data) {
      setAttendanceRecord({
        ...attendanceRecord,
        uuidSessionAttendance: data.uuidSessionAttendance,
        isDisabled: data.status === 1 ? false : true,
        startTime: dayjs(data.startTime, 'DD/MM/YYYY HH:mm:ss'),
        endTime: dayjs(data.endTime, 'DD/MM/YYYY HH:mm:ss')
      })
    } else {
      setAttendanceRecord({
        ...attendanceRecord,
        uuidSessionAttendance: e,
        isDisabled: false,
        startTime: null,
        endTime: null
      })
    }
  }

  const handleRefresh = () => {
    getAttendances()
    setAttendanceRecord(initialAttendanceRecord)
  }

  const isHiddened =
    attendanceRecord.uuidSessionAttendance !== '' && attendanceRecord.uuidSessionAttendance !== 'action'

  return (
    <>
      <LayoutCotent
        title={[
          'Quản lý lớp',
          'Lớp học',
          `${groupName}`,
          `${attendanceRecord.sessionDate} (${attendanceRecord.ordinalNoInDate})`
        ]}
        btnBack={true}
      >
        <Spin spinning={isLoading}>
          <div className='overflow-x-auto' style={{ scrollbarWidth: 'none' }}>
            <Segmented
              size='large'
              options={options}
              value={attendanceRecord.uuidSessionAttendance}
              onChange={(e) => changeOption(e)}
              className='mb-5 border icon-segmented'
            />
          </div>
          {isHiddened && (
            <div className='flex gap-5 max-sm:gap-2 items-end justify-between mb-5'>
              <div className='flex gap-5 max-sm:gap-2'>
                <div className='max-sm:flex-[3.1]'>
                  <label>
                    Ngày <span className='text-error'>*</span>
                  </label>
                  <DatePicker
                    size='large'
                    disabled
                    suffixIcon={<Svg src='/assets/icons/common/datepicker.svg' className='h-4 w-4' />}
                    value={dayjs(attendanceRecord.sessionDate, 'DD/MM/YYYY')}
                    className='w-full border-neutral-7'
                    format='DD/MM/YYYY'
                  />
                </div>
                <div className='max-sm:flex-[4]'>
                  <label>
                    Giờ điểm danh <span className='text-error'>*</span>
                  </label>
                  <RangePicker
                    size='large'
                    value={[attendanceRecord.startTime, attendanceRecord.endTime]}
                    className='w-full border-neutral-7'
                    format='HH:mm'
                    disabled={attendanceRecord.isDisabled || user.personType === TYPE_PERSON['Sinh viên']}
                    onChange={(values) => {
                      if (values && Array.isArray(values) && values[0] && values[1]) {
                        setAttendanceRecord({ ...attendanceRecord, startTime: values[0], endTime: values[1] })
                      }
                    }}
                  />
                </div>
              </div>
              {isAdmin(user) && (
                <div className='max-sm:flex-[2]'>
                  <Button
                    size='large'
                    onClick={() => setIsReload(true)}
                    icon={<SyncOutlined spin={false} />}
                    color='primary'
                    variant='outlined'
                  />
                </div>
              )}
            </div>
          )}
          {isAdmin(user) && !isHiddened && (
            <div className='flex justify-end max-xs:flex-col mb-5'>
              <Button
                size='large'
                icon={<Svg src='/assets/icons/common/export-data.svg' className='h-5 w-5 mt-1 ' />}
                color='primary'
                variant='outlined'
                onClick={exportReport}
              >
                Xuất báo cáo
              </Button>
            </div>
          )}
          <AttendaceRecordTable
            isHiddened={isHiddened}
            isReload={isReload}
            setIsReload={setIsReload}
            uuidSession={uuidSession}
            attendanceRecord={attendanceRecord}
            setIsLoading={setIsLoading}
            refresh={handleRefresh}
          />
        </Spin>
      </LayoutCotent>
    </>
  )
}
export default SessionAttendance
