import { ApiResponse, ResponseList } from '@/types/common'
import { AcademicYearParamsSearch } from '@/types/group'
import { AcademicYear } from '@/types/infomation'
import axiosInstance from '@/utils/axios'

//2.8. Danh sách năm học.
export async function getListAcademicYears(
  params: AcademicYearParamsSearch
): Promise<ApiResponse<ResponseList<AcademicYear>>> {
  const response = await axiosInstance.get('/web-service/v1/academic-years', { params })
  return response.data
}

//2.9. Thêm mới năm học.
export async function addNewAcademicYear(params: AcademicYear): Promise<ApiResponse<any>> {
  const response = await axiosInstance.post('/web-service/v1/academic-years', params)
  return response.data
}

//2.10. Chỉnh sửa năm học.
export async function updateAcademicYear(uuidAcademicYear: string, params: AcademicYear): Promise<ApiResponse<any>> {
  const response = await axiosInstance.put(`/web-service/v1/academic-years/${uuidAcademicYear}`, params)
  return response.data
}

//2.11. Xoá năm học.
export async function deleteAcademicYear(uuidAcademicYear: string): Promise<ApiResponse<any>> {
  const response = await axiosInstance.delete(`/web-service/v1/academic-years/${uuidAcademicYear}`)
  return response.data
}

//2.12. Xem chi tiết năm học.
export async function getDetailsAcademicYear(uuidAcademicYear: string): Promise<ApiResponse<AcademicYear>> {
  const response = await axiosInstance.get(`/web-service/v1/academic-years/${uuidAcademicYear}`)
  return response.data
}
