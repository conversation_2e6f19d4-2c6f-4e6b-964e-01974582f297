import { ERROR_FROM_USER } from '@/constants'
import { CalendarEvent } from '@/types/calendar'
import { CustomField } from '@/types/common'
import { customFieldPersonList, dayMap, Gender, reversedDayMap, TYPE_PERSON, UserStatus } from '@/types/components'
import { Group } from '@/types/group'
import { Person, PersonParams, PersonStatus } from '@/types/person'
import { AttendanceRecord, StudentCheckIn } from '@/types/session'
import dayjs from 'dayjs'
import * as XLSX from 'xlsx'

export const convertDatetoString = (date: Date): string => {
  return dayjs(date).format('DD/MM/YYYY')
}

export const findCustomField = (fieldName: string, listCustomFields: CustomField[]) =>
  listCustomFields.find((field) => field.customFieldName === fieldName)?.uuidCustomField ?? ''

// Hàm kiểm tra trường bắt buộc
const checkRequiredField = (field: string) => {
  const value = String(field ?? '').trim()
  if (!value) {
    throw new Error(ERROR_FROM_USER.MISSING_REQUIRED_FIELD)
  }
  return value
}

// Hàm lấy danh sách tên từ enum
export const getEnumNames = <T extends object>(enumObj: T): string[] => {
  return Object.keys(enumObj).filter((key) => isNaN(Number(key)))
}

export const getEnumNameByValue = <T extends object>(enumObj: T, value: number): string => {
  return Object.keys(enumObj).find((key) => enumObj[key as keyof T] === value) || ''
}

export const enumToOptions = <T extends object>(enumObj: T) => {
  return Object.entries(enumObj)
    .filter(([, value]) => !isNaN(Number(value)))
    .map(([key, value]) => ({
      label: key,
      value: Number(value)
    }))
}

export const convertToPersonParams = (
  person: any,
  personType: number,
  listGroups: Group[],
  customFields: CustomField[]
): PersonParams => {
  if (!person) {
    throw new Error(ERROR_FROM_USER.INVALID_STUDENT_DATA)
  }

  // Kiểm tra các trường bắt buộc (*)
  const personCode = checkRequiredField(person['ID*'])
  const fullName = checkRequiredField(person['Họ tên*'])
  const email = checkRequiredField(person['Email*'])
  const status = checkRequiredField(person['Trạng thái tài khoản*'])

  let group: Group | undefined = undefined
  // Kiểm tra trường 'Khóa*' có tồn tại hay không
  const groupName = person['Khóa*'] ? String(person['Khóa*']).trim() : null
  if (groupName !== null) {
    if (!groupName) {
      throw new Error(ERROR_FROM_USER.MISSING_REQUIRED_FIELD)
    }

    // Tìm group
    group = listGroups.find((g) => g.groupName === groupName)
    if (!group) {
      throw new Error(ERROR_FROM_USER.INVALID_GROUP)
    }
  }

  // Xử lý trạng thái tài khoản
  const statusKey = UserStatus[status as keyof typeof UserStatus]
  if (statusKey === undefined) {
    throw new Error(ERROR_FROM_USER.INVALID_STATUS)
  }

  // Xử lý giới tính (không bắt buộc)
  const gender = person['Giới tính']?.trim()
  const genderKey = gender ? Gender[gender as keyof typeof Gender] : undefined
  if (gender && genderKey === undefined) {
    throw new Error(ERROR_FROM_USER.INVALID_GENDER)
  }

  const specializationField = customFields.find((field) => field.customFieldName === customFieldPersonList[1])
  const schoolField = customFields.find((field) => field.customFieldName === customFieldPersonList[2])
  const departmentField = customFields.find((field) => field.customFieldName === customFieldPersonList[0])

  return {
    personCode: personCode.toLowerCase(),
    fullName,
    status: statusKey,
    personType,
    uuidGroup: (group && group.uuidGroup) || undefined,
    email,
    gender: genderKey,
    phoneNo: person['Số điện thoại'] || undefined,
    dob: person['Ngày sinh'] || undefined,
    customFields: [
      personType === TYPE_PERSON['Sinh viên'] &&
        person['Chuyên ngành'] && {
          uuidCustomField: specializationField?.uuidCustomField || '',
          customFieldValue: person['Chuyên ngành']
        },
      personType === TYPE_PERSON['Sinh viên'] &&
        person['Khoa'] && {
          uuidCustomField: schoolField?.uuidCustomField || '',
          customFieldValue: person['Khoa']
        },
      personType !== TYPE_PERSON['Sinh viên'] &&
        person['Đơn vị'] && {
          uuidCustomField: departmentField?.uuidCustomField || '',
          customFieldValue: person['Đơn vị']
        }
    ].filter(Boolean) as CustomField[],
    faces: []
  }
}

export const convertToStatusParams = (person: any): PersonStatus => {
  const id = checkRequiredField(person['ID*'])
  const status = checkRequiredField(person['Trạng thái tài khoản*'])
  const statusKey = UserStatus[status as keyof typeof UserStatus]
  if (statusKey === undefined) {
    throw new Error(ERROR_FROM_USER.INVALID_STATUS)
  }
  return {
    id: id.toLowerCase(),
    status: statusKey
  }
}

export const exportData = ({ data, filename }: { data: Person[]; filename: string }) => {
  if (!data || data.length === 0) {
    console.error('No data to export!')
    return
  }
  // Chuyển đổi dữ liệu thành định dạng phù hợp cho Excel
  const formattedData = data.map((item, index) => {
    const specializationField = item.customFields?.find((field) => field.customFieldName === customFieldPersonList[1])
    const schoolField = item.customFields?.find((field) => field.customFieldName === customFieldPersonList[2])
    const departmentField = item.customFields?.find((field) => field.customFieldName === customFieldPersonList[0])

    const formattedItem: Record<string, string | number | undefined> = {
      STT: index + 1,
      'Họ tên': item.fullName,
      ID: item.personCode,
      Email: item.email,
      'Ngày sinh': item.dob || '',
      'Giới tính': item.gender !== undefined ? Gender[item.gender] : '',
      'Số điện thoại': item.phoneNo || '',
      'Trạng thái tài khoản*': UserStatus[item.status]
    }

    if (filename === getEnumNames(TYPE_PERSON)[0].toLocaleLowerCase()) {
      formattedItem['Khóa'] = item.groupName
      formattedItem['Khoa'] = specializationField ? specializationField.customFieldValue : ''
      formattedItem['Chuyên Ngành'] = schoolField ? schoolField.customFieldValue : ''
    } else {
      formattedItem['Đơn vị'] = departmentField ? departmentField.customFieldValue : ''
    }
    return formattedItem
  })

  // Tạo một worksheet từ dữ liệu
  const worksheet = XLSX.utils.json_to_sheet(formattedData)

  // Tạo một workbook và thêm worksheet vào
  const workbook = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Data')

  // Xuất file Excel
  XLSX.writeFile(workbook, `Xuất excel_DS ${filename}_Quản lý người dùng.xlsx`)
}
export const formatCalendarData = (calendarData: CalendarEvent[]): CalendarEvent[] => {
  const convertByDay = (byday: string = ''): string => {
    return byday
      .split(',')
      .map((day) => reversedDayMap[day] || day)
      .join(', ')
  }
  return calendarData.map((item) => ({
    ...item,
    uuidCalendarEvent: item.uuidCalendarEvent,
    dayOfWeek: convertByDay(item.recurrenceRule?.byday),
    duration: `${dayjs(item.startTime, 'DD/MM/YYYY HH:mm:ss').format('HH:mm')} - ${dayjs(item.endTime, 'DD/MM/YYYY HH:mm:ss').format('HH:mm')}`,
    room: item.location?.name || 'Không có thông tin',
    lecturer: 'Chưa cập nhật',
    timeSlot: `${item.recurrenceRule?.startDate} - ${item.recurrenceRule?.endDate}`
  }))
}

export const formatEventSchedule = (event: CalendarEvent): string => {
  const { startTime, endTime, recurrenceRule } = event
  if (!recurrenceRule || !startTime || !endTime) return ''

  const { byday } = recurrenceRule
  const start = dayjs(startTime, 'MM/DD/YYYY HH:mm:ss')
  const end = dayjs(endTime, 'MM/DD/YYYY HH:mm:ss')

  const timeRange = `(${start.format('HH:mm')}-${end.format('HH:mm')})`

  const formattedDays = byday
    .split(',')
    .map((day) => `${reversedDayMap[day]} ${timeRange}`)
    .join(', ')

  return formattedDays
}

export const normalizeExcelData = (uuidCalendar: string, data: any[], location: any): CalendarEvent[] => {
  return data.map((item) => {
    const room = location.find((loc: any) => loc.name === String(item['Phòng'])?.trim())
    if (!room) {
      throw new Error(ERROR_FROM_USER.INVALID_ROOM)
    }

    const startTime = dayjs(item['Giờ bắt đầu*'], 'HH:mm')
    const endTime = dayjs(item['Giờ kết thúc*'], 'HH:mm')

    if (!startTime.isValid() || !endTime.isValid() || startTime >= endTime) {
      throw new Error(ERROR_FROM_USER.INVALID_TIME)
    }

    return {
      uuidCalendar: uuidCalendar || '',
      startTime: startTime.format('DD/MM/YYYY HH:mm:ss'),
      endTime: endTime.format('DD/MM/YYYY HH:mm:ss'),
      recurrenceRule: {
        byday: (item['Thứ*'] || '')
          .split(',')
          .map((day: string) => dayMap[day] || day)
          .join(','),
        startDate: item['Thời gian bắt đầu*'] || '',
        endDate: item['Thời gian kết thúc*'] || ''
      },
      uuidLocation: room.uuidLocation
    }
  })
}

export const normalizeExcelDataExam = (uuidCalendar: string, data: any[], location: any): CalendarEvent[] => {
  return data.map((item) => {
    // Xử lý ngày thi
    const rawDate = item['Ngày thi*']?.trim() || ''
    const examDate = dayjs(rawDate, ['DD/MM/YYYY', 'DD-MM-YYYY'], true)

    const room = location.find((location: any) => location.label === String(item['Phòng'])?.trim())
    if (!room) {
      throw new Error(ERROR_FROM_USER.INVALID_ROOM)
    }

    const formatTime = (time?: string) => {
      const cleanedTime = time?.trim() || '00:00'
      return dayjs(`${examDate.format('DD/MM/YYYY')} ${cleanedTime}`, 'DD/MM/YYYY HH:mm', true)
    }

    const startTime = formatTime(item['Giờ bắt đầu*'])
    const endTime = formatTime(item['Giờ kết thúc*'])

    if (!examDate.isValid() || startTime >= endTime) {
      throw new Error(ERROR_FROM_USER.INVALID_TIME)
    }
    return {
      uuidCalendar: uuidCalendar || '',
      startTime: startTime.isValid() ? startTime.format('DD/MM/YYYY HH:mm:ss') : '',
      endTime: endTime.isValid() ? endTime.format('DD/MM/YYYY HH:mm:ss') : '',
      uuidLocation: room.value
    }
  })
}

export const mappingStudentWithAttendance = (
  listPerson: Person[],
  listAttendance: AttendanceRecord[]
): StudentCheckIn[] => {
  return listPerson.map((person) => {
    const attendanceRecord =
      listAttendance.find((record) => record.uuidPerson === person.uuidPerson) || ({} as AttendanceRecord)

    return {
      ...person,
      ...attendanceRecord
    }
  })
}

export const mergeAttendanceData = (attendanceList: any, sessionList: any) => {
  return attendanceList.map((attendee: any) => {
    const sessions = sessionList.flat().filter((sessionItem: any) => sessionItem.personId === attendee.personId)

    if (sessions.length > 0) {
      return {
        ...attendee,
        sessions: sessions.map((session: any) => ({
          checkinTime: session.checkinTime,
          uuidSessionAttendance: session.uuidSessionAttendance
        }))
      }
    }

    return attendee
  })
}

export const exportToExcel = (
  data: any,
  classInfo: string = '',
  reportDate: string = new Date().toLocaleDateString()
) => {
  // Lấy danh sách tất cả các session để tạo cột điểm danh
  const allSessions = Array.from(
    new Set(data.flatMap((person: any) => person?.sessions?.map((_: any, index: number) => `Điểm danh ${index + 1}`)))
  )

  // Tạo header cho file Excel
  const headers = ['STT', 'Họ tên', 'ID', 'Xác nhận của giảng viên', ...allSessions]

  // Chuẩn bị dữ liệu xuất Excel
  const excelData = data.map((person: any, index: number) => {
    const row: Record<string, string | number> = {
      STT: index + 1,
      'Họ tên': person.personName,
      ID: person.personId.toUpperCase(),
      'Xác nhận của giảng viên': person.attendanceStatus === 1 ? 'Đi học' : 'Nghỉ học'
    }

    allSessions.forEach((sessionId: any) => {
      const session = person?.sessions?.find((s: any) => s.uuidSessionAttendance === sessionId)
      row[sessionId] = session?.checkinTime || ''
    })

    return row
  })

  const aoaData = [
    ['BÁO CÁO ĐIỂM DANH'],
    [`Ngày: ${reportDate}`],
    [`Lớp: ${classInfo}`],
    headers,
    ...excelData.map(Object.values)
  ]

  const ws = XLSX.utils.aoa_to_sheet(aoaData)

  ws['!merges'] = [
    { s: { r: 0, c: 0 }, e: { r: 0, c: headers.length - 1 } },
    { s: { r: 1, c: 0 }, e: { r: 1, c: headers.length - 1 } },
    { s: { r: 2, c: 0 }, e: { r: 2, c: headers.length - 1 } }
  ]

  const applyCenterAlignToRow = (rowIndex: number) => {
    const row = aoaData[rowIndex]
    row.forEach((_: any, colIndex: number) => {
      const cellAddress = XLSX.utils.encode_cell({ r: rowIndex, c: colIndex })
      const cell = ws[cellAddress]
      if (cell) {
        cell.s = { alignment: { horizontal: 'center', vertical: 'center' } }
      }
    })
  }

  for (let rowIndex = 0; rowIndex < aoaData.length; rowIndex++) {
    applyCenterAlignToRow(rowIndex)
  }

  // Set auto column widths
  const maxWidths = aoaData[4].map((_: any, colIndex: number) => {
    const columnData = aoaData.map((row) => (row[colIndex] ? row[colIndex].toString().length : 0))
    return { wch: Math.max(...columnData) + 2 }
  })

  ws['!cols'] = maxWidths

  // Create the workbook and append the sheet
  const wb = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(wb, ws, 'Báo cáo điểm danh')

  // Write the file
  XLSX.writeFile(wb, 'Báo cáo điểm danh.xlsx')
}
