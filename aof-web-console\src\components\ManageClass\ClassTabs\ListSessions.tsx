import { PlusOutlined } from '@ant-design/icons'
import { Button, DatePicker, Pagination, Select, Spin, Table, TableProps, Tooltip } from 'antd'
import Svg from '../../Svg'
import ModalSelectDate from '../../Modals/SelectDateModal'
import { useCallback, useEffect, useState } from 'react'
import { getListSessions } from '@/services/session'
import { initialProperty, pageSize } from '@/types/components'
import { showCustomNotification } from '@/common/Notification'
import { Session } from '@/types/session'
import dayjs, { Dayjs } from 'dayjs'
import { MESSAGE_STATUS } from '@/constants'
import { ResponseList } from '@/types/common'
import { Creator } from '@/types/person'
import { useNavigate } from 'react-router-dom'
import ModalNotification from '@/components/Modals/NotificationModal'
import useLayoutStore from '@/stores/layout'
import useAuthStore from '@/stores/auth'
import { checkFeature, FF_DEV_0044 } from '@/utils/feature-flags'
import { useSegmentStore } from '@/stores/useSegmentStore'

const { RangePicker } = DatePicker
const ListSessions = ({ uuidGroup, typeName }: { uuidGroup?: string; typeName?: string }) => {
  const navigate = useNavigate()
  const { isMobile } = useLayoutStore()
  const { user, featFlagsAction } = useAuthStore()
  const isFFA = (code: string) => checkFeature(code, user, featFlagsAction)

  const { session, setSession } = useSegmentStore()
  const defaultRange: [Dayjs, Dayjs] = [dayjs().startOf('month'), dayjs()]
  const [pickedDates, setPickedDates] = useState<[Dayjs | null, Dayjs | null]>(defaultRange)
  const [dataSource, setDataSource] = useState<ResponseList<Session>>()
  const [pageProperty, setPageProperty] = useState<{
    page: number
    maxSize: number
    totalElement: number
  }>(initialProperty)

  const [openSelectModal, setOpenSelectModal] = useState<boolean>(false)
  const [isDuplicate, setIsDuplicate] = useState<boolean>(false)
  const [isLoading, setIsLoading] = useState<boolean>(false)

  const getData = useCallback(async () => {
    try {
      if (!uuidGroup) return
      setIsLoading(true)
      const res = await getListSessions({
        ...pageProperty,
        uuidGroup: uuidGroup,
        startTime: dayjs(pickedDates[0]).format('DD/MM/YYYY'),
        endTime: dayjs(pickedDates[1]).format('DD/MM/YYYY')
      })

      if (res.message === MESSAGE_STATUS.SUCCESS) {
        setDataSource(res.object)
      }
    } catch {
      showCustomNotification({
        status: 'error',
        message: 'Thất bại!',
        description: 'Lấy danh sách điểm danh thất bại!'
      })
    } finally {
      setIsLoading(false)
    }
  }, [pageProperty, uuidGroup, pickedDates])

  useEffect(() => {
    getData()
  }, [getData])

  const handleRedirect = (time: string, uuidSession?: string) => {
    navigate(`/manage-class/${uuidGroup}/checkin`, {
      state: { sessionDate: time, uuidSession: uuidSession, typeName: typeName }
    })
  }

  const handleNavigate = (record: Session) => {
    setSession({
      ...session,
      sessionDate: record.sessionDate,
      uuidSession: record.uuidSession
    })
    navigate(`/manage-class/${uuidGroup}/session-attendance/${record.uuidSession}`, {
      state: { sessionDate: record.sessionDate, ordinalNoInDate: record.ordinalNoInDate }
    })
  }

  const handleOk = (data: string) => {
    const isDuplicate = dataSource?.data.some((record) => record.sessionDate === data)
    setSession({
      ...session,
      sessionDate: data,
      uuidSession: isDuplicate ? dataSource?.data.find((record) => record.sessionDate === data)?.uuidSession : undefined
    })
    if (isDuplicate) {
      setOpenSelectModal(false)
      setIsDuplicate(true)
      return
    }
    handleRedirect(data)
  }

  // const items = (record: Session): MenuProps['items'] => [
  //   {
  //     key: 1,
  //     label: (
  //       <div className='flex items-center gap-2' onClick={() => handleNavigate(record)}>
  //         <Svg src='/assets/icons/common/eye.svg' className='h-5 w-5 text-neutral-4' />
  //         Xem
  //       </div>
  //     )
  //   },
  //   {
  //     key: 2,
  //     label: (
  //       <div className='flex items-center gap-2' onClick={() => handleNavigate(record)}>
  //         <Svg src='/assets/icons/common/edit.svg' className='h-5 w-5' />
  //         Sửa
  //       </div>
  //     )
  //   }
  // ]

  const columns: TableProps<Session>['columns'] = [
    {
      title: 'STT',
      dataIndex: 'key',
      key: 'key',
      width: 48,
      render: (_, __, index) => (dataSource ? (dataSource.page - 1) * dataSource.maxSize + (index + 1) : '')
    },
    {
      title: 'Bản điểm danh',
      dataIndex: 'sessionDate',
      key: 'sessionDate',
      render: (value, record) => (
        <p onClick={() => handleNavigate(record)} className='hover:cursor-pointer hover:underline hover:text-text-3'>
          {`${value} (${record.sessionAttendanceCount})`}
        </p>
      )
    },
    {
      title: 'Người tạo',
      dataIndex: 'creator',
      key: 'creator',
      render: (value: Creator) => value.fullName + ' - ' + value.personCode
    },
    {
      title: 'Xác nhận',
      dataIndex: 'sessionAttendanceCount',
      key: 'sessionAttendanceCount',
      render: (value, record) =>
        value === record.confirmedSessionAttendanceCount ? (
          <Tooltip title={record.confirmedSessionAttendanceCount + '/' + value}>
            <Svg src='/assets/icons/status/done.svg' className='h-5 w-5' />
          </Tooltip>
        ) : (
          <Tooltip title={record.confirmedSessionAttendanceCount + '/' + value}>
            <Svg src='/assets/icons/status/warning.svg' className='h-5 w-5' />
          </Tooltip>
        )
    },
    {
      title: 'Sĩ số điểm danh',
      dataIndex: 'presentStudentCount',
      key: 'presentStudentCount',
      render: (value, record) => value + '/' + record.studentCount
    }
    // {
    //   title: 'Thao tác',
    //   width: 115,
    //   key: 'action',
    //   align: 'center',
    //   render: (_, record) => (
    //     <Dropdown menu={{ items: items(record) }} trigger={['click']} placement='bottomRight'>
    //       <a onClick={(e) => e.preventDefault()}>
    //         <MoreOutlined />
    //       </a>
    //     </Dropdown>
    //   )
    // }
  ]
  return (
    <>
      <ModalNotification
        handleCancel={() => setIsDuplicate(false)}
        isModalOpen={isDuplicate}
        handleOk={() => handleRedirect(session.sessionDate, session.uuidSession)}
        title='Thông báo!'
        subTitle={`Đã tồn tại bản ghi điểm danh ngày ${session.sessionDate}, bạn muốn sửa bản ghi cũ hay tiếp tục thêm mới`}
      />
      <ModalSelectDate
        isModalOpen={openSelectModal}
        handleOk={handleOk}
        handleCancel={() => setOpenSelectModal(false)}
      />
      <Spin spinning={isLoading}>
        <div className='flex justify-between items-end gap-5 mb-5'>
          <div className='flex flex-col'>
            <label className='mb-0 min-w-[220px] w-full'>Chọn thời gian</label>
            <RangePicker
              size='large'
              className='border-neutral-7'
              value={pickedDates}
              format={'DD/MM/YYYY'}
              popupClassName={isMobile ? 'custom-range-picker' : undefined}
              suffixIcon={<Svg src='/assets/icons/common/datepicker.svg' className='h-4 w-4' />}
              onChange={(dates) => setPickedDates(dates || [dayjs().startOf('month'), dayjs()])}
            />
          </div>
          {isFFA(FF_DEV_0044) && (
            <Button
              size='large'
              icon={<PlusOutlined />}
              color='primary'
              variant='outlined'
              onClick={() => setOpenSelectModal(true)}
            >
              <span className='hidden xs:block'>Thêm mới</span>
            </Button>
          )}
        </div>
        <Table
          columns={columns}
          dataSource={dataSource?.data}
          pagination={false}
          rowKey={(record) => record.uuidSession}
          scroll={{ x: isMobile ? 'max-content' : 'auto' }}
        />
        {dataSource?.data && dataSource?.data.length > 0 && (
          <div className='mt-4 flex max-sm:flex-col items-center justify-between gap-4'>
            <div className='flex items-center gap-2'>
              <p className='text-xs font-normal text-gray-400'>Số bản ghi mỗi trang </p>
              <Select
                size='small'
                options={pageSize}
                value={pageProperty.maxSize}
                onChange={(e) => setPageProperty({ ...pageProperty, maxSize: e, page: 1 })}
              />
              <p className='text-xs font-normal text-gray-400 hidden sm:block'>
                trên tổng {dataSource.totalElement} dữ liệu{' '}
              </p>
            </div>
            <Pagination
              onChange={(page) => {
                setPageProperty({ ...pageProperty, page })
              }}
              className='mt-4'
              align='end'
              current={dataSource.page}
              defaultCurrent={1}
              showSizeChanger={false}
              pageSize={dataSource.maxSize}
              defaultPageSize={10}
              total={dataSource.totalElement}
            />
          </div>
        )}
      </Spin>
    </>
  )
}

export default ListSessions
