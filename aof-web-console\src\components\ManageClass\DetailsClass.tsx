import GeneralInformation from '@/components/ManageClass/ClassTabs/GeneralInfo'
import ListSessions from '@/components/ManageClass/ClassTabs/ListSessions'
import ListPersons from '@/components/ManageClass/ClassTabs/ListPersons'
import Schedule from '@/components/ManageClass/ClassTabs/Schedule'
import LayoutCotent from '@/layouts/LayoutCotent'
import { classInfoList, ClassType, TYPE_CLASS_CREDIT, TYPE_CLASS_EXAM, TYPE_PERSON } from '@/types/components'
import { Segmented, Spin } from 'antd'
import { useEffect, useState } from 'react'
import { useLocation, useNavigate, useParams } from 'react-router-dom'
import { getDetailsGroup } from '@/services/group'
import { Group } from '@/types/group'
import { MESSAGE_STATUS } from '@/constants'
import { getCalendarEvent } from '@/services/calendar'
import { CalendarEvent } from '@/types/calendar'
import ModalError from '../Modals/ErrorLoadingModal'
import { formatCalendarData } from '@/helpers/index'
import ScheduleExam from './ClassTabs/ScheduleExam'
import { useSegmentStore } from '@/stores/useSegmentStore'
import useGroupStore from '@/stores/group'
import { checkFeature, FF_DEV_0014, FF_DEV_0015 } from '@/utils/feature-flags'
import useAuthStore from '@/stores/auth'

const DetailsClass = () => {
  const navigate = useNavigate()
  const { state } = useLocation()
  const { uuidGroup } = useParams()
  const { user, featFlagsChild } = useAuthStore()

  const { activeClassDetail, setActiveClassDetail } = useSegmentStore()
  const { groupName, setGroupName } = useGroupStore()

  const [classData, setClassData] = useState<Group>({} as Group)
  const [typeName, setTypeName] = useState<ClassType>('Lớp học')
  const [canlendarData, setCalendarData] = useState<CalendarEvent[]>([])

  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [openErrorModal, setOpenErrorModal] = useState<boolean>(false)

  const isFFC = (code: string) => checkFeature(code, user, featFlagsChild)

  useEffect(() => {
    getDetails()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const getDetails = async () => {
    try {
      if (!uuidGroup) return

      setIsLoading(true)
      const res = await getDetailsGroup(uuidGroup)
      if (res.message === MESSAGE_STATUS.SUCCESS) {
        setClassData(res.object)
        setGroupName(res.object.groupName)
        if (res.object.groupType === TYPE_CLASS_CREDIT) {
          setTypeName('Lớp học')
        } else if (res.object.groupType === TYPE_CLASS_EXAM) {
          setTypeName('Lớp thi')
        }
        if (res.object.uuidCalendar) {
          const resCalendar = await getCalendarEvent(res.object.uuidCalendar)
          setCalendarData(resCalendar.object)
        }
      }
      setIsLoading(false)
    } catch {
      setOpenErrorModal(true)
    }
  }

  const view = () => {
    switch (activeClassDetail) {
      case 'Thông tin chung':
        return (
          <GeneralInformation
            typeName={typeName}
            isUpdate={state}
            classData={classData}
            calendarData={formatCalendarData(canlendarData)}
            setIsLoading={setIsLoading}
            refreshData={getDetails}
          />
        )
      case 'Thời khóa biểu':
        return (
          <Schedule
            lecturers={classData.lecturers}
            groupName={groupName}
            uuidCalendar={classData.uuidCalendar}
            calendarData={formatCalendarData(canlendarData)}
            refreshData={getDetails}
          />
        )
      case 'Lịch thi':
        return (
          <ScheduleExam
            lecturers={classData.lecturers}
            groupName={groupName}
            uuidCalendar={classData.uuidCalendar}
            calendarData={formatCalendarData(canlendarData)}
            refreshData={getDetails}
          />
        )
      case 'Danh sách sinh viên':
        return <ListPersons uuidGroup={uuidGroup} personType={TYPE_PERSON['Sinh viên']} />
      case 'Giảng viên':
      case 'Cán bộ trông thi':
        return <ListPersons uuidGroup={uuidGroup} typeName={typeName} personType={TYPE_PERSON['Giảng viên']} />
      case 'Điểm danh':
        return <ListSessions uuidGroup={uuidGroup} typeName={typeName} />
      default:
        return (
          <GeneralInformation
            typeName={typeName}
            isUpdate={state}
            classData={classData}
            calendarData={formatCalendarData(canlendarData)}
            setIsLoading={setIsLoading}
            refreshData={getDetails}
          />
        )
    }
  }

  const list = classInfoList.filter((item) => {
    if (item === 'Điểm danh') {
      return isFFC(FF_DEV_0015)
    }

    const group = ['Thông tin chung', 'Danh sách sinh viên']
    if (group.includes(item)) {
      return isFFC(FF_DEV_0014)
    }

    const groupExam = ['Lịch thi', 'Cán bộ trông thi']
    if (groupExam.includes(item)) {
      return isFFC(FF_DEV_0014) && typeName === 'Lớp thi'
    }

    const groupLearn = ['Thời khóa biểu', 'Giảng viên']
    if (groupLearn.includes(item)) {
      return isFFC(FF_DEV_0014) && typeName === 'Lớp học'
    }
    return false
  })

  return (
    <>
      <ModalError
        isModalOpen={openErrorModal}
        handleCancel={() => {
          navigate('/manage-class')
          setOpenErrorModal(false)
        }}
      />
      <LayoutCotent title={['Quản lý lớp', typeName, groupName]} btnBack={true}>
        <Spin spinning={isLoading}>
          <div className='overflow-x-auto' style={{ scrollbarWidth: 'none' }}>
            <Segmented
              value={activeClassDetail}
              onChange={setActiveClassDetail}
              size='large'
              options={list}
              className='mb-5 border'
            />
          </div>
          {view()}
        </Spin>
      </LayoutCotent>
    </>
  )
}

export default DetailsClass
