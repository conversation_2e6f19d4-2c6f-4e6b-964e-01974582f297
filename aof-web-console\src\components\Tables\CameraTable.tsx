import { showCustomNotification } from '@/common/Notification'
import { MESSAGE_STATUS } from '@/constants'
import { addCamera, deleteCamera, getListCameras, updateCamera } from '@/services/room_device'
import { ResponseList } from '@/types/common'
import { pageSize } from '@/types/components'
import { Camera, Location } from '@/types/room_device'
import { Dropdown, MenuProps, Pagination, Select, Table, TableProps } from 'antd'
import { useEffect, useState } from 'react'
// import { DeviceStatusField } from '../StatusField'
import { MoreOutlined } from '@ant-design/icons'
import Svg from '../Svg'
import useLayoutStore from '@/stores/layout'
import CameraModal from '../Modals/CameraModal'
import useAuthStore from '@/stores/auth'
import { checkFeature, FF_DEV_0038, FF_DEV_0047 } from '@/utils/feature-flags'
import { useDeleteItem } from '@/hooks/useDeleteItem'
import ModalDelete from '../Modals/DeleteModal'

interface Props {
  params: any
  openCameraModal: boolean
  setIsLoading: (e: boolean) => void
  setOpenCameraModal: (e: boolean) => void
  setParams: (e: any) => void
}

const initialData = { uuid: '', cameraName: '', videoStreamUrl: '' }

const CameraTable = ({ params, openCameraModal, setOpenCameraModal, setIsLoading, setParams }: Props) => {
  const { isMobile } = useLayoutStore()
  const [dataUpdate, setDataUpdate] = useState(initialData)
  const [dataSource, setDataSource] = useState<ResponseList<Camera>>()
  const { user, featFlagsAction } = useAuthStore()
  const isFFA = (code: string) => checkFeature(code, user, featFlagsAction)

  const { loading, openDeleteModal, setOpenDeleteModal, handleDelete } = useDeleteItem(
    deleteCamera,
    `Xóa camera thành công!`,
    `Xóa camera thất bại!`
  )

  const [openModal, setOpenModal] = useState<boolean>(false)

  useEffect(() => {
    getData()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [params])

  const getData = async () => {
    try {
      setIsLoading(true)
      const res = await getListCameras(params)

      if (res.message === MESSAGE_STATUS.SUCCESS) {
        setDataSource(res.object)
      }
    } catch {
      showCustomNotification({
        status: 'error',
        message: 'Thất bại',
        description: 'Lỗi khi lấy danh sách!'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleUpdate = async (data: any) => {
    setIsLoading(true)
    try {
      let res
      if (openCameraModal) {
        res = await addCamera({
          videoStreamUrl: data.videoStreamUrl,
          cameraName: data.cameraName,
          uuidLocation: data.uuidLocation
        })
      } else {
        res = await updateCamera(dataUpdate.uuid, {
          videoStreamUrl: data.videoStreamUrl,
          cameraName: data.cameraName,
          uuidLocation: data.uuidLocation
        })
      }

      if (res.message === MESSAGE_STATUS.SUCCESS) {
        showCustomNotification({
          status: 'success',
          message: 'Thành công',
          description: openCameraModal ? 'Thêm mới camera thành công!' : 'Cập nhật camera thành công!'
        })
        setDataUpdate(initialData)
        getData()
      }
    } catch {
      showCustomNotification({ status: 'error', message: 'Thất bại', description: 'Có lỗi xảy ra!' })
    } finally {
      setIsLoading(false)
      setOpenModal(false)
    }
  }

  const items = (record: Camera): MenuProps['items'] => [
    ...(isFFA(FF_DEV_0038)
      ? [
          {
            key: 1,
            label: (
              <div
                className='flex items-center gap-2'
                onClick={() => {
                  setOpenModal(true)
                  setDataUpdate((pre) => ({
                    ...pre,
                    videoStreamUrl: record.videoStreamUrl,
                    cameraName: record.cameraName,
                    uuidLocation: record.location.uuidLocation
                  }))
                }}
              >
                <Svg src='/assets/icons/common/edit.svg' className='h-5 w-5' />
                Sửa
              </div>
            )
          }
        ]
      : []),
    ...(isFFA(FF_DEV_0047)
      ? [
          {
            key: 2,
            label: (
              <div
                className='flex items-center gap-2'
                onClick={() => {
                  setOpenDeleteModal(true)
                }}
              >
                <Svg src='/assets/icons/common/trash.svg' className='h-5 w-5 text-neutral-4' />
                Xóa
              </div>
            )
          }
        ]
      : [])
  ]

  const columns: TableProps<Camera>['columns'] = [
    {
      title: 'STT',
      dataIndex: 'key',
      key: 'key',
      align: 'center',
      width: 48,
      render: (_, __, index) => (dataSource ? (dataSource.page - 1) * dataSource.maxSize + (index + 1) : '')
    },
    { title: 'Tên camera', dataIndex: 'cameraName', key: 'cameraName' },
    ...(!isMobile
      ? [
          // { title: 'Mã thiết bị', dataIndex: 'cameraIndexCode', key: 'cameraIndexCode' },
          { title: 'Tên phòng', dataIndex: 'location', key: 'location', render: (e: Location) => e.name }
        ]
      : []),
    { title: 'Link RTSP', dataIndex: 'videoStreamUrl', key: 'videoStreamUrl' },
    // {
    //   title: 'Trạng thái',
    //   dataIndex: 'status',
    //   key: 'status',
    //   render: (status: number) => <DeviceStatusField status={status} />
    // },
    ...(isFFA(FF_DEV_0038) || isFFA(FF_DEV_0047)
      ? [
          {
            title: 'Thao tác',
            width: isMobile ? 60 : 80,
            key: 'action',
            render: (_: any, record: Camera) => (
              <Dropdown menu={{ items: items(record) }} trigger={['click']} placement='bottomRight'>
                <a
                  onClick={(e) => {
                    e.preventDefault()
                    setDataUpdate((pre) => ({ ...pre, uuid: record.cameraIndexCode }))
                  }}
                  className='flex justify-center'
                >
                  <MoreOutlined />
                </a>
              </Dropdown>
            )
          }
        ]
      : [])
  ]

  return (
    <>
      <ModalDelete
        isLoading={loading}
        isModalOpen={openDeleteModal}
        handleCancel={() => {
          setOpenDeleteModal(false)
        }}
        title='Xóa'
        handleOk={() => handleDelete(dataUpdate.uuid, getData)}
        subTitle='Bạn có chắc chắn muốn xóa camera này?'
      />
      <CameraModal
        dataUpdate={dataUpdate}
        isUpdate={!openCameraModal}
        handleOk={handleUpdate}
        isModalOpen={openModal || openCameraModal}
        handleCancel={() => {
          setOpenModal(false)
          setOpenCameraModal(false)
        }}
      />
      <Table
        columns={columns}
        pagination={false}
        dataSource={dataSource?.data}
        rowKey={(record) => record.cameraIndexCode}
        scroll={{ x: 'max-content' }}
      />
      {dataSource?.data && dataSource.data.length > 0 && (
        <div className='my-4 flex max-sm:flex-col max-sm:items-center items-center justify-between gap-4'>
          <div className='flex items-center gap-2'>
            <p className='text-xs font-normal text-gray-400'>Số bản ghi mỗi trang </p>
            <Select
              size='small'
              options={pageSize}
              value={params.maxSize}
              onChange={(e) => setParams({ ...params, maxSize: e })}
            />
            <p className='text-xs font-normal text-gray-400 hidden sm:block'>
              trên tổng {dataSource.totalElement} dữ liệu{' '}
            </p>
          </div>
          <Pagination
            onChange={(page) => setParams((prev: any) => ({ ...prev, page }))}
            align='end'
            current={dataSource.page}
            defaultCurrent={1}
            showSizeChanger={false}
            pageSize={dataSource.maxSize}
            defaultPageSize={10}
            total={dataSource.totalElement}
          />
        </div>
      )}
    </>
  )
}

export default CameraTable
