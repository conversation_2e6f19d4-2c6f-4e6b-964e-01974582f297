@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700&family=Open+Sans:wght@300;400;500;600;700&family=Roboto:wght@100;300;400;500;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

*,
*:before,
*:after {
  box-sizing: inherit;
  font-family: 'Inter';
}

body,
body {
  background-color: #ffffff;
  color: #282d57;
}

h1,
h2,
h3,
h4,
h5,
h6,
p,
ol,
ul {
  margin: 0;
  padding: 0;
  font-weight: normal;
}

ol,
ul {
  list-style: none;
}

img {
  max-width: 100%;
  height: auto;
}

.ant-btn {
  box-shadow: none;
}

.ant-menu-item-selected {
  background-color: #e5f6f8 !important;
  color: #0d5b63 !important;
  font-size: '16px' !important;
  font-weight: 500;
  line-height: '24px' !important;
}

.ant-form-item-label {
  padding: 0 !important;
}

.ant-table-cell {
  padding: 8px 8px !important;
}

.ant-table-body {
  scrollbar-width: auto;
  scrollbar-color: auto;
}

tr:nth-child(odd) {
  background: #ffffff;
}

tr:nth-child(even) {
  background: #f7fafb;
}

.ant-table-row-selected {
  background-color: #d3ecee !important;
}

.ant-table-body::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.ant-table-body::-webkit-scrollbar-track {
  background-color: #e1e6e6;
}

.ant-table-body::-webkit-scrollbar-thumb {
  border-radius: 6px;
  background-color: #0d5b63;
}

.ant-drawer-body {
  padding: 12px !important;
}

.ant-drawer-header {
  padding: 12px !important;
}

.icon-segmented .ant-segmented-item:last-child {
  min-width: auto !important;
}

.ant-segmented-item {
  min-width: 150px;
}

.ant-segmented .ant-segmented-item-selected {
  border: 1px solid #00707e;
  color: #0d5b63;
  font-size: 0.875rem;
}

.customer-model .ant-modal-content {
  padding: 0 !important;
  border-radius: 1rem !important;
}

.customer-model .ant-modal-header {
  border-radius: 1rem !important;
}

.custom-range-picker .ant-picker-panel-container {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  max-width: 90vw;
  overflow-x: auto;
}

.ant-dropdown-menu {
  max-height: calc(100vh - 100px) !important;
  overflow-y: auto !important;
  scrollbar-width: thin;
  scrollbar-color: #0d5b63 #e1e6e6;
  scrollbar-arrow-color: #ffffff;
}

.ant-dropdown-menu::-webkit-scrollbar-thumb {
  background-color: #0d5b63;
}
