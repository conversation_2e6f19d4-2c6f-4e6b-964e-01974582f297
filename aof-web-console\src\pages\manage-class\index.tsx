import { showCustomNotification } from '@/common/Notification'
import ModalDelete from '@/components/Modals/DeleteModal'
import { ClassStatusField } from '@/components/StatusField'
import Svg from '@/components/Svg'
import { MESSAGE_STATUS, ROLE_TITLE } from '@/constants'
import { formatEventSchedule } from '@/helpers'
import { useDeleteItem } from '@/hooks/useDeleteItem'
import LayoutCotent from '@/layouts/LayoutCotent'
import { deleteGroup, getListGroups } from '@/services/group'
import { getListPersons } from '@/services/person'
import useAuthStore from '@/stores/auth'
import { useInfoStore } from '@/stores/info'
import useLayoutStore from '@/stores/layout'
import { useSegmentStore } from '@/stores/useSegmentStore'
import { CalendarEvent } from '@/types/calendar'
import { CustomField, ResponseList } from '@/types/common'
import {
  classList,
  initialProperty,
  statusCreditClass,
  statusExamClass,
  // MAX_SIZE_PAGE,
  TYPE_PERSON,
  pageSize
} from '@/types/components'
import { Group } from '@/types/group'
import { Semester } from '@/types/infomation'
import { Person } from '@/types/person'
import { Lecturer } from '@/types/user'
import { checkFeature, FF_DEV_0040, FF_DEV_0041, FF_DEV_0042 } from '@/utils/feature-flags'
import { MoreOutlined, PlusOutlined } from '@ant-design/icons'
import { Button, Dropdown, Input, MenuProps, Pagination, Segmented, Select, Spin, Table, TableProps } from 'antd'
import dayjs from 'dayjs'
import { useEffect, useState } from 'react'
import { Link, useNavigate } from 'react-router-dom'

const initialValue = {
  keySearch: '',
  uuidSemester: '',
  uuidLecturer: '',
  progressStatus: -1
}

const ManageClass = () => {
  const navigate = useNavigate()

  const { isMobile, isTablet } = useLayoutStore()
  const { activeClass, groupType, setActiveClass, setActiveClassDetail } = useSegmentStore()
  const { loadingData, academicYears, semesters, loadAcademicYears, loadSemesters } = useInfoStore()
  const { user, featFlagsAction } = useAuthStore()
  const isFFA = (code: string) => checkFeature(code, user, featFlagsAction)

  const [uuid, setUuid] = useState<string>('')
  const [selectYear, setSelectYear] = useState<string>('')
  const [dataSource, setDataSource] = useState<ResponseList<Group>>()
  const [selectedRows, setSelectedRows] = useState<Group[]>([])
  const [listPersons, setListPersons] = useState<Person[]>([])
  const [searchData, setSearchData] = useState(initialValue)

  const { loading, openDeleteModal, setOpenDeleteModal, handleDelete } = useDeleteItem(
    deleteGroup,
    `Xóa ${activeClass} thành công!`,
    `Xóa ${activeClass} thất bại!`
  )

  const [pageProperty, setPageProperty] = useState<{
    page: number
    maxSize: number
    totalElement: number
  }>(initialProperty)

  const [onAction, setOnAction] = useState<boolean>(false)
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [isLoadingPerson, setIsLoadingPerson] = useState<boolean>(false)
  const [hasMore, setHasMore] = useState<boolean>(true)
  const [page, setPage] = useState(1)
  const [openDeleteMultipleModal, setOpenDeleteMultipleModal] = useState<boolean>(false)

  useEffect(() => {
    setPageProperty(initialProperty)
    setSearchData(initialValue)
  }, [activeClass])

  useEffect(() => {
    if (academicYears.length === 0) {
      loadAcademicYears()
    } else {
      const current = dayjs()
      const currentYear = current.month() < 6 ? current.year() - 1 : current.year()
      const year = academicYears.find((item) => item.startYear === currentYear)
      setSelectYear(year?.uuidAcademicYear || academicYears[0].uuidAcademicYear)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [academicYears])

  useEffect(() => {
    if (selectYear) {
      getData()
      loadSemesters(selectYear)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [groupType, selectYear, searchData.uuidSemester, searchData.uuidLecturer, searchData.progressStatus, pageProperty])

  const getData = async () => {
    try {
      setIsLoading(true)
      const res = await getListGroups({
        page: pageProperty.page,
        maxSize: pageProperty.maxSize,
        countStudent: 1,
        keySearch: searchData.keySearch,
        groupType: groupType,
        progressStatus: searchData.progressStatus,
        uuidAcademicYear: selectYear,
        uuidSemester: searchData.uuidSemester,
        uuidLecturer: searchData.uuidLecturer
      })
      if (res.message === MESSAGE_STATUS.SUCCESS) {
        setDataSource(res.object)
      }
    } catch {
      showCustomNotification({
        status: 'error',
        message: 'Thất bại',
        description: 'Lấy dữ liệu thất bại!'
      })
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    const fetchPersons = async () => {
      setIsLoadingPerson(true)
      const res = await getListPersons({
        page,
        maxSize: 10,
        personTypes:
          activeClass === 'Lớp học'
            ? String(TYPE_PERSON['Giảng viên'])
            : `${TYPE_PERSON['Giảng viên']}, ${TYPE_PERSON['Nhân viên chức năng']}`
      })
      const newData = res.object.data || []
      setListPersons((prev) => [...prev, ...newData])
      setHasMore(newData.length > 0)
      setIsLoadingPerson(false)
    }
    if (user.roles?.[0].name !== ROLE_TITLE.USER) {
      fetchPersons()
    }
  }, [activeClass, page, user.roles])

  const handleRemoveMultipleClass = async () => {
    try {
      setOnAction(true)
      await Promise.all(selectedRows.map((group) => deleteGroup(group.uuidGroup)))
      showCustomNotification({
        status: 'success',
        message: 'Xóa thành công!',
        description: `Xóa ${selectedRows.length} ${activeClass.toLocaleLowerCase()} thành công!`
      })
      setSelectedRows([])
      getData()
    } catch {
      showCustomNotification({
        status: 'error',
        message: 'Thất bại!',
        description: `Xóa ${selectedRows.length} ${activeClass.toLocaleLowerCase()} thất bại!`
      })
    } finally {
      setOnAction(false)
      setOpenDeleteMultipleModal(false)
    }
  }

  const handlePopupScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const target = e.currentTarget
    if (target.scrollTop + target.offsetHeight >= target.scrollHeight - 10 && !loading && hasMore) {
      setPage((prev) => prev + 1)
    }
  }

  const rowSelection = {
    selectedRows,
    onChange: (_: React.Key[], selectedRows: Group[]) => {
      setSelectedRows(selectedRows)
    }
  }

  const columns: TableProps<Group>['columns'] = [
    {
      title: 'STT',
      align: 'center',
      width: 60,
      render: (_, __, index) => dataSource && (dataSource.page - 1) * dataSource.maxSize + (index + 1)
    },
    {
      title: 'Tên lớp',
      dataIndex: 'groupName',
      key: 'groupName',
      width: isMobile || isTablet ? 130 : 250,
      render: (value, record) => (
        <Link
          to={`/manage-class/${record.uuidGroup}`}
          onClick={() => setActiveClassDetail('Thông tin chung')}
          className='hover:underline'
        >
          <p className='line-clamp-2' title={value}>
            {value}
          </p>
        </Link>
      )
    },
    {
      title: 'Mã lớp',
      dataIndex: 'groupCode',
      key: 'groupCode',
      width: 120
    },
    {
      title: 'Kỳ học',
      dataIndex: 'semester',
      key: 'semester',
      width: 80,
      render: (value: Semester) => value.name
    },
    ...(activeClass === 'Lớp học'
      ? [
          {
            title: (
              <p className='font-semibold' title='Số tín chỉ'>
                Số TC
              </p>
            ),
            dataIndex: 'customFields',
            key: 'customFields',
            width: 70,
            render: (value: CustomField[]) => value[0]?.customFieldValue
          },
          {
            title: <p className='flex flex-col font-semibold'>Số tiết</p>,
            dataIndex: 'customFields',
            key: 'customFields',
            width: 70,
            render: (value: CustomField[]) => value[1]?.customFieldValue
          }
        ]
      : []),
    ...(activeClass === 'Lớp thi'
      ? [
          {
            title: 'Cán bộ trông thi',
            dataIndex: 'teachers',
            key: 'teachers',
            width: 200,
            render: (_: any, record: Group) => {
              if (!record?.lecturers?.length)
                return <span className='text-gray-400 italic'>(Không có người trông thi)</span>
              const visible = record?.lecturers.slice(0, 2)
              const hidden = record?.lecturers.slice(2)

              return (
                <div className='flex flex-col truncate'>
                  {visible.map(({ fullName, personCode }) => (
                    <span key={personCode} title={`${fullName} - ${personCode}`} className='truncate text-sm'>
                      {`${fullName} - ${personCode.toUpperCase()}`}
                    </span>
                  ))}
                  <div>
                    {hidden.length > 0 && (
                      <Dropdown
                        menu={{
                          items: hidden.map(({ fullName, personCode }) => ({
                            key: personCode,
                            label: `${fullName} - ${personCode.toUpperCase()}`
                          }))
                        }}
                      >
                        <p className='cursor-pointer text-text-primary text-sm border text-center rounded-2xl'>
                          +{hidden.length} khác
                        </p>
                      </Dropdown>
                    )}
                  </div>
                </div>
              )
            }
          }
        ]
      : []),
    {
      title: 'Thời gian',
      dataIndex: 'time',
      key: 'time',
      width: 190,
      render: (_, record) => (
        <p className='line-clamp-2'>
          <span className='whitespace-nowrap'>{record.startDate} -</span> <span>{record.endDate}</span>
        </p>
      )
    },
    ...(activeClass === 'Lớp học'
      ? [
          {
            title: 'Ngày học',
            dataIndex: 'calendarEvents',
            key: 'calendarEvents',
            width: 180,
            render: (events: CalendarEvent[]) => (
              <p className='line-clamp-2' title={events?.map((event) => formatEventSchedule(event)).join(', ')}>
                {events?.map((event) => formatEventSchedule(event)).join(', ')}
              </p>
            )
          }
        ]
      : []),
    ...(activeClass === 'Lớp thi'
      ? [
          {
            title: 'Giờ thi',
            dataIndex: 'calendarEvents',
            key: 'calendarEvents',
            width: 180,
            render: (events: CalendarEvent[]) => {
              return events ? (
                <p
                  className='line-clamp-2'
                  title={`${dayjs(events?.[0].startTime, 'DD/MM/YYYY HH:mm:ss').format('HH:mm')} - ${dayjs(events?.[0].endTime, 'DD/MM/YYYY HH:mm:ss').format('HH:mm')}`}
                >
                  {`${dayjs(events?.[0].startTime, 'DD/MM/YYYY HH:mm:ss').format('HH:mm')} - ${dayjs(events?.[0].endTime, 'DD/MM/YYYY HH:mm:ss').format('HH:mm')}`}
                </p>
              ) : (
                <span className='text-gray-2 italic'>(Không có lịch thi)</span>
              )
            }
          }
        ]
      : []),
    {
      title: (
        <p className='font-semibold' title='Số sinh viên'>
          Số SV
        </p>
      ),
      dataIndex: 'studentCount',
      key: 'studentCount',
      width: 70
    },
    ...(activeClass === 'Lớp học'
      ? [
          {
            title: 'Giảng viên',
            dataIndex: 'lecturers',
            key: 'lecturers',
            width: 200,
            render: (lecturers: Lecturer[]) => {
              if (!lecturers?.length) return <span className='text-gray-400 italic'>(Không có giảng viên)</span>
              const visible = lecturers.slice(0, 2)
              const hidden = lecturers.slice(2)

              return (
                <div className='flex flex-col truncate'>
                  {visible.map(({ fullName, personCode }) => (
                    <span key={personCode} title={`${fullName} - ${personCode}`} className='truncate text-sm'>
                      {`${fullName} - ${personCode.toUpperCase()}`}
                    </span>
                  ))}
                  <div>
                    {hidden.length > 0 && (
                      <Dropdown
                        menu={{
                          items: hidden.map(({ fullName, personCode }) => ({
                            key: personCode,
                            label: `${fullName} - ${personCode.toUpperCase()}`
                          }))
                        }}
                      >
                        <p className='cursor-pointer text-text-primary text-sm border text-center rounded-2xl'>
                          +{hidden.length} khác
                        </p>
                      </Dropdown>
                    )}
                  </div>
                </div>
              )
            }
          }
        ]
      : []),
    {
      title: 'Trạng thái',
      dataIndex: 'progressStatus',
      key: 'progressStatus',
      width: 120,
      render: (progressStatus) => <ClassStatusField status={progressStatus} exam={activeClass === 'Lớp học'} />
    },
    ...(isFFA(FF_DEV_0041) || isFFA(FF_DEV_0042) || user.roles?.[0].name === ROLE_TITLE.ADMIN
      ? [
          {
            title: 'Thao tác',
            width: 80,
            key: 'action',
            render: (_: any, record: Group) => (
              <Dropdown menu={{ items: items(record) }} trigger={['click']} placement='bottomRight'>
                <a
                  onClick={(e) => {
                    e.preventDefault()
                    setUuid(record.uuidGroup || '')
                  }}
                  className='flex justify-center'
                >
                  <MoreOutlined />
                </a>
              </Dropdown>
            )
          }
        ]
      : [])
  ]

  const items = (record: Group): MenuProps['items'] => [
    ...(isFFA(FF_DEV_0041)
      ? [
          {
            key: 1,
            label: (
              <div
                className='flex items-center gap-2'
                onClick={() => {
                  navigate(`/manage-class/${record.uuidGroup}`, { state: true })
                  setActiveClassDetail('Thông tin chung')
                }}
              >
                <Svg src='/assets/icons/common/edit.svg' className='h-5 w-5 text-neutral-4' />
                Sửa
              </div>
            )
          }
        ]
      : []),
    ...(isFFA(FF_DEV_0042)
      ? [
          {
            key: 2,
            label: (
              <div
                className='flex items-center gap-2'
                onClick={() => {
                  setOpenDeleteModal(true)
                }}
              >
                <Svg src='/assets/icons/common/trash.svg' className='h-5 w-5 text-neutral-4' />
                Xóa
              </div>
            )
          }
        ]
      : [])
  ]

  const statusClass = activeClass === 'Lớp học' ? statusCreditClass : statusExamClass

  return (
    <>
      <ModalDelete
        isLoading={loading}
        isModalOpen={openDeleteModal}
        handleCancel={() => {
          setOpenDeleteModal(false)
        }}
        title='Xóa'
        handleOk={() => handleDelete(uuid, getData)}
        subTitle='Bạn có chắc chắn muốn xóa lớp đã chọn?'
      />
      <ModalDelete
        isModalOpen={openDeleteMultipleModal}
        isLoading={onAction}
        handleCancel={() => {
          setOpenDeleteMultipleModal(false)
        }}
        title='Xóa'
        handleOk={handleRemoveMultipleClass}
        subTitle='Bạn có chắc chắn muốn xóa các lớp đã chọn?'
      />
      <LayoutCotent title={['Quản lý lớp']}>
        <Spin spinning={isLoading || loading}>
          <Segmented
            value={activeClass}
            onChange={(value) => {
              setActiveClass(value)
              setSearchData(initialValue)
            }}
            size='large'
            options={classList}
            className='mb-5 border max-sm:flex max-sm:[&_.ant-segmented-item]:w-1/2'
          />
          <div className='grid grid-cols-2 sm:grid-cols-4 lg:flex gap-3 items-end mb-5'>
            <div className='col mb-0 w-full lg:max-w-[12%]'>
              <span>Năm học</span>
              <Select
                size='large'
                className='w-full border-neutral-7'
                placeholder='Năm học'
                options={academicYears.map((item) => ({
                  label: item.name,
                  value: item.uuidAcademicYear
                }))}
                value={selectYear}
                onChange={(e) => setSelectYear(e)}
              />
            </div>

            <div className=' mb-0 w-full lg:max-w-[15%]'>
              <span>Kỳ học</span>
              <Select
                size='large'
                className='w-full border-neutral-7'
                placeholder='Chọn kỳ học'
                defaultValue={''}
                loading={loadingData}
                value={searchData.uuidSemester}
                options={[
                  { label: 'Tất cả', value: '' },
                  ...semesters.map((item) => ({
                    label: item.name,
                    value: item.uuidSemester
                  }))
                ]}
                onChange={(e) => setSearchData((pre) => ({ ...pre, uuidSemester: e, page: 1, maxSize: 50 }))}
              />
            </div>

            <div className='mb-0 w-full lg:max-w-[15%]'>
              <span>Trạng thái</span>
              <Select
                size='large'
                className='w-full border-neutral-7'
                placeholder='Trạng thái'
                value={searchData.progressStatus}
                options={[
                  { label: 'Tất cả', value: -1 },
                  ...statusClass.map((item) => ({
                    label: item.label,
                    value: item.value
                  }))
                ]}
                defaultValue={-1}
                onChange={(e) => setSearchData((pre) => ({ ...pre, progressStatus: e, page: 1, maxSize: 50 }))}
              />
            </div>
            {user.roles?.[0].name !== ROLE_TITLE.USER && (
              <div className='mb-0 w-full lg:max-w-[15%]'>
                <span>{activeClass === 'Lớp học' ? 'Giảng viên' : 'Cán bộ trông thi'}</span>
                <Select
                  size='large'
                  className='w-full border-neutral-7'
                  value={searchData.uuidLecturer}
                  options={[
                    { label: 'Tất cả', value: '' },
                    ...listPersons.map((item) => ({
                      label: `${item.fullName} - ${item.personCode.toLocaleUpperCase()}`,
                      value: item.uuidPerson
                    }))
                  ]}
                  onPopupScroll={handlePopupScroll}
                  notFoundContent={isLoadingPerson ? <Spin size='small' /> : null}
                  showSearch
                  filterOption={(input, option) => (option?.label ?? '').toLowerCase().includes(input.toLowerCase())}
                  loading={isLoadingPerson}
                  defaultValue={''}
                  placeholder={activeClass === 'Lớp học' ? 'Giảng viên' : 'Cán bộ trông thi'}
                  onChange={(e) => setSearchData((pre) => ({ ...pre, uuidLecturer: e, page: 1, maxSize: 50 }))}
                />
              </div>
            )}

            <div className='sm:col-span-2 mb-0 w-full'>
              <span>Tìm kiếm</span>
              <Input
                size='large'
                className='w-full border-neutral-7'
                placeholder={isMobile ? 'Nhập tìm kiếm' : 'Nhập nội dung tìm kiếm'}
                onChange={(e) => setSearchData((pre) => ({ ...pre, keySearch: e.target.value }))}
                onPressEnter={() => {
                  setPageProperty((pre) => ({ ...pre, page: 1, maxSize: 50 }))
                }}
                value={searchData.keySearch}
                suffix={
                  isMobile && (
                    <Button
                      size='small'
                      type='primary'
                      icon={<Svg src='/assets/icons/common/search.svg' className='h-4 w-4 mt-1' />}
                      onClick={() => setPageProperty((pre) => ({ ...pre, page: 1, maxSize: 50 }))}
                    />
                  )
                }
              />
            </div>
            {!isMobile && (
              <Button
                size='large'
                type='primary'
                className='w-fit'
                icon={<Svg src='/assets/icons/common/search.svg' className='h-5 w-5 mt-1' />}
                onClick={() => setPageProperty((pre) => ({ ...pre, page: 1, maxSize: 50 }))}
              >
                <span className='hidden 3xl:block'>Tìm kiếm</span>
              </Button>
            )}
            {isFFA(FF_DEV_0040) && (
              <div
                className={`sm:col-end-5 flex items-center ${user.roles?.[0].name !== ROLE_TITLE.USER ? 'justify-end' : ''} gap-3`}
              >
                {selectedRows.length !== 0 && (
                  <Button
                    size='large'
                    icon={<Svg src='/assets/icons/common/trash.svg' className='h-5 w-5 mt-1' />}
                    color='primary'
                    variant='outlined'
                    className='max-sm:w-10'
                    onClick={() => setOpenDeleteMultipleModal(true)}
                  >
                    <span className='hidden 3xl:block'>Xóa</span>
                  </Button>
                )}
                <Button
                  size='large'
                  icon={<PlusOutlined />}
                  color='primary'
                  variant='outlined'
                  onClick={() => navigate('/manage-class/add', { state: { activeClass } })}
                >
                  <span className={`${user.roles?.[0].name === ROLE_TITLE.USER ? 'block' : 'hidden'} xs:block`}>
                    Thêm mới
                  </span>
                </Button>
              </div>
            )}
          </div>
          <Spin spinning={isLoadingPerson}>
            <Table
              rowKey={(record) => record.uuidGroup}
              columns={columns}
              pagination={false}
              dataSource={dataSource?.data || []}
              rowSelection={user.roles?.[0].name === ROLE_TITLE.ADMIN ? rowSelection : undefined}
              scroll={{ y: 500 }}
            />
          </Spin>
          {dataSource?.data && dataSource?.data.length > 0 && (
            <div className='my-4 flex max-sm:flex-col items-end max-sm:items-center justify-between gap-4'>
              <div className='flex items-center gap-2'>
                <p className='text-xs font-normal text-gray-400'>Số bản ghi mỗi trang </p>
                <Select
                  size='small'
                  options={pageSize}
                  value={pageProperty.maxSize}
                  onChange={(e) => setPageProperty({ ...pageProperty, maxSize: e, page: 1 })}
                />
                <p className='text-xs font-normal text-gray-400 hidden sm:block'>
                  trên tổng {dataSource.totalElement} dữ liệu
                </p>
              </div>

              <Pagination
                onChange={(page) => {
                  setPageProperty({ ...pageProperty, page })
                }}
                align='end'
                current={dataSource.page}
                defaultCurrent={1}
                showSizeChanger={false}
                pageSize={dataSource.maxSize}
                defaultPageSize={10}
                total={dataSource.totalElement}
              />
            </div>
          )}
        </Spin>
      </LayoutCotent>
    </>
  )
}

export default ManageClass
