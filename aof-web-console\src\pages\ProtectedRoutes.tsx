import { Navigate, Route, Routes } from 'react-router-dom'
import useAuthInitialization from '@/hooks/useAuthInitialization'
import LoadingComponent from '@/common/Loading'
import DefaultLayout from '@/layouts/DefaultLayout'
import useAuthStore from '@/stores/auth'
import {
  checkFeature,
  FF_DEV_0001,
  FF_DEV_0002,
  FF_DEV_0003,
  FF_DEV_0004,
  FF_DEV_0005,
  FF_DEV_0006,
  FF_DEV_0021,
  FF_DEV_0022,
  FF_DEV_0024,
  FF_DEV_0025,
  FF_DEV_0029,
  FF_DEV_0033,
  FF_DEV_0034,
  FF_DEV_0039,
  FF_DEV_0040,
  FF_DEV_0041,
  FF_DEV_0044,
  // ... các FF khác nếu cần
  isAdmin
} from '@/utils/feature-flags'

// import các page tương ứng...
import ManageInfomation from './manage-info'
import ManageUser from './manage-user'
import ManageClass from './manage-class'
import ManageRoomftDevices from './manage-room&device'
import ManagExamination from './manage-exam'
import ManageFeature from './manage-feature'
import SystemLog from './system-log'
import PersonalInfo from './personal-info'
import ManageInfomationUser from '@/components/ManageUser/ManageInfomationUser'
import AddNewClass from '@/components/ManageClass/AddNewClass'
import AddPersonToClass from '@/components/ManageClass/AddPersonToClass'
import AddSessionRecord from '@/components/ManageClass/RecordComponents/AddSessionRecord'
import SessionAttendance from '@/components/ManageClass/RecordComponents/SessionAttendance'
import DetailsClass from '@/components/ManageClass/DetailsClass'
import ManageListStudents from '@/components/ManageInfo/ManageStudents'
import ManageSchoolYears from '@/components/ManageInfo/ManageSchoolYears'
import DetailsPermission from '@/components/MangePermission/DetailsPermission'
import DetailsPermissionPerson from '@/components/MangePermission/DetailsPermissionPerson'
import Login from './auth'
import CheckLicense from './check-license'
import Error403 from './error-page/403'

const ProtectedRoutes = () => {
  const { user, featFlagsParent, featFlagsAction, isLoggedIn, licenseStatus } = useAuthStore()
  useAuthInitialization()
  const isFFP = (code: string) => checkFeature(code, user, featFlagsParent)
  const isFFA = (code: string) => checkFeature(code, user, featFlagsAction)

  const defaultRedirectPath = (() => {
    if (isFFP(FF_DEV_0001)) return '/manage-info'
    if (isFFP(FF_DEV_0002)) return '/manage-user'
    if (isFFP(FF_DEV_0003)) return '/manage-class'
    if (isFFP(FF_DEV_0004)) return '/manage-room-device'
    if (isFFP(FF_DEV_0005)) return '/manage-exam'
    if (isFFP(FF_DEV_0006)) return '/manage-feature'
    return ''
  })()

  if (!isLoggedIn) {
    return (
      <Routes>
        <Route path='/login' element={<Login />} />
        <Route path='*' element={<Navigate to='/login' />} />
        <Route path='/sso/identifier' element={<LoadingComponent />} />
        <Route path='/sso/identifier-logout' element={<LoadingComponent />} />
        <Route path='/403' element={<Error403 />} />
      </Routes>
    )
  }
  if (licenseStatus === 0) {
    return (
      <Routes>
        <Route path='/check-license' element={<CheckLicense />} />
        <Route path='*' element={<Navigate to='/check-license' replace />} />
      </Routes>
    )
  } else if (licenseStatus === 2) {
    return <LoadingComponent />
  }

  return (
    <Routes>
      <Route element={<DefaultLayout />}>
        {isFFP(FF_DEV_0001) && <Route path='/manage-info' element={<ManageInfomation />} />}
        {isFFP(FF_DEV_0002) && <Route path='/manage-user' element={<ManageUser />} />}
        {isFFP(FF_DEV_0003) && <Route path='/manage-class' element={<ManageClass />} />}
        {isFFP(FF_DEV_0004) && <Route path='/manage-room-device' element={<ManageRoomftDevices />} />}
        {isFFP(FF_DEV_0005) && <Route path='/manage-exam' element={<ManagExamination />} />}
        {isFFP(FF_DEV_0006) && <Route path='/manage-feature' element={<ManageFeature />} />}
        {isAdmin(user) && <Route path='/settings' element={<SystemLog />} />}

        {!isAdmin(user) && <Route path='/personal-info' element={<PersonalInfo />} />}

        {isFFA(FF_DEV_0024) && <Route path='/manage-user/add' element={<ManageInfomationUser />} />}
        {isFFA(FF_DEV_0025) && <Route path='/manage-user/:uuidPerson' element={<ManageInfomationUser />} />}

        {isFFA(FF_DEV_0040) && <Route path='/manage-class/add' element={<AddNewClass />} />}
        {isFFA(FF_DEV_0041) && <Route path='/manage-class/:uuidGroup/add' element={<AddPersonToClass />} />}
        {isFFA(FF_DEV_0044) && <Route path='/manage-class/:uuidGroup/checkin' element={<AddSessionRecord />} />}
        {isFFA(FF_DEV_0039) && (
          <Route path='/manage-class/:uuidGroup/session-attendance/:uuidSession' element={<SessionAttendance />} />
        )}
        {isFFA(FF_DEV_0039) && <Route path='/manage-class/:uuidGroup' element={<DetailsClass />} />}

        {isFFA(FF_DEV_0029) && <Route path='/manage-info/students/:uuidGroup' element={<ManageListStudents />} />}
        {isFFA(FF_DEV_0034) && <Route path='/manage-info/academic-year/create' element={<ManageSchoolYears />} />}
        {isFFA(FF_DEV_0033) && (
          <Route path='/manage-info/academic-year/:uuidAcademicYear' element={<ManageSchoolYears />} />
        )}

        {isFFA(FF_DEV_0022) && <Route path='/manage-feature/add' element={<DetailsPermission />} />}
        {isFFA(FF_DEV_0021) && <Route path='/manage-feature/:uuidPermissionGroup' element={<DetailsPermission />} />}
        {isFFA(FF_DEV_0021) && (
          <Route path='/manage-feature/people/:uuidPerson' element={<DetailsPermissionPerson />} />
        )}
        <Route path='*' element={<Navigate to={defaultRedirectPath} replace />} />
      </Route>
    </Routes>
  )
}

export default ProtectedRoutes
