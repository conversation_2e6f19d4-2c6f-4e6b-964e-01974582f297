import { Button, Modal } from 'antd'
import Svg from '../Svg'
import { ModalProps } from '@/types/common'

const ModalNotification = ({ isModalOpen, handleCancel, handleOk, subTitle, isLoading, title }: ModalProps) => {
  const footerModal = (
    <div className='flex items-center justify-center gap-4 p-2 pt-4'>
      {handleOk && (
        <Button
          loading={isLoading}
          onClick={handleCancel}
          variant='outlined'
          color='primary'
          size='large'
          className='min-w-[126px] shadow-none'
        >
          Hủy
        </Button>
      )}

      <Button
        loading={isLoading}
        onClick={handleOk ? handleOk : handleCancel}
        type='primary'
        size='large'
        className='min-w-[126px] shadow-none'
      >
        {handleOk ? 'Thêm mới' : 'Xác nhận'}
      </Button>
    </div>
  )
  return (
    <>
      <Modal open={isModalOpen} onCancel={handleCancel} footer={footerModal} width={335}>
        <div className='mt-6 flex flex-col items-center gap-4'>
          <Svg src='/assets/icons/common/icon-notification.svg' className='h-20 w-20' />
          <div>
            <h2 className='text-center font-semibold text-md'>{title}</h2>
            <p className='text-center font-normal text-sm text-neutral-2'>{subTitle}</p>
          </div>
        </div>
      </Modal>
    </>
  )
}

export default ModalNotification
