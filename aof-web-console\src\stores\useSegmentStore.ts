import {
  PremissionType,
  ClassInfo,
  ClassType,
  InfoType,
  TYPE_CLASS_CREDIT,
  TYPE_CLASS_EXAM,
  TYPE_PERSON,
  PermissionInfo,
  SystemInfo
} from '@/types/components'
import { create } from 'zustand'
import { createJSONStorage, persist } from 'zustand/middleware'

interface SegmentStore {
  activePerson: TYPE_PERSON
  activeClass: ClassType
  activeInfo: InfoType
  activeList: PremissionType
  activeClassDetail: ClassInfo
  activePermissionDetail: PermissionInfo
  activePermissionPerson: PermissionInfo
  activeLog: SystemInfo
  active: string
  setActivePerson: (value: TYPE_PERSON) => void
  setActiveClass: (value: ClassType) => void
  setActiveList: (value: PremissionType) => void
  setActive: (value: string) => void
  setActiveInfo: (value: InfoType) => void
  setActiveClassDetail: (value: ClassInfo) => void
  setActivePermissionDetail: (value: PermissionInfo) => void
  setActivePermissionPerson: (value: PermissionInfo) => void
  setActiveLog: (value: SystemInfo) => void

  personType: number
  setPersonType: (value: number) => void
  groupType: number
  setGroupType: (value: number) => void
  session: { sessionDate: string; uuidSession?: string }
  setSession: (session: { sessionDate: string; uuidSession?: string }) => void
  personName: string
  setPersonName: (value: string) => void
}

export const useSegmentStore = create<SegmentStore>()(
  persist(
    (set) => ({
      active: 'Quản lý phòng học',
      activeInfo: InfoType['Quản lý khóa'],
      activePerson: TYPE_PERSON['Sinh viên'],
      activeClass: 'Lớp học',
      activeList: PremissionType['Danh sách nhóm quyền'],
      activeClassDetail: 'Thông tin chung',
      activePermissionDetail: 'Phân quyền chức năng',
      activePermissionPerson: 'Danh sách nhóm quyền',
      activeLog: 'Nhật ký hệ thống',

      personType: TYPE_PERSON['Sinh viên'],
      groupType: TYPE_CLASS_CREDIT,
      session: { sessionDate: '', uuidSession: undefined },
      personName: '',

      setActive: (value) => set({ active: value }),
      setActivePerson: (value) => set({ activePerson: value }),
      setActiveInfo: (value) => set({ activeInfo: value }),
      setActiveClass: (value) =>
        set({
          activeClass: value,
          groupType: value === 'Lớp học' ? TYPE_CLASS_CREDIT : TYPE_CLASS_EXAM,
          activeClassDetail: 'Thông tin chung'
        }),
      setActivePermissionDetail: (value) => set({ activePermissionDetail: value }),
      setActivePermissionPerson: (value) => set({ activePermissionPerson: value }),
      setActiveLog: (value) => set({ activeLog: value }),
      setActiveClassDetail: (value) => set({ activeClassDetail: value }),
      setActiveList: (value) => set({ activeList: value }),
      setPersonType: (value) => set({ personType: value }),
      setGroupType: (value) => set({ groupType: value }),
      setSession: (session) => set({ session }),
      setPersonName: (value) => set({ personName: value })
    }),
    { name: 'data-storage', storage: createJSONStorage(() => sessionStorage) }
  )
)
