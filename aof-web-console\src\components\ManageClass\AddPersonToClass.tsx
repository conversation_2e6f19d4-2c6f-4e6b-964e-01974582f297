import LayoutCotent from '@/layouts/LayoutCotent'
import { Button, Input, Select, Spin, Table, TableProps } from 'antd'
import { useLocation, useNavigate, useParams } from 'react-router-dom'
import { useCallback, useEffect, useState } from 'react'
import { getDetailsGroup, getListGroups } from '@/services/group'
import { MAX_SIZE_PAGE, TYPE_COURSE, TYPE_PERSON } from '@/types/components'
import { ERROR_FROM_USER, MESSAGE_STATUS } from '@/constants'
import { showCustomNotification } from '@/common/Notification'
import { addMultiplePersonGroup, getListPersons, removeMultiplePersonGroup } from '@/services/person'
import { Face, Person } from '@/types/person'
import useDebounce from '@/hooks/useDebounce'
import { useSegmentStore } from '@/stores/useSegmentStore'
import Svg from '../Svg'
import ModalImport from '../Modals/ImportModal'
import useGroupStore from '@/stores/group'

const AddPersonToClass = () => {
  const navigate = useNavigate()
  const { uuidGroup } = useParams()
  const { state } = useLocation()

  const { listGroups, setGroups, groupName, setGroupName } = useGroupStore()
  const { personType } = useSegmentStore()
  const person =
    personType === TYPE_PERSON['Sinh viên'] ? 'sinh viên' : state && state === 'Lớp học' ? 'giảng viên' : 'cán bộ'

  const [groupTypeName, setGroupTypeName] = useState<string>('')
  const [filterByGroup, setFilterByGroup] = useState<string>('')

  const [listPersonsAll, setListPersonsAll] = useState<Person[]>([])
  const [listPersonsGroup, setListPersonsGroup] = useState<Person[]>([])
  const [listPersonsGroupInit, setListPersonsGroupInit] = useState<Person[]>([])

  const [listPersonsUpdate, setListPersonsUpdate] = useState<Person[]>([])

  const [selectedRows, setSelectedRows] = useState<Person[]>([])
  const [removeRows, setRemoveRows] = useState<Person[]>([])

  const [personsAdd, setPersonsAdd] = useState<Person[]>([])
  const [personsRemove, setPersonsRemove] = useState<Person[]>([])

  const [dataImport, setDataImport] = useState<any[]>([])

  const [filter, setFilter] = useState({
    uuidGroupAll: '',
    personTypeAll: '2,3',
    searchAll: '',
    search: '',
    personType: '2,3',
    uuidGroup
  })
  const [currentPage, setCurrentPage] = useState({ pageAll: 1, pageClass: 1 })

  const [loading, setLoading] = useState({ loadingAll: false, loadingSelected: false })
  const [openImportModal, setOpenImportModal] = useState<boolean>(false)
  const [hasMoreAll, setHasMoreAll] = useState<boolean>(true)
  const [hasMore, setHasMore] = useState<boolean>(true)
  const [onUpdating, setOnUpdating] = useState<boolean>(false)

  const delaySearchAll = useDebounce(filter.searchAll, 500)
  const delaySearch = useDebounce(filter.search, 500)

  const getDetails = useCallback(async () => {
    try {
      if (!uuidGroup) return
      const res = await getDetailsGroup(uuidGroup)
      if (res.message === MESSAGE_STATUS.SUCCESS) {
        setGroupName(res.object.groupName)
        if (res.object.groupType === 2) {
          setGroupTypeName('Lớp học')
        } else if (res.object.groupType === 3) {
          setGroupTypeName('Lớp thi')
        }
      }
    } catch {
      console.error('Lỗi không lấy được thông tin khóa học!')
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [uuidGroup])

  const fetchPersonsAll = useCallback(async () => {
    try {
      const personTypes =
        state === 'Lớp thi' && personType !== TYPE_PERSON['Sinh viên'] ? filter.personTypeAll : String(personType)
      setLoading((pre) => ({ ...pre, loadingAll: true }))
      const res = await getListPersons({
        page: currentPage.pageAll,
        maxSize: MAX_SIZE_PAGE,
        keySearch: delaySearchAll,
        personTypes,
        uuidGroups: filter.uuidGroupAll
      })
      if (res.message === MESSAGE_STATUS.SUCCESS) {
        const newData = res.object.data
        setListPersonsAll((prev) => {
          const existingIds = new Set(prev.map((item) => item.uuidPerson))
          const filteredData = newData.filter((item) => !existingIds.has(item.uuidPerson))
          return [...prev, ...filteredData]
        })
        if (newData.length < MAX_SIZE_PAGE) {
          setHasMoreAll(false)
        }
      }
    } catch {
      showCustomNotification({ status: 'error', message: 'Thất bại', description: 'Lấy dữ liệu thất bại!' })
    } finally {
      setLoading((pre) => ({ ...pre, loadingAll: false }))
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentPage.pageAll, delaySearchAll, filter.uuidGroupAll, filter.personTypeAll])

  const fetchPersonsClass = useCallback(async () => {
    try {
      const personTypes =
        state === 'Lớp thi' && personType !== TYPE_PERSON['Sinh viên'] ? filter.personType : String(personType)
      if (!filter.uuidGroup) return
      setLoading((pre) => ({ ...pre, loadingSelected: true }))
      const res = await getListPersons({
        page: currentPage.pageClass,
        maxSize: MAX_SIZE_PAGE,
        keySearch: delaySearch,
        personTypes,
        uuidGroups: filter.uuidGroup
      })
      const mergeUniqueById = <T extends { uuidPerson: string }>(oldList: T[], newList: T[]) => {
        const map = new Map(oldList.map((item) => [item.uuidPerson, item]))
        newList.forEach((item) => map.set(item.uuidPerson, item)) // ghi đè hoặc thêm mới
        return Array.from(map.values())
      }

      if (res.message === MESSAGE_STATUS.SUCCESS) {
        const data = res.object.data
        const mergedList = mergeUniqueById(data, personsAdd)
        const finalList = mergedList.filter(
          (person) => !personsRemove.some((removed) => removed.uuidPerson === person.uuidPerson)
        )

        setListPersonsGroup((prev) => mergeUniqueById(prev, finalList))
        setListPersonsGroupInit((prev) => mergeUniqueById(prev, data))

        if (data.length < MAX_SIZE_PAGE) {
          setHasMore(false)
        }
      }
    } catch {
      showCustomNotification({ status: 'error', message: 'Thất bại', description: 'Lấy dữ liệu thất bại!' })
    } finally {
      setLoading((pre) => ({ ...pre, loadingSelected: false }))
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [delaySearch, filter.uuidGroup, currentPage.pageClass, filter.personType])

  useEffect(() => {
    if (personType == TYPE_PERSON['Sinh viên']) {
      ;(async () => {
        try {
          const res = await getListGroups({ page: 1, maxSize: 999, groupType: TYPE_COURSE })
          if (res.message === MESSAGE_STATUS.SUCCESS) setGroups(res.object.data || [])
        } catch {
          console.error('Failed to get list of groups!')
          setGroups([])
        }
      })()
    }
    getDetails()
  }, [personType, setGroups, getDetails])

  useEffect(() => {
    setCurrentPage((pre) => ({ ...pre, pageAll: 1 }))
    setListPersonsAll([])
    setHasMoreAll(true)
  }, [delaySearchAll, filter.uuidGroupAll, filter.personTypeAll])

  useEffect(() => {
    setCurrentPage((pre) => ({ ...pre, pageClass: 1 }))
    setListPersonsGroup([])
    setHasMore(true)
  }, [delaySearch, filter.uuidGroup, filter.personType])

  useEffect(() => {
    fetchPersonsAll()
  }, [fetchPersonsAll])

  useEffect(() => {
    fetchPersonsClass()
  }, [fetchPersonsClass])

  const handleSelect = () => {
    const newPersons = selectedRows.filter((item) => !listPersonsGroup.some((p) => p.uuidPerson === item.uuidPerson))
    const newList = [...listPersonsGroup, ...newPersons]
    setListPersonsGroup(newList)
    setListPersonsUpdate((prev) => [...prev, ...newPersons])
    setPersonsAdd([...personsAdd, ...selectedRows])
    setSelectedRows([])
  }

  const handleRemove = () => {
    const newList = listPersonsGroup.filter((item) => !removeRows.some((row) => row.uuidPerson === item.uuidPerson))
    setListPersonsGroup(newList)
    setListPersonsUpdate(newList)
    setPersonsRemove([...personsRemove, ...removeRows])
    setRemoveRows([])
  }

  const handleSave = async () => {
    try {
      setOnUpdating(true)
      const personsToAdd = listPersonsUpdate.filter(
        (itemUpdate) => !listPersonsGroupInit.some((itemOriginal) => itemOriginal.uuidPerson === itemUpdate.uuidPerson)
      )
      const personsToRemove = listPersonsGroupInit.filter(
        (itemOriginal) => !listPersonsGroup.some((itemUpdate) => itemUpdate.uuidPerson === itemOriginal.uuidPerson)
      )

      const [resAdd, resDel] = await Promise.all([
        personsToAdd.length > 0
          ? addMultiplePersonGroup({
              personGroups: personsToAdd.map((p) => ({
                personId: p.uuidPerson,
                groupId: uuidGroup || ''
              }))
            })
          : Promise.resolve(null),
        personsToRemove.length > 0
          ? removeMultiplePersonGroup({
              personGroups: personsToRemove.map((p) => ({
                personId: p.uuidPerson,
                groupId: uuidGroup || ''
              }))
            })
          : Promise.resolve(null)
      ])

      const isSuccessAdd = resAdd?.message === MESSAGE_STATUS.SUCCESS || resAdd === null
      const isSuccessDel = resDel?.message === MESSAGE_STATUS.SUCCESS || resDel === null

      if (isSuccessAdd && isSuccessDel) {
        setListPersonsGroupInit([])
        setPersonsAdd([])
        setPersonsRemove([])
        fetchPersonsClass()
        setSelectedRows([])
        setRemoveRows([])
        setFilter({
          uuidGroupAll: '',
          searchAll: '',
          personType: '2,3',
          personTypeAll: '2,3',
          search: '',
          uuidGroup
        })
        showCustomNotification({
          status: 'success',
          message: 'Thành công',
          description: `Cập nhật ${person} vào lớp thành công!`
        })
      }
    } catch {
      showCustomNotification({
        status: 'error',
        message: 'Thất bại',
        description: 'Lưu thất bại!'
      })
    } finally {
      setOnUpdating(false)
      navigate(-1)
    }
  }

  const handleOk = async (persons: any) => {
    try {
      const body = persons.map((p: { [x: string]: any }) => ({
        personId: String(p['ID']).toLowerCase(),
        groupId: uuidGroup || ''
      }))

      const idPerson = listPersonsAll.map((p) => p.personCode)
      const idExist = body.every((b: { personId: string }) => idPerson.includes(b.personId))
      if (!idExist) {
        throw new Error(ERROR_FROM_USER.INVALID_ID)
      }

      const res = await addMultiplePersonGroup({
        personGroups: body,
        personIdType: 2
      })
      if (res.message === MESSAGE_STATUS.SUCCESS) {
        setDataImport([])
        fetchPersonsClass()
        showCustomNotification({
          status: 'success',
          message: 'Thành công',
          description: `Import ${person} vào lớp thành công!`
        })
      }
    } catch (e: any) {
      const errorMessages: { [key: string]: { message: string; description: string } } = {
        [ERROR_FROM_USER.INVALID_ID]: {
          message: 'Lỗi!',
          description: 'Không tìm thấy ID của người dùng trong hệ thống!'
        }
      }
      const errorInfo = errorMessages[e.message] || {
        message: 'Thất bại!',
        description: 'Có lỗi xảy ra trong quá trình nhập dữ liệu!'
      }

      showCustomNotification({
        status: 'error',
        message: errorInfo.message,
        description: errorInfo.description
      })
    } finally {
      setOpenImportModal(false)
    }
  }

  const handleCancel = () => {
    setListPersonsGroup(listPersonsGroupInit)
    setListPersonsUpdate([])
    setSelectedRows([])
    setRemoveRows([])
  }

  const rowSelectionAll = {
    selectedRowKeys: selectedRows.map((r) => r.uuidPerson),
    onChange: (_: React.Key[], selectedRows: Person[]) => {
      setSelectedRows(selectedRows)
    }
  }

  const rowSelectionGroup = {
    selectedRowKeys: removeRows.map((r) => r.uuidPerson),
    onChange: (_: React.Key[], selectedRows: Person[]) => {
      setRemoveRows(selectedRows)
    }
  }

  const columns: TableProps<Person>['columns'] = [
    {
      title: 'Ảnh đại diện',
      dataIndex: 'faces',
      key: 'faces',
      width: 60,
      render: (faces: Face[]) => (
        <div className='flex justify-center'>
          {faces.length > 0 ? (
            <img src={faces?.[0]?.objectUrl} alt='avatar' className='w-10 h-10 rounded-full object-cover border' />
          ) : (
            <Svg src='/assets/icons/common/image-avatar.svg' className='w-10 h-10' />
          )}
        </div>
      )
    },
    {
      title: 'Họ tên',
      dataIndex: 'fullName',
      key: 'fullName',
      sorter: true,
      render: (value, record) => (
        <div className='flex flex-col'>
          {value} <span className='text-text-4 text-xs'>ID: {record.personCode.toUpperCase()}</span>
        </div>
      )
    }
  ]

  console.log(listPersonsUpdate)

  const getFilteredPersons = () => {
    return listPersonsGroup
      ?.filter(
        (person) => !filterByGroup || person.uuidGroup === filterByGroup || String(person.personType) === filterByGroup
      )
      .filter(
        (person) =>
          !delaySearch ||
          person.fullName.toLowerCase().includes(delaySearch.toLowerCase()) ||
          person.personCode.includes(delaySearch)
      )
  }

  return (
    <>
      <ModalImport
        isModalOpen={openImportModal}
        handleOk={handleOk}
        handleCancel={() => {
          setOpenImportModal(false)
        }}
        dataImport={dataImport}
        setDataImport={setDataImport}
        title={`Import ${person} vào lớp`}
      />
      <LayoutCotent
        title={['Quản lý lớp', `${groupTypeName}`, `${groupName}`, `Thêm mới ${person} vào lớp`]}
        btnBack={true}
      >
        <Spin spinning={onUpdating}>
          <div className='flex items-center justify-end gap-2 sm:relative pb-4'>
            <Button
              size='large'
              icon={<Svg src='/assets/icons/common/import-status.svg' className='h-5 w-5 mt-1' />}
              color='primary'
              className='max-sm:w-full'
              variant='outlined'
              onClick={() => setOpenImportModal(true)}
            >
              <span>{`Import ${person} vào lớp`}</span>
            </Button>
          </div>
          <div className='w-full flex max-md:flex-col gap-5 h-full'>
            <div className='w-1/2 max-md:w-full bg-background-card rounded-lg p-1 h-full'>
              <h2 className='px-5 py-3 text-neutral-1 text-base font-medium'>{`Danh sách tất cả ${person}`}</h2>
              <div className='bg-white rounded-lg p-5 h-full'>
                <div className='flex max-md:flex-col gap-5'>
                  {personType == TYPE_PERSON['Sinh viên'] && (
                    <div className='flex flex-col w-full'>
                      <label htmlFor='course'>Khóa</label>
                      <Select
                        size='large'
                        onChange={(e) => setFilter((pre) => ({ ...pre, uuidGroupAll: e }))}
                        options={[
                          { label: 'Tất cả', value: '' },
                          ...listGroups.map((item) => ({
                            label: item.groupName,
                            value: item.uuidGroup
                          }))
                        ]}
                        defaultValue=''
                      />
                    </div>
                  )}
                  {groupTypeName === 'Lớp thi' && personType !== TYPE_PERSON['Sinh viên'] && (
                    <div className='flex flex-col w-full'>
                      <label htmlFor='course'>Phân loại</label>
                      <Select
                        size='large'
                        placeholder='Phân loại'
                        onChange={(e) => setFilter((pre) => ({ ...pre, personTypeAll: e }))}
                        options={[
                          { label: 'Tất cả', value: '2,3' },
                          { label: 'Giảng viên', value: '2' },
                          { label: 'Nhân viên chức năng', value: '3' }
                        ]}
                        defaultValue='2,3'
                      />
                    </div>
                  )}
                  <div className='flex flex-col w-full'>
                    <label htmlFor='search'>Tìm kiếm</label>
                    <Input
                      size='large'
                      placeholder='Nhập nội dung tìm kiếm'
                      onChange={(e) => setFilter((pre) => ({ ...pre, searchAll: e.target.value }))}
                      suffix={<Svg src='/assets/search.svg' className='h-5 w-5 text-text-4' />}
                    />
                  </div>
                </div>
                <Table
                  loading={loading.loadingAll}
                  showHeader={false}
                  columns={columns}
                  dataSource={listPersonsAll}
                  pagination={false}
                  className='m-2'
                  onScroll={({ target }) => {
                    const { scrollTop, scrollHeight, clientHeight } = target as HTMLDivElement
                    if (scrollTop + clientHeight >= scrollHeight - 50 && hasMoreAll && !loading.loadingAll) {
                      setCurrentPage((prev) => ({ ...prev, pageAll: prev.pageAll + 1 }))
                    }
                  }}
                  rowKey={(record) => record.uuidPerson}
                  rowSelection={rowSelectionAll}
                  scroll={{ y: 500 }}
                />

                <div className='flex justify-center'>
                  <Button
                    style={{ width: 120 }}
                    className='mt-2'
                    size='large'
                    variant='outlined'
                    color='primary'
                    disabled={selectedRows.length === 0}
                    onClick={handleSelect}
                  >
                    Chọn
                  </Button>
                </div>
              </div>
            </div>
            <div className='w-1/2 max-md:w-full bg-background-card rounded-lg p-1 h-full'>
              <h2 className='px-5 py-3 text-neutral-1 text-base font-medium'>{`Danh sách ${person} đã chọn`}</h2>
              <div className='bg-white rounded-lg p-5 h-full'>
                <div className='flex max-md:flex-col gap-5'>
                  {personType == TYPE_PERSON['Sinh viên'] && (
                    <div className='flex flex-col w-full'>
                      <label htmlFor='course'>Khóa</label>
                      <Select
                        size='large'
                        placeholder='Chọn khóa'
                        onChange={setFilterByGroup}
                        options={[
                          { label: 'Tất cả', value: '' },
                          ...listGroups.map((item) => ({
                            label: item.groupName,
                            value: item.uuidGroup
                          }))
                        ]}
                        defaultValue=''
                      />
                    </div>
                  )}
                  {groupTypeName === 'Lớp thi' && personType !== TYPE_PERSON['Sinh viên'] && (
                    <div className='flex flex-col w-full'>
                      <label htmlFor='course'>Phân loại</label>
                      <Select
                        size='large'
                        placeholder='Phân loại'
                        onChange={setFilterByGroup}
                        options={[
                          { label: 'Tất cả', value: '2,3' },
                          { label: 'Giảng viên', value: '2' },
                          { label: 'Nhân viên chức năng', value: '3' }
                        ]}
                        defaultValue='2,3'
                      />
                    </div>
                  )}
                  <div className='flex flex-col w-full'>
                    <label htmlFor='search'>Tìm kiếm</label>
                    <Input
                      size='large'
                      placeholder='Nhập nội dung tìm kiếm'
                      onChange={(e) => setFilter((pre) => ({ ...pre, search: e.target.value }))}
                      suffix={<Svg src='/assets/search.svg' className='h-5 w-5 text-text-4' />}
                    />
                  </div>
                </div>
                <Table
                  loading={loading.loadingSelected}
                  showHeader={false}
                  columns={columns}
                  dataSource={getFilteredPersons()}
                  pagination={false}
                  className='m-2'
                  scroll={{ y: 500 }}
                  onScroll={({ target }) => {
                    const { scrollTop, scrollHeight, clientHeight } = target as HTMLDivElement
                    if (scrollTop + clientHeight >= scrollHeight - 50 && hasMore && !loading.loadingSelected) {
                      setCurrentPage((prev) => ({ ...prev, pageAll: prev.pageAll + 1 }))
                    }
                  }}
                  rowKey={(record) => record.uuidPerson}
                  rowSelection={rowSelectionGroup}
                />
                <div className='flex justify-center'>
                  <Button
                    style={{ width: 120 }}
                    className='mt-2'
                    size='large'
                    variant='outlined'
                    color='primary'
                    disabled={removeRows.length === 0}
                    onClick={handleRemove}
                  >
                    Xóa
                  </Button>
                </div>
              </div>
            </div>
          </div>
          <div className='flex items-center justify-center gap-5 p-2 pt-4'>
            <Button
              className='min-w-[126px] bg-neutral-9 hover:opacity-70'
              size='large'
              variant='outlined'
              color='primary'
              onClick={handleCancel}
            >
              Hủy
            </Button>
            <Button type='primary' size='large' className='min-w-[126px] shadow-none' onClick={handleSave}>
              Lưu
            </Button>
          </div>
        </Spin>
      </LayoutCotent>
    </>
  )
}

export default AddPersonToClass
