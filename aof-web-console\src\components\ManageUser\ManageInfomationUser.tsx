import { showCustomNotification } from '@/common/Notification'
import {
  courseField,
  dateField,
  emailField,
  genderField,
  nameField,
  numberIdField,
  phoneNumberField,
  schoolField,
  specializationField,
  statusField,
  unitField
} from '@/components/InputsField'
import Svg from '@/components/Svg'
import { AxiosError } from 'axios'
import { MESSAGE_STATUS } from '@/constants'
import { setParams } from '@/helpers/configData'
import LayoutCotent from '@/layouts/LayoutCotent'
import { getCustomFields, updateCustomFieldsPerson, uploadFileToPresignedUrl, uploadImage } from '@/services/common'
import { getListGroups } from '@/services/group'
import { addFace, addNewPerson, getDetailPerson, updateFaces, updatePerson } from '@/services/person'
import useStore from '@/stores/group'
import { useSegmentStore } from '@/stores/useSegmentStore'
import { CustomField } from '@/types/common'
import { customFieldPersonList, EntityType, TYPE_COURSE, TYPE_PERSON } from '@/types/components'
import { Button, Col, Form, Image, Row, Spin } from 'antd'
import { RcFile } from 'antd/es/upload'
import dayjs from 'dayjs'
import { useEffect, useState } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import ModalError from '../Modals/ErrorLoadingModal'
import useLayoutStore from '@/stores/layout'
import { getEnumNameByValue } from '@/helpers'

// const beforeUpload = (file: File) => {
//   const isImage = file.type.startsWith('image/')

//   if (!isImage) {
//     showCustomNotification({
//       status: 'error',
//       message: 'Tải ảnh thất bại!',
//       description: "Vui lòng tải ảnh với đuôi mở rộng '.jpg', '.png' hoặc '.jpeg'"
//     })
//   }

//   return isImage
// }

const ManageInfomationUser = () => {
  const [form] = Form.useForm()
  const navigate = useNavigate()
  const { uuidPerson } = useParams()

  const { isMobile } = useLayoutStore()
  const { listGroups, setGroups } = useStore()
  const { activePerson, setActivePerson } = useSegmentStore()

  const [imageUrl, setImageUrl] = useState<File | undefined>(undefined)
  const [avatar, setAvatar] = useState<string>('')
  const [uuidFace, setUuidFace] = useState<string>('')
  const [previewImg, setPreviewImg] = useState<string>('')
  const [listCustomFields, setListCustomFields] = useState<CustomField[]>([])

  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [openErrorModal, setOpenErrorModal] = useState<boolean>(false)

  const personTypeName = getEnumNameByValue(TYPE_PERSON, activePerson)

  useEffect(() => {
    if (uuidPerson) {
      getDetails()
    }
    if (activePerson === TYPE_PERSON['Sinh viên']) {
      if (listGroups.length === 0) {
        getListCourses()
      }
    }
    getListCustomFields()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activePerson, uuidPerson])

  const getListCourses = async () => {
    try {
      const res = await getListGroups({
        page: 1,
        maxSize: 999,
        groupType: TYPE_COURSE
      })
      if (res.message === MESSAGE_STATUS.SUCCESS) {
        setGroups(res.object.data)
      }
    } catch {
      console.error('Failed to get list of groups')
    }
  }

  const getListCustomFields = async () => {
    try {
      const res = await getCustomFields(EntityType.Person)
      if (res.message === MESSAGE_STATUS.SUCCESS) {
        setListCustomFields(res.object.data)
      }
    } catch {
      console.error('Failed to get list custom fields')
    }
  }

  const getDetails = async () => {
    try {
      if (!uuidPerson) return

      setIsLoading(true)
      const res = await getDetailPerson(uuidPerson)

      if (res.message !== MESSAGE_STATUS.SUCCESS) return

      const { object } = res
      const { dob, personType, customFields = [], faces } = object

      form.setFieldsValue(object)
      setActivePerson(personType)

      if (dob) {
        form.setFieldValue('dob', dayjs(dob, 'DD/MM/YYYY'))
      }

      const customFieldMap = new Map(customFields.map((field) => [field.customFieldName, field.customFieldValue]))

      customFieldPersonList.forEach((fieldName) => {
        form.setFieldValue(fieldName, customFieldMap.get(fieldName) || '')
      })

      if (faces?.[0]) {
        const { objectUrl = '', uuidFace = '' } = faces[0]
        setAvatar(objectUrl)
        setPreviewImg(objectUrl)
        setUuidFace(uuidFace)
      }
    } catch {
      setOpenErrorModal(true)
    } finally {
      setIsLoading(false)
    }
  }

  const handleUpload = async (file: RcFile) => {
    try {
      const res = await uploadImage(file.type)

      const { postUrl, formData, bucketName, objectName } = res.object
      const data = new FormData()
      Object.entries(formData).forEach(([key, value]) => data.append(key, value as string))
      data.append('file', file)

      await uploadFileToPresignedUrl(postUrl, data)

      return { bucketName, objectName }
    } catch {
      showCustomNotification({
        status: 'error',
        message: 'Tải ảnh thất bại!',
        description: 'Lỗi trong quá trình tải ảnh lên storage!'
      })
    }
  }

  const handleSave = async () => {
    await form.validateFields()
    try {
      setIsLoading(true)
      const values = form.getFieldsValue()
      let body
      if (imageUrl) {
        const face = await handleUpload(imageUrl as RcFile)
        body = setParams(values, activePerson, listCustomFields, face)
      } else {
        body = setParams(values, activePerson, listCustomFields)
      }

      if (uuidPerson) {
        if (avatar.trim() === '' && body.faces.length > 0) {
          await addFace({ bucketName: body.faces[0].bucketName, objectName: body.faces[0].objectName, uuidPerson })
        } else if (imageUrl) {
          await updateFaces(uuidFace, { bucketName: body.faces[0].bucketName, objectName: body.faces[0].objectName })
        }

        if (body.customFields.length > 0) {
          await updateCustomFieldsPerson(uuidPerson, body.customFields)
        }
        const res = await updatePerson(uuidPerson, body)
        if (res.message === MESSAGE_STATUS.SUCCESS) {
          navigate('/manage-user')
          showCustomNotification({
            status: 'success',
            message: 'Thành công!',
            description: `Sửa thông tin ${personTypeName.toLowerCase()} thành công!`
          })
        }
      } else {
        const res = await addNewPerson(body)
        if (res.message === MESSAGE_STATUS.SUCCESS) {
          navigate('/manage-user')
          showCustomNotification({
            status: 'success',
            message: 'Thành công!',
            description: `Thêm mới ${personTypeName.toLowerCase()} thành công!`
          })
          form.resetFields()
          setPreviewImg('')
        }
      }
    } catch (error: unknown) {
      const axiosError = error as AxiosError<{ message: string }>
      if (uuidPerson) {
        showCustomNotification({
          status: 'error',
          message: 'Thất bại!',
          description: `Sửa thông tin ${personTypeName.toLowerCase()} thất bại!`
        })
      } else
        showCustomNotification({
          status: 'error',
          message: 'Thất bại!',
          description:
            axiosError.response?.data?.message === MESSAGE_STATUS.USER_ID_INVALID
              ? 'ID đã tồn tại!'
              : `Thêm mới ${personTypeName.toLowerCase()} thất bại!`
        })
    } finally {
      setIsLoading(false)
    }
  }

  const handleChange = (info: any) => {
    const file = info.target.files[0]
    if (file && file.type.startsWith('image/')) {
      setImageUrl(file)
      const fileURL = URL.createObjectURL(file)
      setPreviewImg(fileURL)
    }
  }

  return (
    <>
      <ModalError
        handleCancel={() => {
          setOpenErrorModal(false)
          navigate('/manage-user')
        }}
        isModalOpen={openErrorModal}
      />
      <LayoutCotent
        title={[
          'Quản lý người dùng',
          personTypeName,
          uuidPerson ? `Sửa thông tin ${personTypeName.toLowerCase()}` : `Thêm mới ${personTypeName.toLowerCase()}`
        ]}
        btnBack={true}
      >
        <Spin spinning={isLoading}>
          <div className='xs:px-5 lg:flex justify-items-stretch xl:w-3/4'>
            <div className='lg:w-[236px] p-5 gird flex justify-center'>
              <div className='relative h-[120px] w-[120px] border rounded-full'>
                <label htmlFor='avatar-upload' className='cursor-pointer block w-full h-full  overflow-hidden'>
                  {previewImg ? (
                    <div className='w-full h-full rounded-full overflow-hidden flex items-center justify-center'>
                      <Image
                        src={previewImg}
                        preview={false}
                        className='rounded-full w-full h-full'
                        style={{ height: 120, width: 120 }}
                      />
                    </div>
                  ) : (
                    <Svg src='/assets/icons/common/image-avatar.svg' className='w-full h-full' />
                  )}
                </label>
                <input id='avatar-upload' type='file' accept='image/*' className='hidden' onChange={handleChange} />
              </div>
            </div>
            <Form
              form={form}
              requiredMark={false}
              layout='vertical'
              className='flex gap-5 w-full justify-center'
              initialValues={{ status: 1 }}
            >
              <Row gutter={[20, 0]} className='w-full'>
                <Col span={24} md={12}>
                  {nameField}
                </Col>
                <Col span={24} md={12}>
                  {emailField}
                </Col>
                <Col span={24} md={12}>
                  {numberIdField(!!uuidPerson)}
                </Col>
                {activePerson === TYPE_PERSON['Sinh viên'] && (
                  <Col span={24} md={12}>
                    {courseField(listGroups)}
                  </Col>
                )}
                <Col span={24} md={12}>
                  {dateField}
                </Col>
                <Col span={24} md={12}>
                  {genderField}
                </Col>
                {activePerson !== TYPE_PERSON['Sinh viên'] && (
                  <Col span={24} md={12}>
                    {unitField}
                  </Col>
                )}
                {activePerson === TYPE_PERSON['Sinh viên'] && (
                  <Col span={24} md={12}>
                    {schoolField}
                  </Col>
                )}
                {activePerson === TYPE_PERSON['Sinh viên'] && (
                  <Col span={24} md={12}>
                    {specializationField}
                  </Col>
                )}
                <Col span={24} md={12}>
                  {phoneNumberField}
                </Col>
                <Col span={24} md={12}>
                  {statusField(activePerson)}
                </Col>
                <Col span={24}>
                  <div className='flex gap-5 justify-center lg:justify-normal'>
                    <Button
                      variant='outlined'
                      color='primary'
                      size='large'
                      className={isMobile ? 'flex-1' : undefined}
                      style={{ width: 126 }}
                      onClick={() => {
                        navigate('/manage-user')
                      }}
                    >
                      Hủy
                    </Button>
                    <Button
                      type='primary'
                      size='large'
                      className={isMobile ? 'flex-1' : undefined}
                      style={{ width: 126 }}
                      onClick={handleSave}
                    >
                      Lưu
                    </Button>
                  </div>
                </Col>
              </Row>
            </Form>
          </div>
        </Spin>
      </LayoutCotent>
    </>
  )
}

export default ManageInfomationUser
