import { ModalProps } from '@/types/common'
import { Button, Divider, message, Modal, Spin } from 'antd'
import { useEffect, useRef, useState } from 'react'
import { showCustomNotification } from '@/common/Notification'
import { uploadFileToPresignedUrl, uploadImage } from '@/services/common'
import { RcFile } from 'antd/es/upload'
import useAuthStore from '@/stores/auth'
import { ImageParams } from '@/types/person'
import { collectImage } from '@/services/person'
import { MESSAGE_STATUS } from '@/constants'
import useLayoutStore from '@/stores/layout'
import Svg from '../Svg'

const WebcamModal = ({ isModalOpen, handleCancel, isLoading }: ModalProps) => {
  const [dataCapture, setDataCapture] = useState<any>({})
  const [loadingCamera, setLoadingCamera] = useState(false)
  const [isChange, setIsChange] = useState<'upload' | 'camera' | ''>()
  const videoRef = useRef<HTMLVideoElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const streamRef = useRef<MediaStream | null>(null)
  const { user } = useAuthStore()
  const { isMobile } = useLayoutStore()

  useEffect(() => {
    if (isModalOpen) {
      setIsChange('')
      setDataCapture({})
    } else {
      stopCamera()
    }
  }, [isModalOpen])

  const startCamera = async () => {
    setLoadingCamera(true)
    setDataCapture({})
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ video: true })
      if (videoRef.current) {
        videoRef.current.srcObject = stream
        videoRef.current.play()
      }
      streamRef.current = stream
    } catch {
      message.error('Vui lòng cấp quyền truy cập camera!')
      handleCancel({} as any)
    } finally {
      setLoadingCamera(false)
    }
  }

  const stopCamera = () => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach((track) => track.stop())
      streamRef.current = null
    }
  }

  const handleTakePicture = async () => {
    setIsChange('camera')
    if (!streamRef.current) {
      await startCamera()
      return
    }

    if (!videoRef.current || !canvasRef.current) return

    const context = canvasRef.current.getContext('2d')
    if (context) {
      canvasRef.current.width = videoRef.current.videoWidth
      canvasRef.current.height = videoRef.current.videoHeight
      context.drawImage(videoRef.current, 0, 0, videoRef.current.videoWidth, videoRef.current.videoHeight)
      canvasRef.current.toBlob((blob) => {
        if (!blob) return
        const file = new File([blob], `captured-${Date.now()}.jpeg`, {
          type: 'image/jpeg',
          lastModified: Date.now()
        })
        const rcFile = Object.assign(file, {
          uid: Date.now().toString()
        }) as RcFile
        setDataCapture({ capturedFile: rcFile })
      }, 'image/jpeg')
    }

    stopCamera()
  }

  const handleRetake = () => {
    startCamera()
  }

  const handleUploadImage = async (info: any) => {
    setIsChange('upload')
    stopCamera()
    const file = info.target.files[0]
    if (!file || !file.type.startsWith('image/')) {
      showCustomNotification({
        status: 'error',
        message: 'Tải ảnh thất bại!',
        description: "Vui lòng tải ảnh với đuôi mở rộng '.jpg', '.png' hoặc '.jpeg'"
      })
      return
    }
    const rcFile = Object.assign(file, {
      uid: Date.now().toString()
    }) as RcFile
    setDataCapture({ capturedFile: rcFile })
  }

  const handleUpload = async (file: RcFile) => {
    try {
      const res = await uploadImage(file.type)
      const { postUrl, formData, bucketName, objectName } = res.object
      const data = new FormData()
      Object.entries(formData).forEach(([key, value]) => data.append(key, value as string))
      data.append('file', file)
      await uploadFileToPresignedUrl(postUrl, data)
      return { bucketName, objectName }
    } catch {
      showCustomNotification({
        status: 'error',
        message: 'Tải ảnh thất bại!',
        description: 'Lỗi trong quá trình tải ảnh lên storage!'
      })
    }
  }

  const handleConfirm = async () => {
    try {
      if (dataCapture.capturedFile) {
        const result = await handleUpload(dataCapture.capturedFile)
        if (result) {
          const { bucketName, objectName } = result
          const body: ImageParams = {
            bucketName,
            objectName,
            uuidPerson: user.uuidPerson
          }
          const res = await collectImage(body)
          if (res.message === MESSAGE_STATUS.SUCCESS) {
            showCustomNotification({
              status: 'success',
              message: 'Thành công!',
              description: 'Ảnh sẽ được gửi tới quản trị viên phê duyệt!'
            })
          }
        }
      }
    } catch {
      showCustomNotification({
        status: 'error',
        message: 'Thất bại!',
        description: 'Có lỗi xảy ra!'
      })
    } finally {
      handleCancel({} as any)
    }
  }

  const imagePreview = dataCapture.capturedFile instanceof Blob ? URL.createObjectURL(dataCapture.capturedFile) : null

  const footerModal = (
    <div className='flex items-center justify-center gap-4 p-2 pt-4'>
      <label className='relative inline-block md:min-w-[115px] cursor-pointer'>
        <input
          type='file'
          accept='image/*'
          onChange={handleUploadImage}
          className='absolute inset-0 w-full h-full opacity-0 cursor-pointer z-10'
        />
        <Button
          className='w-full bg-neutral-9 hover:opacity-70 cursor-pointer'
          size='large'
          variant='outlined'
          color='primary'
        >
          {isMobile ? (
            <Svg src='/assets/icons/common/export-excel.svg' className='h-5 w-5 cursor-pointer' />
          ) : (
            <span className='cursor-pointer'>Tải ảnh lên</span>
          )}
        </Button>
      </label>
      <Button
        onClick={isChange === 'camera' && imagePreview ? handleRetake : handleTakePicture}
        className='min-w-[115px] bg-neutral-9 hover:opacity-70'
        size='large'
        variant='outlined'
        color='primary'
      >
        {isChange === 'camera' && imagePreview ? 'Chụp lại' : 'Chụp ảnh'}
      </Button>
      <Button
        loading={isLoading}
        onClick={handleConfirm}
        type='primary'
        size='large'
        className='min-w-[115px] shadow-none'
        disabled={imagePreview ? false : true}
      >
        Lưu
      </Button>
    </div>
  )

  const renderModalContent = () => {
    if (isChange) {
      return imagePreview ? (
        <div className='output'>
          <img className='rounded-lg' src={imagePreview} id='photo' alt='Ảnh chụp sẽ hiển thị ở đây.' />
        </div>
      ) : (
        <video className='rounded-lg' playsInline ref={videoRef} id='video' autoPlay>
          Video stream not available!
        </video>
      )
    }
    return (
      <div className='flex h-[250px] items-center justify-center rounded-lg bg-neutral-7 text-center text-primary'>
        Vui lòng chọn "Chụp ảnh" để bật camera
        <br />
        hoặc "Tải ảnh lên" để chọn ảnh từ thiết bị.
      </div>
    )
  }

  return (
    <Modal open={isModalOpen} title='Camera' footer={footerModal} onCancel={handleCancel} afterClose={stopCamera}>
      <Divider className='my-4' />
      <Spin spinning={loadingCamera}>
        {renderModalContent()}
        <canvas ref={canvasRef} style={{ display: 'none' }} />
      </Spin>
    </Modal>
  )
}

export default WebcamModal
