import { <PERSON><PERSON>, <PERSON>, Divider, Pagination, Row, Select, Table, TableProps } from 'antd'
import Svg from '../Svg'
import { initialProperty, MAX_SIZE_PAGE, pageSize, TYPE_PERSON } from '@/types/components'
import { AttendanceRecord } from '@/types/session'
import { Person } from '@/types/person'
import { AttendanceStatusField, CheckinStatusField } from '../StatusField'
import { ResponseList } from '@/types/common'
import { useCallback, useEffect, useMemo, useState } from 'react'
import {
  addMultipleAttendanceRecord,
  getListAttendanceRecords,
  getPreviewAttendanceRecord,
  updateMultipleAttendanceRecord,
  updateSessionAttendance
} from '@/services/session'
import { MESSAGE_STATUS } from '@/constants'
import { showCustomNotification } from '@/common/Notification'
import RecordInfo from '../RecordInfo'
import dayjs from 'dayjs'
import { useParams } from 'react-router-dom'
import { Record } from '../ManageClass/RecordComponents/SessionAttendance'
import { setAddAttendanceRecordParams, setUpdateAttendanceRecordParams } from '@/helpers/configData'
import ModalCheckinRecordInfo from '../Modals/CheckinRecordInfoModal'
import useLayoutStore from '@/stores/layout'
import { isAdmin } from '@/utils/feature-flags'
import useAuthStore from '@/stores/auth'

const AttendaceRecordTable = ({
  uuidSession,
  isHiddened,
  isReload,
  setIsReload,
  attendanceRecord,
  setIsLoading,
  refresh
}: {
  uuidSession?: string
  attendanceRecord: Record
  isReload: boolean
  setIsReload: (e: boolean) => void
  setIsLoading: (e: boolean) => void
  isHiddened: boolean
  refresh: () => void
}) => {
  const { uuidGroup } = useParams()
  const { isMobile, isTablet } = useLayoutStore()
  const { user } = useAuthStore()

  const [personAttendace, setPersonAttendace] = useState<AttendanceRecord>()
  const [dataSource, setDataSource] = useState<ResponseList<AttendanceRecord>>()
  const [listAttendanceRecord, setListAttendanceRecord] = useState<AttendanceRecord[]>([])
  const [openModal, setOpenModal] = useState<boolean>(false)
  const [pageProperty, setPageProperty] = useState<{
    page: number
    maxSize: number
    totalElement: number
  }>(initialProperty)

  const getAttendanceRecords = useCallback(async () => {
    try {
      setIsLoading(true)
      const resRecords = await getListAttendanceRecords({
        ...pageProperty,
        uuidSession: uuidSession,
        uuidSessionAttendance:
          attendanceRecord.uuidSessionAttendance !== 'action' && attendanceRecord.uuidSessionAttendance !== ''
            ? attendanceRecord.uuidSessionAttendance
            : undefined
      })
      if (resRecords.message === MESSAGE_STATUS.SUCCESS) {
        if (!isHiddened) {
          setDataSource(resRecords.object)
          setListAttendanceRecord([])
        } else {
          setListAttendanceRecord(resRecords.object.data)
        }
      }
    } catch {
      showCustomNotification({
        status: 'error',
        message: 'Thất bại!',
        description: 'Có lỗi xảy ra!'
      })
    } finally {
      setIsLoading(false)
    }
  }, [setIsLoading, pageProperty, uuidSession, attendanceRecord, isHiddened])

  const getPreviewRecords = useCallback(async () => {
    try {
      if (
        !uuidGroup ||
        !attendanceRecord.startTime ||
        !attendanceRecord.endTime ||
        !isHiddened ||
        attendanceRecord.isDisabled
      )
        return

      setIsLoading(true)
      const resRecords = await getPreviewAttendanceRecord({
        page: 1,
        maxSize: MAX_SIZE_PAGE,
        startTime: attendanceRecord.startTime.format('DD/MM/YYYY HH:mm:ss'),
        endTime: attendanceRecord.endTime.format('DD/MM/YYYY HH:mm:ss'),
        uuidGroup: uuidGroup
      })
      if (resRecords.message === MESSAGE_STATUS.SUCCESS) {
        setDataSource(resRecords.object)
        setIsReload(false)
      }
    } catch {
      showCustomNotification({
        status: 'error',
        message: 'Thất bại!',
        description: 'Có lỗi xảy ra!'
      })
    } finally {
      setIsLoading(false)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [attendanceRecord, isHiddened, setIsLoading, uuidGroup])

  useEffect(() => {
    getAttendanceRecords()
    if (user.personType !== TYPE_PERSON['Sinh viên']) {
      getPreviewRecords()
    }
  }, [getAttendanceRecords, getPreviewRecords, isReload, user.personType])

  const dataTable = useMemo(() => {
    return (
      dataSource?.data.map((record) => {
        const attendanceInfo = listAttendanceRecord.find((attendance) => attendance.uuidPerson === record.uuidPerson)
        return {
          ...record,
          attendanceStatus: attendanceInfo?.attendanceStatus ?? record.attendanceStatus,
          checkInStatus: attendanceInfo?.checkInStatus ?? record.checkInStatus,
          approvalReason: attendanceInfo?.approvalReason ?? record.approvalReason,
          checkInRecord: attendanceInfo?.checkInRecord ?? record.checkInRecord
        }
      }) || []
    ) // Fallback to an empty array if dataSource.data is undefined
  }, [listAttendanceRecord, dataSource?.data])

  const findDiffPersons = useCallback((persons: AttendanceRecord[], attendedPersons: AttendanceRecord[]) => {
    const attendedUuids = new Set(attendedPersons.map((person) => person.uuidPerson))
    return persons.filter((person) => !attendedUuids.has(person.uuidPerson))
  }, [])

  const findSamePersons = useCallback((persons: AttendanceRecord[], attendedPersons: AttendanceRecord[]) => {
    const attendedUuids = new Set(persons.map((person) => person.uuidPerson))
    return attendedPersons.filter((person) => attendedUuids.has(person.uuidPerson))
  }, [])

  const handleConfirm = async () => {
    try {
      if (
        !attendanceRecord.uuidSessionAttendance ||
        !attendanceRecord.startTime ||
        !attendanceRecord.endTime ||
        !dataTable ||
        !dataSource
      )
        return
      setIsLoading(true)

      const dataDiff = findDiffPersons(dataSource.data, listAttendanceRecord)
      const dataSame = findSamePersons(dataSource.data, listAttendanceRecord)

      // Kiểm tra nếu cần thêm mới bản ghi điểm danh
      if (dataDiff.length > 0) {
        const addAttendaceRecordParams = setAddAttendanceRecordParams(
          attendanceRecord.uuidSessionAttendance,
          dataDiff,
          dataTable
        )
        await addMultipleAttendanceRecord(addAttendaceRecordParams)
      }

      const updateAttendaceRecord = setUpdateAttendanceRecordParams(dataSame, dataTable)
      const updateRecords = updateMultipleAttendanceRecord(updateAttendaceRecord)

      const updateSession = updateSessionAttendance(attendanceRecord.uuidSessionAttendance, {
        startTime: dayjs(attendanceRecord.startTime).format('DD/MM/YYYY HH:mm:ss'),
        endTime: dayjs(attendanceRecord.endTime).format('DD/MM/YYYY HH:mm:ss'),
        status: 2
      })

      await Promise.all([updateSession, updateRecords])

      showCustomNotification({
        status: 'success',
        message: 'Thành công!',
        description: `Cập nhật bản ghi điểm danh thành công.`
      })
    } catch {
      showCustomNotification({
        status: 'error',
        message: 'Thất bại!',
        description: ` Cập nhật bản ghi điểm danh thất bại.`
      })
    } finally {
      refresh()
      setIsLoading(false)
    }
  }

  const columns: TableProps<AttendanceRecord>['columns'] = [
    {
      title: 'STT',
      key: 'string',
      width: 48,
      align: 'center',
      render: (_, __, index) => index + 1
    },
    {
      title: 'Ảnh đại diện',
      dataIndex: 'person',
      key: 'person',
      width: 120,
      render: (value: Person) => (
        <div className='flex justify-center'>
          {value?.faces && value?.faces.length > 0 ? (
            <img src={value.faces[0].objectUrl} alt='avatar' className='w-10 h-10 rounded-full object-cover' />
          ) : (
            <Svg src='/assets/icons/common/image-avatar.svg' className='w-10 h-10' />
          )}
        </div>
      )
    },
    {
      title: 'Họ tên',
      dataIndex: 'person',
      key: 'person',
      render: (value: Person, record) => (
        <p
          className='text-text-3 underline cursor-pointer'
          onClick={() => {
            setPersonAttendace(record)
            setOpenModal(true)
          }}
        >
          {value.fullName}
        </p>
      )
    },

    {
      title: 'ID',
      dataIndex: 'person',
      key: 'person',
      render: (value: Person) => value.personCode.toLocaleUpperCase()
    },
    ...(isHiddened
      ? [
          {
            title: 'Trạng thái',
            dataIndex: 'checkInStatus',
            key: 'checkInStatus',
            render: (status: any) => <CheckinStatusField status={status} />
          }
        ]
      : []),
    {
      title: 'Xác nhận của giáo viên',
      dataIndex: 'attendanceStatus',
      key: 'attendanceStatus',
      render: (status) => <AttendanceStatusField status={status} />
    },
    ...(isHiddened
      ? [
          {
            title: 'Lý do',
            dataIndex: 'approvalReason',
            key: 'approvalReason',
            render: (value: any) => (value?.trim() === '' ? '-' : value)
          }
        ]
      : [])
  ]
  return (
    <>
      {(isMobile || isTablet) && personAttendace?.uuidPerson && (
        <ModalCheckinRecordInfo
          isModalOpen={openModal}
          handleCancel={() => setOpenModal(false)}
          personAttendace={personAttendace}
          sessionAttendaceStatus={attendanceRecord.isDisabled || !isHiddened}
          setPersonAttendace={setPersonAttendace}
          setData={setDataSource}
          listAttendanceRecord={listAttendanceRecord}
          setListAttendanceRecord={setListAttendanceRecord}
        />
      )}
      <Row gutter={[16, 16]}>
        <Col span={personAttendace?.uuidPerson && !isMobile && !isTablet ? 16 : 24}>
          {isHiddened && <h1 className='mb-5 text-lg font-semibold'>Danh sách sinh viên</h1>}
          <Table
            columns={columns}
            dataSource={dataTable}
            pagination={false}
            scroll={{ x: 700, y: 600 }}
            rowKey={(record) => record.uuidPerson}
          />
          {
            <div className='mt-4 flex max-sm:flex-col items-center justify-between gap-4'>
              <div className='flex items-center gap-2'>
                <p className='text-xs font-normal text-gray-400'>Số bản ghi mỗi trang </p>
                <Select
                  size='small'
                  options={pageSize}
                  value={pageProperty.maxSize}
                  onChange={(e) => setPageProperty({ ...pageProperty, maxSize: e })}
                />
              </div>
              <Pagination
                onChange={(page) => {
                  setPageProperty({ ...pageProperty, page })
                }}
                className='mt-4'
                align='end'
                current={dataSource?.page}
                defaultCurrent={1}
                showSizeChanger={false}
                pageSize={dataSource?.maxSize}
                defaultPageSize={10}
                total={dataSource?.totalElement}
              />
            </div>
          }
        </Col>
        {personAttendace?.uuidPerson && !isMobile && !isTablet && (
          <Col span={8}>
            <h1 className='mb-2 text-lg font-semibold'>Thông tin điểm danh</h1>
            <RecordInfo
              listAttendanceRecord={listAttendanceRecord}
              personAttendace={personAttendace}
              sessionAttendaceStatus={attendanceRecord.isDisabled || !isHiddened}
              setPersonAttendace={setPersonAttendace}
              setData={setDataSource}
              setListAttendanceRecord={setListAttendanceRecord}
            />
          </Col>
        )}
      </Row>
      {isAdmin(user) && isHiddened && (
        <>
          <Divider className='hidden xs:block' />
          <Row>
            <Col span={24}>
              <div className='flex items-center justify-center mt-4 gap-5'>
                <Button
                  type='primary'
                  size='large'
                  style={{ width: 160 }}
                  className='max-xs:!w-full'
                  onClick={handleConfirm}
                  disabled={attendanceRecord.isDisabled}
                >
                  Xác nhận lớp học
                </Button>
              </div>
            </Col>
          </Row>
        </>
      )}
    </>
  )
}

export default AttendaceRecordTable
