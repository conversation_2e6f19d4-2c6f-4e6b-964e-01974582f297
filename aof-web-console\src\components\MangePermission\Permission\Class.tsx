import { showCustomNotification } from '@/common/Notification'
import Svg from '@/components/Svg'
import { MESSAGE_STATUS } from '@/constants'
import {
  getDetailFeatureGrantPerson,
  getListPermissionToGroup,
  getPermissionToGroup,
  permissionToGroup,
  updatePermissionToGroup
} from '@/services/auth'
import { getListGroups } from '@/services/group'
import { useInfoStore } from '@/stores/info'
import { classList, MAX_SIZE_PAGE } from '@/types/components'
import { Group } from '@/types/group'
import { PermissionGroup, PermissionGroupResource, Resource } from '@/types/user'
import { Button, Form, Input, Radio, RadioChangeEvent, Select, Table, TableProps } from 'antd'
import { Key, useCallback, useEffect, useState } from 'react'

const initialState = {
  keySearch: '',
  groupType: 2,
  uuidAcademicYear: '',
  uuidSemester: ''
}

const Class = ({
  setIsLoading,
  permissionGroup,
  uuidPermissionGroup,
  uuidP
}: {
  setIsLoading: (value: boolean) => void
  uuidPermissionGroup: string
  permissionGroup: PermissionGroup
  uuidP?: string
}) => {
  const { loadingData, academicYears, semesters, loadAcademicYears, loadSemesters } = useInfoStore()

  const [value, setValue] = useState(1)
  const [selectedRows, setSelectedRows] = useState<string[]>([])
  const [listGroup, setListGroup] = useState<Group[]>()
  const [listResource, setListResource] = useState<Resource[]>()
  const [listPermissionGroup, setListPermissionGroup] = useState<PermissionGroupResource[]>()

  const [params, setParams] = useState(initialState)

  useEffect(() => {
    if (academicYears.length === 0) {
      loadAcademicYears()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [academicYears])

  const getData = useCallback(async () => {
    if (value !== 3) return
    try {
      setIsLoading(true)
      const res = await getListGroups({
        page: 1,
        maxSize: MAX_SIZE_PAGE,
        countStudent: 0,
        ...params
      })
      if (res.message === MESSAGE_STATUS.SUCCESS) {
        setListGroup(res.object.data)
      }
    } catch {
      showCustomNotification({
        status: 'error',
        message: 'Thất bại',
        description: 'Lấy dữ liệu thất bại!'
      })
    } finally {
      setIsLoading(false)
    }
  }, [params, setIsLoading, value])

  const getDetailFeaturePerson = useCallback(async () => {
    if (!uuidP) return
    try {
      setIsLoading(true)
      const res = await getDetailFeatureGrantPerson(uuidP, 1)
      if (res.message === MESSAGE_STATUS.SUCCESS) {
        setValue(res.object.scopeType)
        setListResource(res.object.resources)
      }
    } catch {
      showCustomNotification({
        status: 'error',
        message: 'Thất bại',
        description: 'Lấy dữ liệu thất bại!'
      })
    } finally {
      setIsLoading(false)
    }
  }, [uuidP, setIsLoading])

  const getListPermissionGroup = useCallback(async () => {
    if (!uuidPermissionGroup) return
    try {
      setIsLoading(true)
      const res = await getListPermissionToGroup({
        page: 1,
        maxSize: MAX_SIZE_PAGE,
        uuidPermissionGroup
      })
      if (res.message === MESSAGE_STATUS.SUCCESS) {
        setListPermissionGroup(res.object.data)
        setValue(res.object.data[0]?.scopeType || 1)
        const uuidPermissionGroupResource = res.object.data[0]?.uuidPermissionGroupResource
        if (uuidPermissionGroupResource) {
          const res = await getPermissionToGroup(uuidPermissionGroupResource)
          setSelectedRows(res.object.uuidResources as unknown as string[])
        }
      }
    } catch {
      showCustomNotification({
        status: 'error',
        message: 'Thất bại',
        description: 'Lấy dữ liệu thất bại!'
      })
    } finally {
      setIsLoading(false)
    }
  }, [uuidPermissionGroup, setIsLoading])

  const handleSubmit = async () => {
    setIsLoading(true)
    try {
      let res
      const uuidPermissionGroupResource = listPermissionGroup?.[0]?.uuidPermissionGroupResource
      const uuidResources = selectedRows || []
      if (uuidPermissionGroupResource) {
        res = await updatePermissionToGroup(uuidPermissionGroupResource, {
          scopeType: value,
          uuidResources: uuidResources
        })
      } else {
        const body = {
          uuidPermissionGroup: uuidPermissionGroup || '',
          resourceType: 1,
          scopeType: value,
          uuidResources
        }
        res = await permissionToGroup(body)
      }
      if (res.message === MESSAGE_STATUS.SUCCESS) {
        showCustomNotification({
          status: 'success',
          message: 'Thành công',
          description: 'Chỉnh sửa quyền lớp học thành công'
        })
      }
      await getListPermissionGroup()
    } catch {
      showCustomNotification({
        status: 'error',
        message: 'Thất bại',
        description: 'Chỉnh sửa quyền lớp học thất bại!'
      })
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    getDetailFeaturePerson()
    getListPermissionGroup()
    if (params.uuidAcademicYear) {
      loadSemesters(params.uuidAcademicYear)
    }
  }, [getListPermissionGroup, getDetailFeaturePerson, loadSemesters, params.uuidAcademicYear])

  useEffect(() => {
    getData()
  }, [getData])

  const onChange = (e: RadioChangeEvent) => {
    setValue(e.target.value)
  }

  const rowSelection = {
    selectedRowKeys: selectedRows,
    onChange: (keys: Key[]) => {
      setSelectedRows(keys as string[])
    }
  }

  const columns: TableProps<Group>['columns'] = [
    {
      title: ' ',
      dataIndex: 'groupName',
      key: 'groupName'
    }
  ]
  return (
    <>
      <Radio.Group
        className='flex flex-col gap-2 [&.ant-radio-wrapper-disabled]:opacity-100'
        onChange={uuidP || !permissionGroup.allowModification ? undefined : onChange}
        defaultValue={1}
        value={value}
        options={[
          { value: 1, label: 'Chỉ lớp được phân công' },
          { value: 2, label: 'Tất cả các lớp' },
          { value: 3, label: 'Tùy chọn' }
        ]}
      />
      {value === 3 && (
        <div>
          {uuidP ? (
            <ul className='list-disc pl-10 max-h-[300px] overflow-auto'>
              {listResource?.map((r) => <li className='py-1'>{r.resourceName}</li>)}
            </ul>
          ) : (
            <div className='bg-background-card p-1 rounded-lg w-full lg:w-2/3 mt-2'>
              <Form layout='vertical' className='flex flex-row gap-2 px-5 py-2' hidden={uuidP ? false : true}>
                <Form.Item label={<p>Năm học</p>} className='w-full mb-0'>
                  <Select
                    size='large'
                    placeholder='Chọn năm'
                    options={[
                      { label: 'Tất cả', value: '' },
                      ...academicYears.map((item) => ({
                        label: item.name,
                        value: item.uuidAcademicYear
                      }))
                    ]}
                    value={params.uuidAcademicYear}
                    onChange={(e) => setParams((pre) => ({ ...pre, uuidAcademicYear: e }))}
                  />
                </Form.Item>
                <Form.Item label={<p>Kỳ học</p>} className='w-full mb-0'>
                  <Select
                    size='large'
                    placeholder='Chọn kỳ'
                    loading={loadingData}
                    options={[
                      { label: 'Tất cả', value: '' },
                      ...semesters.map((item) => ({
                        label: item.name,
                        value: item.uuidSemester
                      }))
                    ]}
                    value={params.uuidSemester}
                    onChange={(e) => setParams((pre) => ({ ...pre, uuidSemester: e }))}
                  />
                </Form.Item>
                <Form.Item label={<p>Phân loại</p>} className='w-full mb-0'>
                  <Select
                    size='large'
                    placeholder='Phân loại'
                    options={[
                      ...classList.map((item, index) => ({
                        label: item,
                        value: index + 2
                      }))
                    ]}
                    value={params.groupType}
                    onChange={(e) => setParams((pre) => ({ ...pre, groupType: e }))}
                  />
                </Form.Item>
                <Form.Item label={<p>Tìm kiếm</p>} className='w-full mb-0'>
                  <Input
                    size='large'
                    placeholder='Tìm kiếm'
                    onChange={(e) => setParams((pre) => ({ ...pre, keySearch: e.target.value }))}
                    suffix={<Svg src='/assets/icons/common/search.svg' className='w-5 h-5 text-neutral-4' />}
                  />
                </Form.Item>
              </Form>
              <Table
                dataSource={listGroup}
                showHeader={false}
                rowSelection={rowSelection}
                pagination={false}
                columns={columns}
                scroll={{ y: 330 }}
                rowKey={(record) => record.uuidGroup}
              />
            </div>
          )}
        </div>
      )}
      <Button
        type='primary'
        className='mt-5'
        size='large'
        style={{ width: '128px' }}
        hidden={uuidP || !permissionGroup.allowModification ? true : false}
        onClick={handleSubmit}
      >
        Lưu
      </Button>
    </>
  )
}

export default Class
