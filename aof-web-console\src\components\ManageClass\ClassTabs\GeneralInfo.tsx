/* eslint-disable react-hooks/exhaustive-deps */
import { Button, Col, Form, Row, Table } from 'antd'
import {
  classIdField,
  classNameField,
  numberCreditsField,
  numberLessonsField,
  numberStudentsField,
  semesterField,
  statusClassField,
  timeField,
  yearField
} from '../../InputsField'
import { useEffect, useState, useMemo } from 'react'
import { Group } from '@/types/group'
import { useInfoStore } from '@/stores/info'
import { CalendarEvent } from '@/types/calendar'
import dayjs from 'dayjs'
import { setGroupParams } from '@/helpers/configData'
import { getCustomFields, updateCustomFieldsGroup } from '@/services/common'
import { ClassType, customFieldGroupList, EntityType, TYPE_CLASS_CREDIT } from '@/types/components'
import { MESSAGE_STATUS } from '@/constants'
import { CustomField } from '@/types/common'
import { showCustomNotification } from '@/common/Notification'
import { updateGroup } from '@/services/group'
import { Location } from '@/types/room_device'
import useLayoutStore from '@/stores/layout'
import { Lecturer } from '@/types/user'
import useAuthStore from '@/stores/auth'
import { checkFeature, FF_DEV_0041 } from '@/utils/feature-flags'

interface Props {
  typeName: ClassType
  isUpdate: boolean
  classData: Group
  calendarData: CalendarEvent[]
  setIsLoading: (e: boolean) => void
  refreshData: () => void
}

const GeneralInformation = ({ typeName, isUpdate, classData, calendarData, setIsLoading, refreshData }: Props) => {
  const [form] = Form.useForm()
  const { loadingData, academicYears, semesters, loadAcademicYears, loadSemesters } = useInfoStore()
  const { user, featFlagsAction } = useAuthStore()
  const isFFA = (code: string) => checkFeature(code, user, featFlagsAction)

  const [listCustomFields, setListCustomFields] = useState<CustomField[]>([])

  const [onUpdate, setOnUpdate] = useState<boolean>(isUpdate ? !isUpdate : true)
  const { isMobile, isTablet } = useLayoutStore()

  useEffect(() => {
    if (!classData) return

    const { academicYear, startDate, endDate, customFields } = classData
    const time = [dayjs(startDate, 'DD/MM/YYYY'), dayjs(endDate, 'DD/MM/YYYY')]

    form.setFieldsValue({
      ...classData,
      academicYear: academicYear?.uuidAcademicYear,
      rangeTime: time,
      [customFieldGroupList[0]]: customFields?.find((field) => field.customFieldName === customFieldGroupList[0])
        ?.customFieldValue,
      [customFieldGroupList[1]]: customFields?.find((field) => field.customFieldName === customFieldGroupList[1])
        ?.customFieldValue
    })
    if (classData?.uuidGroup && classData.academicYear?.uuidAcademicYear) {
      loadSemesters(classData.academicYear?.uuidAcademicYear)
    }
  }, [classData])

  useEffect(() => {
    getListCustomFields()
    if (academicYears.length === 0) {
      loadAcademicYears()
    }
  }, [academicYears])

  const getListCustomFields = async () => {
    try {
      const res = await getCustomFields(EntityType.Group)
      if (res.message === MESSAGE_STATUS.SUCCESS) {
        setListCustomFields(res.object.data)
      }
    } catch {
      console.error('Failed to get list custom fields')
    }
  }

  const handleUpdate = async () => {
    await form.validateFields()
    try {
      setIsLoading(true)
      const values = form.getFieldsValue()
      const body = setGroupParams(values, classData?.groupType || 0, listCustomFields)
      const res = await updateGroup(classData?.uuidGroup || '', body)
      if (res.message === MESSAGE_STATUS.SUCCESS && body.customFields.length > 0) {
        await updateCustomFieldsGroup(classData?.uuidGroup || '', body.customFields)
      }
      refreshData()
      showCustomNotification({
        status: 'success',
        message: 'Thành công!',
        description: 'Cập nhật thông tin lớp thành công!'
      })
    } catch {
      showCustomNotification({
        status: 'error',
        message: 'Thất bại!',
        description: 'Cập nhật thông tin lớp thất bại!'
      })
    } finally {
      setIsLoading(false)
      setOnUpdate(true)
    }
  }

  const btnAction = () => {
    switch (onUpdate) {
      case true:
        return (
          <Button
            variant='outlined'
            color='primary'
            size='large'
            style={{ width: 126 }}
            className='max-xs:!w-full'
            onClick={() => setOnUpdate(false)}
          >
            Sửa
          </Button>
        )
      case false:
        return (
          <Button type='primary' size='large' className='max-xs:!w-full' style={{ width: 126 }} onClick={handleUpdate}>
            Lưu
          </Button>
        )
    }
  }

  const useTableColumns = (typeName: string, classData: any) => {
    return useMemo(
      () => [
        {
          title: 'Thời gian',
          dataIndex: typeName === 'Lớp học' ? 'timeSlot' : 'startTime',
          key: typeName === 'Lớp học' ? 'timeSlot' : 'startTime',
          ...(typeName === 'Lớp thi' && {
            render: (value: string) => dayjs(value, 'DD/MM/YYYY').format('DD/MM/YYYY')
          })
        },
        ...(typeName === 'Lớp thi'
          ? []
          : [
              {
                title: 'Thứ',
                dataIndex: 'dayOfWeek',
                key: 'dayOfWeek',
                width: isMobile || isTablet ? 200 : undefined
              }
            ]),

        {
          title: typeName === 'Lớp học' ? 'Giờ học' : 'Giờ thi',
          dataIndex: 'duration',
          key: 'duration'
        },
        {
          title: 'Phòng',
          dataIndex: 'location',
          key: 'location',
          render: (value: Location) => value?.name
        },
        {
          title: typeName === 'Lớp học' ? 'Giảng viên' : 'Cán bộ trông thi',
          dataIndex: 'lecturer',
          key: 'lecturer',
          render: () => {
            return classData.lecturers.map((lecturer: Lecturer) => (
              <p
                key={lecturer.uuidPerson}
                className='line-clamp-2'
              >{`${lecturer.fullName} - ${lecturer.personCode.toLocaleUpperCase()}`}</p>
            ))
          }
        }
      ],
      [typeName, classData]
    )
  }

  return (
    <div className='px-5 flex flex-col gap-5'>
      <div className='xl:w-2/3'>
        <Form
          form={form}
          requiredMark={false}
          layout='vertical'
          className='flex gap-5'
          initialValues={{ studentCount: 0 }}
        >
          <Row gutter={[20, 0]}>
            <Col span={isMobile ? 24 : 12}>{classNameField({ onUpdate })}</Col>
            <Col span={isMobile ? 24 : 12}>{classIdField({ onUpdate: true })}</Col>
            {classData.groupType === TYPE_CLASS_CREDIT && (
              <>
                <Col span={12}>{numberCreditsField({ onUpdate })}</Col>
                <Col span={12}>{numberLessonsField({ onUpdate })}</Col>
              </>
            )}
            <Col span={12}>{yearField({ onUpdate: true, academicYears, setSelectYear: () => {} })}</Col>
            <Col span={12}>{semesterField({ onUpdate, semesters, loadingData })}</Col>
            <Col span={isMobile ? 24 : 12}>{numberStudentsField}</Col>
            <Col span={isMobile ? 24 : 12}>{timeField({ onUpdate, isMobile })}</Col>
            <Col span={24}>{statusClassField({ onUpdate: true, groupType: classData.groupType })}</Col>
            {isFFA(FF_DEV_0041) && <Col span={isMobile ? 24 : 12}>{btnAction()}</Col>}
          </Row>
        </Form>
      </div>
      <h1 className='font-semibold text-lg'>Thời khóa biểu</h1>
      <Table
        columns={useTableColumns(typeName, classData)}
        dataSource={calendarData}
        pagination={false}
        rowKey={(record) => record.uuidCalendarEvent ?? `row-${Math.random()}`}
        scroll={{ x: isMobile ? 'max-content' : undefined }}
      />
    </div>
  )
}

export default GeneralInformation
