import { But<PERSON>, Col, Image, Row } from 'antd'
import Svg from './Svg'
import { ExamCheating } from '@/types/person'
import { ExamStatus } from '@/types/components'
import { removeMultipleExamCheating } from '@/services/person'
import { showCustomNotification } from '@/common/Notification'
import { MESSAGE_STATUS } from '@/constants'
import ModalDelete from './Modals/DeleteModal'
import { useState } from 'react'
import { BehaviorField } from './StatusField'
import useLayoutStore from '@/stores/layout'
import useAuthStore from '@/stores/auth'
import { checkFeature, FF_DEV_0019, FF_DEV_0020 } from '@/utils/feature-flags'

interface ViolationCardProps {
  data: ExamCheating
  status: boolean
  isSelected: boolean
  refreshData: () => void
  setSelectedCard: (e: any) => void
}

const ViolationCard = ({ data, refreshData, isSelected, setSelectedCard }: ViolationCardProps) => {
  const [openDeleteModal, setOpenDeleteModal] = useState<boolean>(false)
  const { isMobile } = useLayoutStore()
  const { user, featFlagsAction } = useAuthStore()
  const isFFA = (code: string) => checkFeature(code, user, featFlagsAction)

  const handleRemove = async () => {
    try {
      const res = await removeMultipleExamCheating([data.uuidExamCheating])
      if (res.message === MESSAGE_STATUS.SUCCESS) {
        setSelectedCard(null)
        refreshData()
        showCustomNotification({
          status: 'success',
          message: 'Thành công!',
          description: 'Xóa sự kiện thành công!'
        })
      }
    } catch {
      showCustomNotification({
        status: 'error',
        message: 'Thất bại!',
        description: 'Xóa sự kiện thất bại!'
      })
    } finally {
      setOpenDeleteModal(false)
    }
  }
  const confirmedPersons = data.persons.filter((p) => p.confirmed === 1)

  return (
    <>
      <ModalDelete
        isModalOpen={openDeleteModal}
        handleCancel={() => {
          setOpenDeleteModal(false)
        }}
        title='Xóa'
        image={data.imageUrl}
        handleOk={handleRemove}
        subTitle='Bạn có chắc chắn muốn xóa sự kiện này?'
      />
      <div
        className='border relative rounded-lg bg-background-card h-full'
        style={{ borderColor: isSelected ? '#00707E' : '#BDBDBD' }}
      >
        <div
          className={`p-4 max-sm:p-3 ${isSelected ? 'bg-[#E5F6F8]' : 'bg-background-1'} rounded-lg items-start gap-5 overflow-hidden h-full`}
        >
          <div className='flex items-start gap-5 max-sm:gap-3 w-full'>
            <div className='relative inline-block' onClick={(e) => e.stopPropagation()}>
              <Image
                src={data.thumbnailUrl}
                className='min-w-[220px] max-md:min-w-[130px] object-cover rounded-2xl'
                height={isMobile ? 130 : 170}
              />
              {isFFA(FF_DEV_0019) &&
                (data.cheatingStatus === 1 ? (
                  <Svg
                    src='/assets/icons/status/unconfirmed.svg'
                    className='h-7 w-7 absolute -top-1.5 -left-2 rounded-full bg-white p-1 shadow'
                  />
                ) : (
                  <Svg
                    src='/assets/icons/status/done.svg'
                    className='h-7 w-7 absolute -top-1.5 -left-2 rounded-full bg-white p-0.5 shadow'
                  />
                ))}
            </div>
            <div className='flex flex-col'>
              <Row gutter={[4, 4]}>
                <Col span={10}>
                  <p className='text-sm font-semibold text-neutral-2'>Thời gian vi phạm:</p>
                </Col>
                <Col span={14} className='text-neutral-2'>
                  {data.cheatedAt}
                </Col>
                <Col span={10}>
                  <p className='text-sm font-semibold text-neutral-2'>Hành vi:</p>
                </Col>
                <Col span={14} className='text-neutral-2'>
                  <BehaviorField status={data.cheatingType} />
                </Col>
                <Col span={10}>
                  <p className='text-sm font-semibold text-neutral-2'>Phòng thi:</p>
                </Col>
                <Col span={14} className='text-neutral-2'>
                  {data.location.name}
                </Col>
                <Col span={10}>
                  <p className='text-sm font-semibold text-neutral-2'>Sinh viên vi phạm:</p>
                </Col>
                <Col span={14} className='text-neutral-2'>
                  {data.persons.length > 0
                    ? confirmedPersons.length > 0
                      ? confirmedPersons.map(
                          (p) => `${p.person.fullName} - ${p.person.personCode?.toLocaleUpperCase()}`
                        )
                      : '---'
                    : 'Chưa xác định'}
                </Col>
              </Row>

              <Row gutter={[4, 4]} className='flex gap-3 pt-3 max-sm:justify-end'>
                {isFFA(FF_DEV_0019) && data.cheatingStatus === ExamStatus['Chưa xác nhận'] && (
                  <Button
                    variant='outlined'
                    color='primary'
                    className={isMobile ? 'w-1/2' : 'w-[120px]'}
                    size='large'
                    onClick={() => {
                      setSelectedCard(data)
                    }}
                  >
                    Xác nhận
                  </Button>
                )}
                {isFFA(FF_DEV_0020) && (
                  <Button
                    color='red'
                    variant='outlined'
                    size='large'
                    icon={<Svg src='/assets/icons/common/trash.svg' className='h-5 w-5' />}
                    onClick={(e) => {
                      e.stopPropagation()
                      setOpenDeleteModal(true)
                    }}
                  />
                )}
              </Row>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default ViolationCard
