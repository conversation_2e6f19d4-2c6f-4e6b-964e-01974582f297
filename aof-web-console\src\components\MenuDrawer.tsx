import { URL } from '@/types/components'
import { Drawer, Image, Menu, MenuProps } from 'antd'
import { useEffect, useState } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import { getMenu } from '@/layouts/Sider'
import useLayoutStore from '@/stores/layout'
import Svg from './Svg'
import useAuthStore from '@/stores/auth'
import { checkFeature, FF_DEV_0000, FF_DEV_0045 } from '@/utils/feature-flags'

const MenuDrawer = ({ open, setOpen }: { open: boolean; setOpen: (e: boolean) => void }) => {
  const navigate = useNavigate()
  const location = useLocation()

  const { isMobile } = useLayoutStore()
  const { user, featFlagsParent } = useAuthStore()
  const isFFP = (code: string) => checkFeature(code, user, featFlagsParent)

  const [currentPath, setCurrentPath] = useState<URL>(location.pathname as URL)

  useEffect(() => {
    const check = getMenu(user).find((path) => location.pathname.includes(path.url))
    if (check) {
      setCurrentPath(check.url)
      setOpen(false)
    }
  }, [location.pathname, setOpen, user])

  const onClose = () => {
    setOpen(false)
  }

  const filteredMenu = getMenu(user).filter(
    (feature) => isFFP(feature.actionCode) || feature.actionCode === FF_DEV_0045 || feature.actionCode === FF_DEV_0000
  )

  const menuItems: MenuProps['items'] = [
    ...filteredMenu.map((item) => ({
      key: item.url,
      label: item.label,
      icon: <Svg src={currentPath === item.url ? item.activeIcon : item.icon} className='h-5 w-5' />
    })),
    ...(isMobile
      ? [
          {
            key: 'language',
            label: (
              <div className='flex items-center justify-between gap-2'>
                <span>Ngôn ngữ</span>
                <div className='flex items-center gap-2'>
                  <div className='border rounded-full p-1 border-text-primary'>
                    <img src='/assets/icons/flag/vn-large.svg' alt='Vietnamese' className='h-5 w-5' />
                  </div>
                  <div className='border rounded-full p-1'>
                    <img src='/assets/icons/flag/en-large.svg' alt='English' className='h-5 w-5' />
                  </div>
                </div>
              </div>
            ),
            icon: <Svg src='/assets/icons/menu/language.svg' className='h-5 w-5' />
          }
        ]
      : [])
  ]

  return (
    <Drawer
      title={
        isMobile && <Image src='/assets/images/logo.png' className='max-h-12 w-50 cursor-pointer' preview={false} />
      }
      closable={false}
      onClose={onClose}
      open={open}
      width={312}
      placement='left'
    >
      <div className='flex mb-2 items-center'>
        <h1 className='uppercase text-sm font-semibold'>Menu</h1>
      </div>
      <Menu
        mode='inline'
        className='!border-none'
        items={menuItems}
        selectedKeys={[currentPath]}
        onSelect={(e: any) => {
          navigate(e.key)
          setCurrentPath(e.key)
        }}
        defaultSelectedKeys={[location.pathname]}
      />
    </Drawer>
  )
}

export default MenuDrawer
