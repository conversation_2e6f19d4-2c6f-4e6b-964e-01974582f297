import { But<PERSON>, Di<PERSON>r, Modal, Upload, UploadFile, UploadProps } from 'antd'
import { showCustomNotification } from '@/common/Notification'
import { EXAMPLE_FILE } from '@/constants'
import { ModalProps } from '@/types/common'
import * as XLSX from 'xlsx'
import Svg from '../Svg'
import { useState } from 'react'
import { TYPE_PERSON } from '@/types/components'

const { Dragger } = Upload

interface Props extends ModalProps {
  dataImport: any[]
  active?: TYPE_PERSON
  setDataImport: (value: any[]) => void
}

const ModalImport = ({
  isModalOpen,
  isLoading,
  title,
  active,
  dataImport,
  setDataImport,
  handleOk,
  handleCancel
}: Props) => {
  let exampleFile
  const [fileList, setFileList] = useState<any>([])

  const props: UploadProps = {
    name: 'file',
    multiple: false,
    fileList,
    beforeUpload: (file) => {
      if (file) {
        validateFile(file)
        return false
      }
    },
    onChange(info) {
      if (info.fileList.length > 0 && info.file) {
        setFileList(info.fileList)
        handleChange(info.file)
      } else {
        setFileList([])
        setDataImport([])
      }
    }
  }
  const handleClick = (event: { stopPropagation: () => void }) => {
    event.stopPropagation()
    switch (active) {
      case TYPE_PERSON['Sinh viên']:
        exampleFile = title === 'Import người dùng' ? EXAMPLE_FILE.STUDENT_MANAGE_USER : EXAMPLE_FILE.STATUS_STUDENT
        break
      case TYPE_PERSON['Giảng viên']:
        exampleFile = title === 'Import người dùng' ? EXAMPLE_FILE.LECTURER_MANAGE_USER : EXAMPLE_FILE.STATUS_LECTURER
        break
      case TYPE_PERSON['Nhân viên chức năng']:
        exampleFile = title === 'Import người dùng' ? EXAMPLE_FILE.STAFF_MANAGE_USER : EXAMPLE_FILE.STATUS_STAFF
        break
      default:
        if (title === 'Import thời khóa biểu') {
          exampleFile = EXAMPLE_FILE.CALENDAR_EVENT
        } else if (title === 'Import sinh viên vào lớp') {
          exampleFile = EXAMPLE_FILE.STUDENT
        } else if (title === 'Import giảng viên vào lớp') {
          exampleFile = EXAMPLE_FILE.LECTURER
        } else if (title === 'Import lịch thi') {
          exampleFile = EXAMPLE_FILE.CALENDAR_EXAM
        } else exampleFile = 'Không có'
    }
    const link = document.createElement('a')
    link.href = `/assets/files_example/${exampleFile}`
    link.setAttribute('download', exampleFile)
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const handleChange = async (info: any) => {
    try {
      const file = info as UploadFile<any>
      const dataFromExcel = await readExcel(file as unknown as File)
      setDataImport(dataFromExcel)
    } catch {
      setFileList([])
      setDataImport([])
      showCustomNotification({
        status: 'error',
        message: 'Thất bại!',
        description: 'Không thể tải file!'
      })
    }
  }

  const validateFile = (file: File) => {
    const fileExtension = file.name.split('.').pop()?.toLowerCase()
    const MAX_SIZE = 1 * 1024 * 1024 // giới hạn 5MB
    if (file.size > MAX_SIZE) {
      showCustomNotification({
        status: 'error',
        message: 'Thất bại!',
        description: `Vui lòng tải file có dung lượng nhỏ hơn 1MB`
      })
    }

    // Kiểm tra file có phải .csv hoặc .xlsx không
    if (fileExtension !== 'csv' && fileExtension !== 'xlsx') {
      showCustomNotification({
        status: 'error',
        message: 'Thất bại!',
        description: 'File không hợp lệ!'
      })
    }
    return true
  }

  const readExcel = (file: File) => {
    return new Promise<any[]>((resolve, reject) => {
      const reader = new FileReader()

      reader.onload = (event: ProgressEvent<FileReader>) => {
        const data = new Uint8Array(event.target?.result as ArrayBuffer)
        const workbook = XLSX.read(data, { type: 'array' })

        // Lấy Sheet đầu tiên
        const sheetName = workbook.SheetNames[0]
        const worksheet = workbook.Sheets[sheetName]

        // Chuyển đổi sheet thành JSON dạng object
        const rawData: any[][] = XLSX.utils.sheet_to_json(worksheet, { header: 1 })

        if (rawData.length < 2) {
          showCustomNotification({
            status: 'error',
            message: 'Thất bại!',
            description: 'File Excel không có dữ liệu!'
          })
          return
        }

        // Lấy hàng đầu tiên làm key (tên cột)
        const headers = rawData[0]

        // Loại bỏ các hàng không có giá trị (tất cả các cột là null, undefined hoặc chuỗi rỗng)
        const filteredData = rawData.filter((row) => {
          return row.some((cell) => cell !== undefined && cell !== null && cell !== '')
        })

        // Chuyển từng hàng thành object JSON
        const jsonData = filteredData.slice(1).map((row) => {
          const obj: Record<string, any> = {}
          headers.forEach((header, index) => {
            obj[header] = row[index] !== undefined && row[index] !== '' ? row[index] : null
          })
          return obj
        })

        resolve(jsonData)
      }

      reader.onerror = (error) => reject(error)
      reader.readAsArrayBuffer(file)
    })
  }

  const footerModal = (
    <div className='flex items-center justify-center gap-4 p-2 pt-4'>
      <Button
        className='min-w-[126px] bg-neutral-9 hover:opacity-70'
        size='large'
        variant='outlined'
        color='primary'
        disabled={isLoading}
        onClick={(e) => {
          handleCancel(e)
          setFileList([])
          setDataImport([])
        }}
      >
        Hủy
      </Button>
      <Button
        loading={isLoading}
        onClick={() => {
          if (handleOk) {
            handleOk(dataImport)
            setFileList([])
          }
        }}
        type='primary'
        size='large'
        className='min-w-[126px] shadow-none'
        disabled={dataImport.length === 0}
      >
        Xác nhận
      </Button>
    </div>
  )
  return (
    <>
      <Modal
        open={isModalOpen}
        footer={footerModal}
        width={444}
        onCancel={handleCancel}
        title={<p className='font-semibold text-md'>{title}</p>}
      >
        <Divider className='mt-0 mb-5' />
        <Dragger {...props} className='bg-background-row'>
          <div className='flex flex-col items-center'>
            <Svg src='/assets/icons/common/upload.svg' className='h-10 w-10' />
            <p className='text-sm font-normal'>Chọn file .csv hoặc .xlsx</p>
            <a className='text-sm font-medium text-text-primary cursor-pointer hover:underline' onClick={handleClick}>
              Click để tải file mẫu
            </a>
          </div>
        </Dragger>
      </Modal>
    </>
  )
}

export default ModalImport
