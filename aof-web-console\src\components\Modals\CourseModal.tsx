import { Button, Divider, Form, Input, Modal } from 'antd'
import { ModalProps } from '@/types/common'
import { useEffect, useState } from 'react'
import { useForm } from 'antd/es/form/Form'

interface CreateCourseProps extends ModalProps {
  groupName: string
}

const CreateCourseModal = ({
  isModalOpen,
  isLoading,
  groupName,
  update,
  handleCancel,
  handleOk
}: CreateCourseProps) => {
  const [form] = useForm()
  const [isDisabled, setIsDisabled] = useState(true)

  useEffect(() => {
    if (isModalOpen) {
      form.setFieldsValue({ courseName: groupName })
      checkDisabled()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isModalOpen, form, groupName])

  const checkDisabled = () => {
    const values = form.getFieldsValue()
    const hasError = form.getFieldsError().some(({ errors }) => errors.length > 0)
    setIsDisabled(!values.courseName?.trim() || hasError)
  }

  const handleSubmit = async () => {
    const values = await form.validateFields()
    handleOk?.(values.courseName.trim())
    form.resetFields()
  }

  const handleCancelModal = () => {
    form.resetFields()
    handleCancel?.(undefined)
  }

  const footerModal = (
    <div className='flex items-center justify-center gap-4 p-2 pt-4'>
      <Button
        onClick={handleCancelModal}
        size='large'
        variant='outlined'
        color='primary'
        className='min-w-[126px] bg-neutral-9 hover:opacity-70'
      >
        Hủy
      </Button>
      <Button
        loading={isLoading}
        disabled={isDisabled}
        onClick={handleSubmit}
        type='primary'
        size='large'
        className='min-w-[126px]'
      >
        Lưu
      </Button>
    </div>
  )

  return (
    <Modal
      open={isModalOpen}
      onCancel={handleCancelModal}
      footer={footerModal}
      width={582}
      closable
      title={<h2 className='mb-5 text-center text-lg font-semibold'>{update ? 'Sửa' : 'Thêm mới'}</h2>}
    >
      <Form form={form} layout='vertical' requiredMark={false} onValuesChange={checkDisabled}>
        <Form.Item
          name='courseName'
          label={
            <p>
              Tên khóa <span className='text-error'>*</span>
            </p>
          }
          rules={[
            {
              validator: (_, value) =>
                value && value.trim() ? Promise.resolve() : Promise.reject(new Error('Vui lòng nhập tên khóa!'))
            }
          ]}
        >
          <Input allowClear size='large' placeholder='Nhập tên khóa' />
        </Form.Item>
      </Form>

      <Divider className='mb-0' />
    </Modal>
  )
}

export default CreateCourseModal
