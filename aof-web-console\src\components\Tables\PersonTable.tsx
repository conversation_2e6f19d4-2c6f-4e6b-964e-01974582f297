import { MoreOutlined } from '@ant-design/icons'
import { Dropdown, Image, MenuProps, Table, TableProps } from 'antd'
import { useState } from 'react'
import Svg from '@/components/Svg'
import ModalDelete from '@/components/Modals/DeleteModal'
import { useDeleteItem } from '@/hooks/useDeleteItem'
import { deletePerson } from '@/services/person'
import { useNavigate } from 'react-router-dom'
import { Face, Person } from '@/types/person'
import { UserStatusField } from '@/components/StatusField'
import useLayoutStore from '@/stores/layout'
import useAuthStore from '@/stores/auth'
import { checkFeature, FF_DEV_0025, FF_DEV_0026 } from '@/utils/feature-flags'

export interface InfoTableProps {
  active: string
  data: Person[]
  page: number
  maxSize: number
  selectedRow: Person[]
  refreshData: () => Promise<void>
  setSelectedRows: (value: Person[]) => void
}

const PersonTable = ({ active, data, page, maxSize, selectedRow, refreshData, setSelectedRows }: InfoTableProps) => {
  const navigate = useNavigate()

  const { isMobile } = useLayoutStore()
  const { user, featFlagsAction } = useAuthStore()
  const isFFA = (code: string) => checkFeature(code, user, featFlagsAction)

  const [uuid, setUuid] = useState<string>('')

  const { loading, openDeleteModal, setOpenDeleteModal, handleDelete } = useDeleteItem(
    deletePerson,
    `Xóa ${active} thành công!`,
    `Xóa ${active} thất bại!`
  )

  const rowSelection = isMobile
    ? undefined
    : {
        selectedRow,
        onChange: (_: React.Key[], person: Person[]) => {
          setSelectedRows(person)
        }
      }

  const items: MenuProps['items'] = [
    ...(isFFA(FF_DEV_0025)
      ? [
          {
            key: 1,
            label: (
              <div className='flex items-center gap-2' onClick={() => navigate(`/manage-user/${uuid}`)}>
                <Svg src='/assets/icons/common/edit.svg' className='h-5 w-5' />
                Sửa
              </div>
            )
          }
        ]
      : []),
    ...(isFFA(FF_DEV_0026)
      ? [
          {
            key: 2,
            label: (
              <div
                className='flex items-center gap-2'
                onClick={() => {
                  setOpenDeleteModal(true)
                }}
              >
                <Svg src='/assets/icons/common/trash.svg' className='h-5 w-5 text-neutral-4' />
                Xóa
              </div>
            )
          }
        ]
      : [])
  ]

  const columns: TableProps<Person>['columns'] = [
    {
      title: 'STT',
      key: 'personCode',
      align: 'center',
      width: 48,
      render: (_, __, index) => (page - 1) * maxSize + (index + 1)
    },
    {
      title: 'Ảnh đại diện',
      dataIndex: 'faces',
      key: 'faces',
      width: 110,
      render: (faces: Face[]) => (
        <div className='flex justify-center'>
          {faces.length > 0 ? (
            <Image
              src={faces?.[0]?.objectUrl}
              className='rounded-full object-cover border'
              alt='avatar'
              style={{ width: 32, height: 32 }}
              preview={{ mask: false }}
            />
          ) : (
            <Svg src='/assets/icons/common/image-avatar.svg' className='w-8 h-8' />
          )}
        </div>
      )
    },
    {
      title: 'Họ tên',
      dataIndex: 'fullName',
      key: 'fullName',
      width: 300
    },
    {
      title: 'ID',
      dataIndex: 'personCode',
      key: 'personCode',
      width: 250,
      render: (value) => value.toLocaleUpperCase()
    },
    ...(active !== 'Sinh viên'
      ? [
          {
            title: 'Email',
            dataIndex: 'email',
            key: 'email',
            width: 250
          }
        ]
      : []),
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status) => <UserStatusField status={status} />
    },
    ...(isFFA(FF_DEV_0026) || isFFA(FF_DEV_0025)
      ? [
          {
            title: 'Thao tác',
            width: 80,
            key: 'action',
            render: (_: any, record: Person) => (
              <Dropdown menu={{ items }} trigger={['click']} placement='bottomRight'>
                <a
                  onClick={(e) => {
                    e.preventDefault()
                    setUuid(record.uuidPerson || '')
                  }}
                  className='flex justify-center'
                >
                  <MoreOutlined />
                </a>
              </Dropdown>
            )
          }
        ]
      : [])
  ]

  return (
    <>
      <ModalDelete
        isLoading={loading}
        isModalOpen={openDeleteModal}
        handleCancel={() => {
          setOpenDeleteModal(false)
        }}
        title='Xóa'
        handleOk={() => handleDelete(uuid, refreshData)}
        subTitle='Bạn có chắc chắn muốn xóa người dùng này?'
      />
      <Table
        columns={columns}
        dataSource={data}
        pagination={false}
        rowKey={(record) => record.uuidPerson}
        rowSelection={(isFFA(FF_DEV_0026) && rowSelection) || undefined}
        scroll={{ x: 'max-content', y: 560 }}
      />
    </>
  )
}

export default PersonTable
