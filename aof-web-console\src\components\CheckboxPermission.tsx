import { FeatureAction } from '@/types/user'
import { Checkbox, Col, Row } from 'antd'

interface CheckboxPermissionProps {
  title: string
  data: FeatureAction[]
  isChange?: boolean
  showTitle?: boolean
  uuidFeatureActions: string[]
  setUuidFeatureActions: React.Dispatch<React.SetStateAction<string[]>>
}

const CheckboxPermission = ({
  title,
  data,
  isChange,
  showTitle,
  uuidFeatureActions,
  setUuidFeatureActions
}: CheckboxPermissionProps) => {
  const colSpan = Math.floor(24 / data.length)

  const handleToggle = (uuid: string) => {
    setUuidFeatureActions((prev) => (prev.includes(uuid) ? prev.filter((id) => id !== uuid) : [...prev, uuid]))
  }
  return (
    <div>
      {showTitle && (
        <div className='h-10 bg-neutral-9 flex items-center px-2 py-3'>
          <h2 className='text-sm font-medium'>{title}</h2>
        </div>
      )}

      <Checkbox.Group value={uuidFeatureActions} className='px-2 py-3 border-y-[1px] border-neutral-7 w-full'>
        <Row className='w-full'>
          {data.map((item: FeatureAction) => (
            <Col span={colSpan} key={item.uuidFeatureAction}>
              <Checkbox
                value={item.uuidFeatureAction}
                onChange={isChange ? () => handleToggle(item.uuidFeatureAction) : undefined}
              >
                {item.shortName}
              </Checkbox>
            </Col>
          ))}
        </Row>
      </Checkbox.Group>
    </div>
  )
}

export default CheckboxPermission
