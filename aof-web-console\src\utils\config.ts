// Helper function để lấy config value
export function getConfig(key: string): string {
  // Ưu tiên runtime config, fallback về build-time env
  const runtimeConfig = (window as any).APP_CONFIG

  if (runtimeConfig && runtimeConfig[key]) {
    return runtimeConfig[key]
  }

  // Fallback về build-time env vars
  switch (key) {
    case 'API_URL':
      return import.meta.env.VITE_URL_API
    case 'SSO_SERVER':
      return import.meta.env.VITE_IP_PORT_SERVER
    case 'CLIENT_ID':
      return import.meta.env.VITE_CLIENT_ID
    default:
      return ''
  }
}
