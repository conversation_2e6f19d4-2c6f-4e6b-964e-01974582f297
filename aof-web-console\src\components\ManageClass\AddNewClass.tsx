import { showCustomNotification } from '@/common/Notification'
import {
  classIdField,
  classNameField,
  numberCreditsField,
  numberLessonsField,
  semesterField,
  timeField,
  yearField
} from '@/components/InputsField'
import { MESSAGE_STATUS } from '@/constants'
import { setGroupParams } from '@/helpers/configData'
import LayoutCotent from '@/layouts/LayoutCotent'
import { getCustomFields } from '@/services/common'
import { addNewGroup } from '@/services/group'
import { useInfoStore } from '@/stores/info'
import useLayoutStore from '@/stores/layout'
import { useSegmentStore } from '@/stores/useSegmentStore'
import { CustomField } from '@/types/common'
import { ClassType, EntityType, TYPE_CLASS_CREDIT, TYPE_CLASS_EXAM } from '@/types/components'
import { Button, Col, Form, Row, Spin } from 'antd'
import { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'

const AddNewClass = () => {
  const [form] = Form.useForm()
  const navigate = useNavigate()

  const { loadingData, academicYears, semesters, loadAcademicYears, loadSemesters, resetSemesters } = useInfoStore()
  const { groupType } = useSegmentStore()

  const [selectYear, setSelectYear] = useState('')
  const [active, setActive] = useState<ClassType>('Lớp học')
  const [listCustomFields, setListCustomFields] = useState<CustomField[]>([])
  const { isMobile } = useLayoutStore()
  const [isLoading, setIsLoading] = useState<boolean>(false)

  useEffect(() => {
    resetSemesters()
    if (groupType === TYPE_CLASS_CREDIT) {
      setActive('Lớp học')
      getListCustomFields()
    } else if (groupType === TYPE_CLASS_EXAM) {
      setActive('Lớp thi')
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [groupType])

  useEffect(() => {
    if (academicYears.length === 0) {
      loadAcademicYears()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [academicYears])

  useEffect(() => {
    if (selectYear) {
      loadSemesters(selectYear)
    }
  }, [selectYear, loadSemesters])

  const handleClose = () => {
    navigate('/manage-class')
    form.resetFields()
  }

  const getListCustomFields = async () => {
    try {
      const res = await getCustomFields(EntityType.Group)
      if (res.message === MESSAGE_STATUS.SUCCESS) {
        setListCustomFields(res.object.data)
      }
    } catch {
      console.error('Failed to get list custom fields')
    }
  }

  const handleSubmit = async () => {
    await form.validateFields()

    try {
      setIsLoading(true)
      const values = form.getFieldsValue()
      const body = setGroupParams(values, groupType, listCustomFields)
      const res = await addNewGroup(body)
      if (res.message === MESSAGE_STATUS.SUCCESS) {
        navigate('/manage-class')
        showCustomNotification({
          status: 'success',
          message: 'Thành công!',
          description: 'Thêm mới lớp học thành công!'
        })
      }
    } catch {
      showCustomNotification({
        status: 'error',
        message: 'Thất bại',
        description: 'Thêm mới lớp học thất bại!'
      })
    } finally {
      setIsLoading(false)
    }
  }
  return (
    <LayoutCotent title={['Quản lý lớp', active, `Thêm mới ${active.toLowerCase()}`]} btnBack={true}>
      <Spin spinning={isLoading}>
        <div className='px-5 flex justify-items-stretch xl:w-2/3'>
          <Form form={form} requiredMark={false} layout='vertical' className='flex gap-5 w-full'>
            <Row gutter={[20, 0]} className='w-full'>
              <Col span={isMobile ? 24 : 12}>{classNameField({})}</Col>
              <Col span={isMobile ? 24 : 12}>{classIdField({})}</Col>
              {groupType === TYPE_CLASS_CREDIT && (
                <>
                  <Col span={12}>{numberCreditsField({})}</Col>
                  <Col span={12}>{numberLessonsField({})}</Col>
                </>
              )}
              <Col span={12}>{yearField({ academicYears, setSelectYear })}</Col>
              <Col span={12}>{semesterField({ semesters, loadingData })}</Col>
              <Col span={24}>{timeField({ isMobile })}</Col>
              <Col span={isMobile ? 24 : 12}>
                <div className='flex gap-5 mt-2'>
                  <Button
                    variant='outlined'
                    color='primary'
                    size='large'
                    className={isMobile ? 'flex-1' : undefined}
                    style={{ width: 126 }}
                    onClick={handleClose}
                  >
                    Hủy
                  </Button>
                  <Button
                    type='primary'
                    size='large'
                    className={isMobile ? 'flex-1' : undefined}
                    style={{ width: 126 }}
                    onClick={handleSubmit}
                  >
                    Lưu
                  </Button>
                </div>
              </Col>
            </Row>
          </Form>
        </div>
      </Spin>
    </LayoutCotent>
  )
}

export default AddNewClass
