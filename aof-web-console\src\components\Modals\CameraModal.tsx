import { Button, Divider, Form, Input, Modal, Select } from 'antd'
import { ModalProps } from '@/types/common'
import { useForm } from 'antd/es/form/Form'
import { useEffect, useState } from 'react'
import { getListRooms } from '@/services/room_device'
import { MESSAGE_STATUS } from '@/constants'
import { MAX_SIZE_PAGE } from '@/types/components'
import { showCustomNotification } from '@/common/Notification'
import { Location } from '@/types/room_device'

interface UpdateProps extends ModalProps {
  dataUpdate: any
  isUpdate: boolean
}

const CameraModal = ({ dataUpdate, isModalOpen, isLoading, isUpdate, handleOk, handleCancel }: UpdateProps) => {
  const [form] = useForm()
  const [listRooms, setListRoom] = useState<Location[]>([])

  useEffect(() => {
    if (isModalOpen) {
      form.setFieldsValue(dataUpdate)
      form.setFieldValue('uuidLocation', dataUpdate?.uuidLocation || '')
      getData()
    }
  }, [isModalOpen, form, dataUpdate])

  const getData = async () => {
    try {
      const res = await getListRooms({ page: 1, maxSize: MAX_SIZE_PAGE })

      if (res.message === MESSAGE_STATUS.SUCCESS) {
        setListRoom(res.object.data || [])
      }
    } catch {
      showCustomNotification({
        status: 'error',
        message: 'Thất bại',
        description: 'Lỗi khi lấy danh sách!'
      })
    }
  }

  const handleSubmit = async () => {
    const values = await form.validateFields()
    handleOk?.(values)
    form.resetFields()
  }

  const footerModal = (
    <div className='flex items-center justify-center gap-4 p-2 pt-4'>
      <Button
        onClick={handleCancel}
        size='large'
        variant='outlined'
        color='primary'
        className='min-w-[126px] bg-neutral-9 hover:opacity-70'
      >
        Hủy
      </Button>
      <Button loading={isLoading} onClick={handleSubmit} type='primary' size='large' className='min-w-[126px]'>
        Lưu
      </Button>
    </div>
  )

  return (
    <Modal
      open={isModalOpen}
      onCancel={handleCancel}
      footer={footerModal}
      title={
        <p className=' text-md font-semibold text-neutral-1'>
          {isUpdate ? 'Sửa thông tin Camera ' : 'Thêm mới Camera'}
        </p>
      }
    >
      <Divider className='mb-4 mt-0' />
      <Form form={form} layout='vertical' requiredMark={false}>
        <Form.Item
          name='cameraName'
          label={
            <p>
              Tên Camera <span className='text-error'>*</span>
            </p>
          }
          rules={[
            {
              validator: (_, value) => {
                if (!value || value.trim() === '') {
                  return Promise.reject(new Error('Vui lòng nhập tên camera!'))
                }
                return Promise.resolve()
              }
            }
          ]}
        >
          <Input size='large' placeholder='Nhập tên camera' />
        </Form.Item>
        <Form.Item
          name='videoStreamUrl'
          label='Link RTSP'
          // rules={[
          //   {
          //     validator: (_, value) => {
          //       if (!value || value.trim() === '') {
          //         return Promise.reject(new Error('Vui lòng nhập Link RTSP!'))
          //       }
          //       return Promise.resolve()
          //     }
          //   }
          // ]}
        >
          <Input allowClear size='large' placeholder='Nhập link rtsp' />
        </Form.Item>
        <Form.Item
          name='uuidLocation'
          label={
            <p>
              Lớp học <span className='text-error'>*</span>
            </p>
          }
          rules={[
            {
              validator: (_, value) => {
                if (!value || value.trim() === '') {
                  return Promise.reject(new Error('Vui lòng chọn lớp học!'))
                }
                return Promise.resolve()
              }
            }
          ]}
        >
          <Select
            allowClear
            size='large'
            placeholder='Chọn phòng học'
            options={listRooms.map((item) => ({ label: item.name, value: item.uuidLocation }))}
          />
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default CameraModal
