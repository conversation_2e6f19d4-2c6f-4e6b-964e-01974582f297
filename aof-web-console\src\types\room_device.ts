export interface Location {
  uuidLocation: string
  name: string
  description: string
  uuidOrganization?: string
}

export interface Device {
  acsDevIndexCode: string
  acsDevName: string
  acsDevIp: string
  acsDevPort: string
  acsDevCode: string
  status: number
  location: Location
}

export interface Camera {
  cameraIndexCode: string
  cameraName: string
  status: number
  videoStreamUrl: string
  location: Location
}

export interface ParamsCamera {
  cameraName: string
  videoStreamUrl: string
  uuidLocation: string
}
