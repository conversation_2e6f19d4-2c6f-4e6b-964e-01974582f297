import { useState } from 'react'
import { Button, Form, Image, Input, Spin } from 'antd'
import { showCustomNotification } from '@/common/Notification'
import backgroundImage from '/assets/images/background.png'
import logoImage from '/assets/images/logo2.png'
import Svg from '@/components/Svg'
import { login } from '@/services/auth'
import { MESSAGE_STATUS } from '@/constants'
import useAuthStore from '@/stores/auth'
import { getConfig } from '@/utils/config'

const initialValues = { username: '', password: '' }

const Login = () => {
  const user = useAuthStore()
  const [form] = Form.useForm()
  const [data, setData] = useState(initialValues)
  const [onLoggin, setOnLoggin] = useState<boolean>(false)

  const handleLogin = async () => {
    try {
      setOnLoggin(true)
      const res = await login({
        username: data.username,
        password: data.password
      })
      if (res.message === MESSAGE_STATUS.SUCCESS) {
        user.login(res.object.accessToken, res.object.refreshToken)
      }
    } catch {
      showCustomNotification({
        status: 'error',
        message: 'Thất bại!',
        description: 'Đăng nhập thất bại. Vui lòng thử lại!'
      })
    } finally {
      setOnLoggin(false)
    }
  }

  const handleSSOLogin = () => {
    const ssoURL = new URL(getConfig('SSO_SERVER') + `/oauth2/authorize`)
    ssoURL.searchParams.append('response_type', 'code')
    ssoURL.searchParams.append('client_id', getConfig('CLIENT_ID'))
    ssoURL.searchParams.append('redirect_uri', `${window.location.origin}/sso/identifier`)
    ssoURL.searchParams.append('scope', 'openid')

    window.location.replace(ssoURL.toString())
  }

  return (
    <div className='flex items-center'>
      <div className='h-screen w-2/3 hidden lg:block relative'>
        <img src={backgroundImage} alt='Placeholder Image' className='object-cover w-full h-full' />
      </div>
      <div className='w-full xxl:w-1/2 xl:w-1/4 lg:w-1/5 md:m-20 flex flex-col p-10 items-center'>
        <Spin spinning={onLoggin}>
          <div className='flex flex-col items-center'>
            <Image src={logoImage} preview={false} height={100} width={100} />
            <h1 className='font-bold text-2xl sm:text-4xl text-text-primary mt-4 mb-16'>HỌC VIỆN TÀI CHÍNH</h1>
          </div>
          <h1 className='text-2xl font-semibold mb-4 '>Đăng nhập</h1>
          <Form
            layout='vertical'
            className='sm:w-[400px] '
            initialValues={initialValues}
            onFinish={handleLogin}
            requiredMark={false}
            form={form}
          >
            <Form.Item
              label={
                <p>
                  Tên đăng nhập <span className='text-error'>*</span>
                </p>
              }
              name='email'
              rules={[
                {
                  required: true,
                  message: 'Vui lòng nhập tên đăng nhập!'
                }
              ]}
            >
              <Input
                size='large'
                className='w-full'
                prefix={<Svg src='/assets/icons/common/user.svg' className='h-5 w-5' />}
                onChange={(e) => setData((pre) => ({ ...pre, username: e.target.value }))}
                placeholder='Nhập tên đăng nhập'
              />
            </Form.Item>
            <Form.Item
              label={
                <p>
                  Mật khẩu <span className='text-error'>*</span>
                </p>
              }
              name='password'
              rules={[
                {
                  required: true,
                  message: 'Vui lòng nhập mật khẩu!'
                }
              ]}
            >
              <Input.Password
                size='large'
                className='w-full'
                prefix={<Svg src='/assets/icons/common/pwd.svg' className='h-5 w-5' />}
                onChange={(e) => setData((pre) => ({ ...pre, password: e.target.value }))}
                placeholder='Nhập mật khẩu'
              />
            </Form.Item>
            <div className='flex flex-col gap-4'>
              <Button size='large' className='w-full' type='primary' htmlType='submit'>
                Đăng nhập
              </Button>
              <Button size='large' className='w-full' color='primary' variant='outlined' onClick={handleSSOLogin}>
                Đăng nhập bằng SSO
              </Button>
            </div>
          </Form>
        </Spin>
      </div>
    </div>
  )
}

export default Login
