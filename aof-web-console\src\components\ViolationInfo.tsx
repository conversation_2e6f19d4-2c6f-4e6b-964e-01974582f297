import { showCustomNotification } from '@/common/Notification'
import { MESSAGE_STATUS } from '@/constants'
import { confirmExamCheating } from '@/services/person'
import { ExamStatus } from '@/types/components'
import { ExamCheating } from '@/types/person'
import { Button, Col, Image, Row } from 'antd'
import { BehaviorField } from './StatusField'
import Svg from './Svg'
import { useEffect, useState } from 'react'
import { checkFeature, FF_DEV_0019 } from '@/utils/feature-flags'
import useAuthStore from '@/stores/auth'
import ReactPlayer from 'react-player/lazy'

const ViolationInfo = ({
  selectedCard,
  setSelectedCard,
  refreshData
}: {
  selectedCard: ExamCheating
  setSelectedCard: (e: any) => void
  refreshData: () => void
}) => {
  const { user, featFlagsAction } = useAuthStore()
  const isFFA = (code: string) => checkFeature(code, user, featFlagsAction)
  const [personStates, setPersonStates] = useState<Record<string, boolean>>({})
  useEffect(() => {
    const initialStates: Record<string, boolean> = {}
    let hasConfirmed = false

    selectedCard.persons.forEach((p) => {
      const isConfirmed = p.confirmed === 1
      initialStates[p.uuidPerson] = isConfirmed
      if (isConfirmed) hasConfirmed = true
    })
    if (!hasConfirmed && selectedCard.persons.length > 0) {
      const firstPersonUuid = selectedCard.persons[0].uuidPerson
      initialStates[firstPersonUuid] = true
    }

    setPersonStates(initialStates)
  }, [selectedCard.persons])

  const handleConfirm = async (status: boolean) => {
    try {
      const res = await confirmExamCheating(
        [selectedCard.uuidExamCheating],
        status ? ExamStatus['Đã xác nhận'] : ExamStatus['Chưa xác nhận'],
        Object.keys(personStates).find((key) => personStates[key] === true) || ''
      )
      if (res.message === MESSAGE_STATUS.SUCCESS) {
        refreshData()
        showCustomNotification({
          status: 'success',
          message: 'Thành công!',
          description: 'Cập nhật trạng thái thành công!'
        })
      }
    } catch {
      showCustomNotification({
        status: 'error',
        message: 'Thất bại!',
        description: 'Cập nhật trạng thái thất bại!'
      })
    }
  }
  const handleIconClick = (personUuid: string) => {
    setPersonStates((prevStates) => {
      const currentConfirmation = prevStates[personUuid]

      const newStates: Record<string, boolean> = {}

      Object.keys(prevStates).forEach((uuid) => {
        newStates[uuid] = uuid === personUuid ? !currentConfirmation : false
      })

      return newStates
    })
  }

  const isChange = selectedCard.cheatingStatus === ExamStatus['Chưa xác nhận']
  return (
    <>
      <h1 className='mb-2 text-lg font-semibold'>Chi tiết vi phạm</h1>
      <div className='border border-[#d9f0f4] rounded-lg bg-[#f7fcfd] p-4'>
        <div className='w-[500px] mx-auto'>
          {selectedCard.videoUrl ? (
            <ReactPlayer url={selectedCard.videoUrl} style={{ width: 'auto' }} height={280} width={'auto'} controls />
          ) : (
            <img className='rounded-xl w-full cursor-pointer' src={selectedCard.thumbnailUrl} alt='Video thumbnail' />
          )}
        </div>
        <div className='pt-5'>
          <h1 className='mb-3 text-lg font-semibold'>Thông tin vi phạm</h1>
          <div className='flex flex-1 gap-2'>
            <div>
              <Image
                src={selectedCard.imageUrl}
                className='min-w-[180px] object-cover rounded-2xl border-4 border-[#00A5BA]'
                height={180}
              />
            </div>
            <Row gutter={4}>
              {selectedCard.persons.length > 0 &&
                selectedCard.persons.map((p) => {
                  const isPersonConfirmed = personStates[p.uuidPerson] ?? false
                  return (
                    <Col span={8} key={p.uuidPerson}>
                      <div className='relative inline-block'>
                        <img
                          src={p.person.faces?.[0]?.objectUrl}
                          alt=''
                          className='w-[120px] h-[120px] object-cover rounded-xl mx-auto'
                        />
                        <Svg
                          src={
                            isPersonConfirmed
                              ? '/assets/icons/status/confirmed_violation.svg'
                              : '/assets/icons/status/unconfirmed_violation.svg'
                          }
                          className={`h-7 w-7 absolute -top-1.5 -right-2 rounded-full bg-white p-1 shadow ${isChange && 'cursor-pointer'}`}
                          onClick={isChange ? () => handleIconClick(p.uuidPerson) : undefined}
                        />
                      </div>
                      <Row gutter={4} className='text-neutral-2'>
                        <Col span={10} className='text-xs'>
                          Tỷ lệ khớp:
                        </Col>
                        <Col
                          span={14}
                          className={`${
                            parseFloat(p.matchRate) > 0.7
                              ? 'text-[#059649]'
                              : parseFloat(p.matchRate) > 0.5
                                ? 'text-[#E79200]'
                                : 'text-neutral-2'
                          }
                            text-xs`}
                        >
                          {(parseFloat(p.matchRate) * 100).toFixed(0)}%
                        </Col>
                        <Col span={10} className='text-xs'>
                          Họ tên:
                        </Col>
                        <Col span={14} className='text-xs text-neutral-2'>
                          {p.person.fullName}
                        </Col>
                        <Col span={10} className='text-xs'>
                          ID:
                        </Col>
                        <Col span={14} className='text-xs text-neutral-2'>
                          {p.person.personCode.toUpperCase()}
                        </Col>
                      </Row>
                    </Col>
                  )
                })}
            </Row>
          </div>
          <div className='flex flex-col pt-5'>
            <Row gutter={[4, 4]}>
              <Col span={8}>
                <p className='text-sm font-semibold text-neutral-2'>Thời gian vi phạm:</p>
              </Col>
              <Col span={16} className='text-neutral-2'>
                {selectedCard.cheatedAt}
              </Col>
              <Col span={8}>
                <p className='text-sm font-semibold text-neutral-2'>Hành vi:</p>
              </Col>
              <Col span={16} className='text-neutral-2'>
                <BehaviorField status={selectedCard.cheatingType} />
              </Col>
              <Col span={8}>
                <p className='text-sm font-semibold text-neutral-2'>Phòng thi:</p>
              </Col>
              <Col span={16} className='text-neutral-2'>
                {selectedCard.location.name}
              </Col>
              <Col span={8}>
                <p className='text-sm font-semibold text-neutral-2'>Lớp thi:</p>
              </Col>
              <Col span={16} className='text-neutral-2'>
                {selectedCard.group.groupName}
              </Col>
              {selectedCard.cheatingStatus === ExamStatus['Đã xác nhận'] && (
                <>
                  <Col span={8}>
                    <p className='text-sm font-semibold text-neutral-2'>Trạng thái xác nhận:</p>
                  </Col>
                  <Col span={16} className='text-neutral-2'>
                    <div className='flex items-center gap-2'>
                      <Svg src='/assets/icons/status/graduated.svg' className='h-5 w-5' />
                      Đã xác nhận
                    </div>
                  </Col>
                  <Col span={8}>
                    <p className='text-sm font-semibold text-neutral-2'>Thời gian xác nhận:</p>
                  </Col>
                  <Col span={16} className='text-neutral-2'>
                    {selectedCard.statusUpdatedAt}
                  </Col>
                </>
              )}
            </Row>
          </div>
          <div className='flex gap-3 pt-5 justify-end'>
            <Button
              variant='outlined'
              color='primary'
              size='large'
              style={{ width: 90 }}
              onClick={() => setSelectedCard(null)}
            >
              Hủy
            </Button>
            {isFFA(FF_DEV_0019) && selectedCard.cheatingStatus === ExamStatus['Chưa xác nhận'] && (
              <Button
                type='primary'
                className='w-[120px]'
                size='large'
                onClick={(e) => {
                  e.stopPropagation()
                  handleConfirm(true)
                  setSelectedCard(null)
                }}
              >
                Xác nhận
              </Button>
            )}
          </div>
        </div>
      </div>
    </>
  )
}

export default ViolationInfo
