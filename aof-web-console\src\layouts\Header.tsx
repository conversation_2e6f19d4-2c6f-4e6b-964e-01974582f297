import MenuDrawer from '@/components/MenuDrawer'
import Profile from '@/components/Profile'
import Svg from '@/components/Svg'
import useLayoutStore from '@/stores/layout'
import { DownOutlined } from '@ant-design/icons'
import { Button, Col, Dropdown, Image, MenuProps, Row } from 'antd'
import { useEffect, useState } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import { BehaviorField } from '@/components/StatusField'
import { getMenu } from './Sider'
import useAuthStore from '@/stores/auth'

export default function Header({ showMenu }: { showMenu: boolean }) {
  const navigate = useNavigate()
  const [selectedLang, setSelectedLang] = useState<'vi' | 'en'>('vi')
  const { isMobile } = useLayoutStore()
  const { user } = useAuthStore()
  const location = useLocation()
  const [title, setTitle] = useState('')
  const [open, setOpen] = useState(false)
  const isHiddened = location.pathname.split('/').length === 2

  useEffect(() => {
    const check = getMenu(user).find((path) => location.pathname.includes(path.url))
    if (check) {
      setTitle(check.label)
    }
  }, [location.pathname, user])

  const items: MenuProps['items'] = [
    {
      key: 'vi',
      label: (
        <div className='flex items-center space-x-2' onClick={() => setSelectedLang('vi')}>
          <Image src='/assets/icons/flag/vn.svg' alt='Vietnamese' width={20} height={14} preview={false} />
          <span>Tiếng Việt</span>
        </div>
      )
    },
    {
      key: 'en',
      label: (
        <div className='flex items-center space-x-2' onClick={() => setSelectedLang('en')}>
          <Image src='/assets/icons/flag/en.svg' alt='English' width={20} height={14} preview={false} />
          <span>English</span>
        </div>
      )
    }
  ]
  const notificationItems: MenuProps['items'] = [
    {
      key: '1',
      className: '!bg-[#F5F9FE] border-b-2 !rounded-b-none',
      onClick: () => {
        navigate('/manage-exam')
      },
      label: (
        <div className={`rounded-lg p-2 ${isMobile ? 'w-[300px]' : 'w-[400px]'}`}>
          <div className='flex items-center justify-between mb-2'>
            <div className='flex items-center gap-2'>
              <span className='text-base font-medium text-neutral-1'>Phát hiện hành vi gian lận</span>
              <Svg src='/assets/icons/status/warning.svg' className='w-5 h-5' />
            </div>
            <span className='inline-flex size-2 rounded-full bg-sky-500'></span>
          </div>
          <Row gutter={[8, 0]}>
            <Col span={6}>
              <Image
                src='/assets/images/avatar.png'
                className='w-full h-full max-h-20 max-w-25 object-cover'
                preview={false}
              />
            </Col>
            <Col span={18} className='text-neutral-2'>
              <div className='grid grid-cols-3'>
                <span className='text-sm font-semibold'>Thời gian:</span>
                <span className='col-span-2'>21/01/2024 - 8:23:45</span>

                <span className='text-sm font-semibold'>Hành vi:</span>
                <span className='col-span-2'>
                  <BehaviorField status={1} />
                </span>

                <span className='text-sm font-semibold'>Phòng thi:</span>
                <span className='col-span-2'>{201}</span>
              </div>
            </Col>
          </Row>
        </div>
      )
    },
    {
      key: '2',
      label: (
        <div className={`rounded-lg p-2 ${isMobile ? 'w-[300px]' : 'w-[400px]'}`}>
          <div className='flex items-center justify-between mb-2'>
            <div className='flex items-center gap-2'>
              <span className='text-base font-medium text-neutral-1'>Phát hiện hành vi gian lận</span>
              <Svg src='/assets/icons/status/warning-read.svg' className='w-5 h-5' />
            </div>
          </div>
          <Row gutter={[8, 0]}>
            <Col span={6}>
              <Image
                src='/assets/images/avatar.png'
                className='w-full h-full max-h-20 max-w-25 object-cover'
                preview={false}
              />
            </Col>
            <Col span={18} className='text-neutral-2'>
              <div className='grid grid-cols-3'>
                <span className='text-sm font-semibold'>Thời gian:</span>
                <span className='col-span-2'>21/01/2024 - 8:23:45</span>

                <span className='text-sm font-semibold'>Hành vi:</span>
                <span className='col-span-2'>
                  <BehaviorField status={1} />
                </span>

                <span className='text-sm font-semibold'>Phòng thi:</span>
                <span className='col-span-2'>{201}</span>
              </div>
            </Col>
          </Row>
        </div>
      )
    }
  ]
  return (
    <div className={!isHiddened && isMobile ? 'hidden' : 'block'}>
      <MenuDrawer open={open} setOpen={setOpen} />
      <div
        className='flex sm:h-[66px] items-center justify-between bg-background-1 px-3 md:px-6 py-1 shadow-header'
        style={{ borderBottom: '1px solid #DCDEEF' }}
      >
        <div className='flex items-center gap-1 md:gap-4'>
          {showMenu && (
            <Button
              variant='outlined'
              type='text'
              onClick={() => setOpen(true)}
              icon={<Svg src='/assets/icons/menu/menu.svg' className='h-6 w-6 mt-1 text-text-2' />}
            />
          )}
          {!isMobile && <Image src='/assets/images/logo.png' className='max-h-10 w-56' preview={false} />}
          {isMobile && <h1 className='text-md font-medium text-neutral-1'>{title}</h1>}
        </div>
        <div className='flex gap-3 md:gap-5 items-center'>
          {!isMobile && (
            <div className='cursor-pointer duration-200 hover:scale-110'>
              <Dropdown menu={{ items }} trigger={['click']}>
                <div className='flex items-center space-x-2 cursor-pointer md:p-2 rounded-lg'>
                  <Image
                    src={selectedLang === 'vi' ? '/assets/icons/flag/vn.svg' : '/assets/icons/flag/en.svg'}
                    alt='Flag'
                    width={24}
                    height={16}
                    preview={false}
                  />
                  <span className='hidden xs:block font-semibold'>{selectedLang.toUpperCase()}</span>
                  <DownOutlined className='hidden xs:block text-gray-500 text-xs' />
                </div>
              </Dropdown>
            </div>
          )}

          <div className='cursor-pointer duration-200 hover:scale-110'>
            <Dropdown menu={{ items: notificationItems }} trigger={['click']} placement='bottom'>
              <div className='relative'>
                <Svg src='/assets/icons/common/bell.svg' className='xs:h-6 xs:w-6 h-5 w-5' />
                <span className='absolute inline-flex size-3 rounded-full bg-red-500 top-0 right-0 border-2 border-white'></span>
              </div>
            </Dropdown>
          </div>
          <div className='cursor-pointer'>
            <Profile />
          </div>
        </div>
      </div>
    </div>
  )
}
