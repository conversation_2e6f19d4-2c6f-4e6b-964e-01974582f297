import { showCustomNotification } from '@/common/Notification'
import StudentInformation from '@/components/StudentInfo'
import Svg from '@/components/Svg'
import { MESSAGE_STATUS } from '@/constants'
import LayoutCotent from '@/layouts/LayoutCotent'
import { getListPersons } from '@/services/person'
import useLayoutStore from '@/stores/layout'
import { ResponseList } from '@/types/common'
import { initialProperty, pageSize } from '@/types/components'
import { Face, Person } from '@/types/person'
import { Button, Col, Input, Pagination, Row, Select, Table, TableProps } from 'antd'
import { useEffect, useState } from 'react'
import { useParams } from 'react-router-dom'
import ModalStudentInfo from '../Modals/StudentInfomationModal'
import { getDetailsGroup } from '@/services/group'

const ManageListStudents = () => {
  const { uuidGroup } = useParams()
  const { isMobile, isTablet } = useLayoutStore()

  const [keySearch, setKeySearch] = useState('')
  const [listPersons, setListPersons] = useState<ResponseList<Person>>()
  const [pageProperty, setPageProperty] = useState<{
    page: number
    maxSize: number
    totalElement: number
  }>(initialProperty)

  const [uuidPerson, setUuidPerson] = useState<string>('')
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [openModal, setOpenModal] = useState<boolean>(false)
  const [groupName, setGroupName] = useState('')

  useEffect(() => {
    getData()
    getDetails()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pageProperty])

  const getData = async () => {
    setIsLoading(true)
    try {
      const res = await getListPersons({
        ...pageProperty,
        uuidGroups: uuidGroup,
        keySearch
      })

      if (res.message === MESSAGE_STATUS.SUCCESS) {
        setListPersons(res.object)
      }
    } catch {
      showCustomNotification({
        status: 'error',
        message: 'Thất bại!',
        description: 'Có lỗi xảy ra!'
      })
    } finally {
      setIsLoading(false)
    }
  }
  const getDetails = async () => {
    try {
      if (!uuidGroup) return
      setIsLoading(true)
      const res = await getDetailsGroup(uuidGroup)
      if (res.message === MESSAGE_STATUS.SUCCESS) {
        setGroupName(res.object.groupName)
      }
    } catch {
      console.log('Lỗi không lấy được thông tin khóa học!')
    }
  }

  const columns: TableProps<Person>['columns'] = [
    {
      title: 'STT',
      key: 'string',
      width: 48,
      align: 'center',
      render: (_, __, index) => index + 1
    },
    ...(isMobile
      ? []
      : [
          {
            title: 'Ảnh đại diện',
            dataIndex: 'faces',
            key: 'faces',
            width: 120,
            render: (faces: Face[]) => (
              <div className='flex justify-center'>
                {faces.length > 0 ? (
                  <img
                    src={faces?.[0]?.objectUrl}
                    alt='avatar'
                    className='rounded-full object-cover border'
                    style={{ width: 32, height: 32 }}
                  />
                ) : (
                  <Svg src='/assets/icons/common/image-avatar.svg' className='w-8 h-8' />
                )}
              </div>
            )
          }
        ]),

    {
      title: 'Họ tên',
      dataIndex: 'fullName',
      key: 'fullName',
      render: (value, record) => (
        <p
          className='text-text-3 underline cursor-pointer'
          onClick={() => {
            setUuidPerson(record.uuidPerson)
            if (isMobile || isTablet) {
              setOpenModal(true)
            }
          }}
        >
          {value}
        </p>
      )
    },
    {
      title: 'ID',
      dataIndex: 'personCode',
      key: 'personCode',
      render: (value) => value.toLocaleUpperCase()
    }
  ]
  if (!listPersons) return
  return (
    <>
      <ModalStudentInfo
        isModalOpen={openModal}
        handleCancel={() => {
          setOpenModal(false)
          setUuidPerson('')
        }}
        uuidPerson={uuidPerson}
      />
      <LayoutCotent title={['Quản lý khóa', 'Danh sách sinh viên']} btnBack={true}>
        <div className='xs:flex gap-5 items-end mb-5'>
          <div>
            <label htmlFor='search'>Khóa</label>
            <Input id='search' size='large' disabled value={groupName} className='w-full border-neutral-7' />
          </div>
          <div className='flex items-end gap-2 xs:gap-5 w-full'>
            <div className='w-full'>
              <label htmlFor='search'>Tìm kiếm</label>
              <Input
                id='search'
                size='large'
                value={keySearch}
                className='w-full border-neutral-7'
                placeholder='Nhập nội dung tìm kiếm'
                onPressEnter={() => {
                  setPageProperty((pre) => ({ ...pre, page: 1 }))
                  setUuidPerson('')
                }}
                onChange={(e) => setKeySearch(e.target.value)}
              />
            </div>
            <Button
              size='large'
              type='primary'
              icon={<Svg src='/assets/icons/common/search.svg' className='h-5 w-5' />}
              onClick={() => {
                setPageProperty((pre) => ({ ...pre, page: 1 }))
                setUuidPerson('')
              }}
            >
              {!isMobile && 'Tìm kiếm'}
            </Button>
          </div>
        </div>

        <Row gutter={[16, 16]}>
          <Col span={!isMobile && !isTablet && uuidPerson ? 16 : 24}>
            <h1 className='mb-5 text-lg font-semibold'>Danh sách</h1>
            <Table
              loading={isLoading}
              columns={columns}
              dataSource={listPersons.data || []}
              pagination={false}
              scroll={{ x: 'max-content', y: 565 }}
              rowKey={(record) => record.uuidPerson}
            />
            {listPersons.data.length > 0 && (
              <div className='my-4 flex max-sm:flex-col max-sm:items-center items-center justify-between'>
                <div className='flex items-center gap-2'>
                  <p className='text-xs font-normal text-gray-400'>Số bản ghi mỗi trang </p>
                  <Select
                    size='small'
                    options={pageSize}
                    value={pageProperty.maxSize}
                    onChange={(e) => setPageProperty({ ...pageProperty, maxSize: e, page: 1 })}
                  />
                  <p className='text-xs font-normal text-gray-400 hidden sm:block'>
                    trên tổng {listPersons.totalElement} dữ liệu
                  </p>
                </div>
                <Pagination
                  onChange={(page) => {
                    setPageProperty({ ...pageProperty, page })
                  }}
                  align='end'
                  current={listPersons.page}
                  defaultCurrent={1}
                  showSizeChanger={false}
                  pageSize={listPersons.maxSize}
                  defaultPageSize={10}
                  total={listPersons.totalElement}
                />
              </div>
            )}
          </Col>
          {!isMobile && !isTablet && uuidPerson && (
            <Col span={8} className='duration-500 '>
              <h1 className='mb-5 text-lg font-semibold'>Thông tin sinh viên</h1>
              <StudentInformation uuidPerson={uuidPerson} setUuidPerson={setUuidPerson} />
            </Col>
          )}
        </Row>
      </LayoutCotent>
    </>
  )
}

export default ManageListStudents
