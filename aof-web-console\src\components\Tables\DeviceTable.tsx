import { showCustomNotification } from '@/common/Notification'
import { MESSAGE_STATUS } from '@/constants'
import { getListDevices } from '@/services/room_device'
import { ResponseList } from '@/types/common'
import { pageSize } from '@/types/components'
import { Device, Location } from '@/types/room_device'
import { Pagination, Select, Table, TableProps } from 'antd'
import { useEffect, useState } from 'react'
import { DeviceStatusField } from '../StatusField'
import useLayoutStore from '@/stores/layout'

interface Props {
  params: any
  setIsLoading: (e: boolean) => void
  setParams: (e: any) => void
}

const DeviceTable = ({ params, setIsLoading, setParams }: Props) => {
  const { isMobile } = useLayoutStore()
  const [dataSource, setDataSource] = useState<ResponseList<Device>>()

  useEffect(() => {
    getData()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [params])

  const getData = async () => {
    try {
      setIsLoading(true)
      const res = await getListDevices(params)

      if (res.message === MESSAGE_STATUS.SUCCESS) {
        setDataSource(res.object)
      }
    } catch {
      showCustomNotification({
        status: 'error',
        message: 'Thất bại',
        description: 'Lỗi khi lấy danh sách!'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const columns: TableProps<Device>['columns'] = [
    {
      title: 'STT',
      dataIndex: 'key',
      key: 'key',
      align: 'center',
      width: 48,
      render: (_, __, index) => (dataSource ? (dataSource.page - 1) * dataSource.maxSize + (index + 1) : '')
    },
    { title: 'Tên thiết bị', dataIndex: 'acsDevName', key: 'acsDevName' },
    { title: 'Tên phòng', dataIndex: 'location', key: 'location', render: (e: Location) => e.name },
    ...(!isMobile
      ? [
          { title: 'Mã thiết bị', dataIndex: 'acsDevCode', key: 'acsDevCode' },
          { title: 'Địa chỉ kết nối', dataIndex: 'acsDevIp', key: 'acsDevIp' },
          { title: 'Cổng kết nối', dataIndex: 'acsDevPort', key: 'acsDevPort' }
        ]
      : []),

    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      render: (status: number) => <DeviceStatusField status={status} />
    }
  ]

  return (
    <>
      <Table
        columns={columns}
        pagination={false}
        dataSource={dataSource?.data}
        rowKey={(record) => record.acsDevCode}
      />
      {dataSource?.data && dataSource.data.length > 0 && (
        <div className={'my-4 flex max-sm:flex-col max-sm:items-center items-end justify-between gap-4'}>
          <div className='flex items-center gap-2'>
            <p className='text-xs font-normal text-gray-400'>Số bản ghi mỗi trang </p>
            <Select
              size='small'
              options={pageSize}
              value={params.maxSize}
              onChange={(e) => setParams({ ...params, maxSize: e })}
            />
            <p className='text-xs font-normal text-gray-400 hidden sm:block'>
              trên tổng {dataSource.totalElement} dữ liệu{' '}
            </p>
          </div>
          <Pagination
            onChange={(page) => setParams((prev: any) => ({ ...prev, page }))}
            align='end'
            current={dataSource.page}
            defaultCurrent={1}
            showSizeChanger={false}
            pageSize={dataSource.maxSize}
            defaultPageSize={10}
            total={dataSource.totalElement}
          />
        </div>
      )}
    </>
  )
}

export default DeviceTable
