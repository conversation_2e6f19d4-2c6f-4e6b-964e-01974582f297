import Svg from '@/components/Svg'
import useLayoutStore from '@/stores/layout'
import { useSegmentStore } from '@/stores/useSegmentStore'
import { Breadcrumb } from 'antd'
import { useLocation, useNavigate } from 'react-router-dom'

interface Content {
  children: any
  title: string[]
  btnBack?: boolean
}

const LayoutCotent = ({ btnBack, children, title }: Content) => {
  const navigate = useNavigate()
  const { isMobile } = useLayoutStore()
  const { setActivePermissionDetail, setActiveClassDetail } = useSegmentStore()
  const location = useLocation().pathname.split('/')
  const isHiddened = location.length === 2

  const handleClick = (index: number) => {
    if (index === 0) index = 1
    else if (index === title.length - 1) return
    const path = location.slice(0, index + 1).join('/')
    setActivePermissionDetail('Phân quyền chức năng')
    setActiveClassDetail('Thông tin chung')
    navigate(path, { replace: true })
  }
  return (
    <>
      {isMobile && !isHiddened && (
        <div className='flex items-center gap-2 p-3' style={{ borderBottom: '1px solid #DCDEEF' }}>
          {btnBack && (
            <Svg
              src='/assets/icons/common/arrow-left.svg'
              className='h-6 w-6 cursor-pointer'
              onClick={() => navigate(-1)}
            />
          )}
          <h1 className='text-xl max-xs:text-md font-semibold'>{title.at(-1)}</h1>
        </div>
      )}
      <div className='p-3 sm:px-6 sm:py-3 bg-background-1 h-full'>
        {!isMobile && (
          <Breadcrumb
            items={[
              {
                title: (
                  <span className='cursor-pointer hover:text-[#273266]' onClick={() => navigate('/')}>
                    Trang chủ
                  </span>
                )
              },
              ...title.map((item, index) => ({
                title: (
                  <span className='cursor-pointer hover:text-[#273266]' onClick={() => handleClick(index)}>
                    {item}
                  </span>
                )
              }))
            ]}
            separator={<Svg src='/assets/icons/common/breadcrum.svg' className='h-6 w-6 text-gray-2' />}
            className='mb-3 text-gray-2'
          />
        )}

        {!isMobile && (
          <div className='flex items-center mb-5 gap-2'>
            {btnBack && (
              <Svg
                src='/assets/icons/common/arrow-left.svg'
                className='h-6 w-6 cursor-pointer'
                onClick={() => {
                  navigate(-1)
                  setActivePermissionDetail('Phân quyền chức năng')
                }}
              />
            )}
            <h1 className='text-xl max-xs:text-md font-semibold'>{title.at(-1)}</h1>
          </div>
        )}
        <div>{children}</div>
      </div>
    </>
  )
}

export default LayoutCotent
