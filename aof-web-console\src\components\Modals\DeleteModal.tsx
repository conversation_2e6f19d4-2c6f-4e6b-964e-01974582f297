import { Button, Image, Modal } from 'antd'
import Svg from '../Svg'
import { ModalProps } from '@/types/common'

const ModalDelete = ({ isModalOpen, handleCancel, handleOk, subTitle, isLoading, title, image }: ModalProps) => {
  const footerModal = (
    <div className='flex items-center justify-center gap-4 p-2 pt-4'>
      <Button
        className='min-w-[126px] bg-neutral-9 hover:opacity-70'
        size='large'
        variant='outlined'
        color='primary'
        disabled={isLoading}
        onClick={handleCancel}
      >
        Hủy
      </Button>
      <Button loading={isLoading} onClick={handleOk} type='primary' size='large' className='min-w-[126px] shadow-none'>
        Xác nhận
      </Button>
    </div>
  )
  return (
    <>
      <Modal open={isModalOpen} onCancel={handleCancel} footer={footerModal} width={335}>
        <div className='mt-6 flex flex-col items-center gap-4'>
          <Svg src='/assets/icons/common/icon-delete.svg' className='h-20 w-20' />
          <div>
            <h2 className='text-center font-semibold text-md'>{title}</h2>
            <p className='text-center font-normal text-sm'>{subTitle}</p>
            <div className='flex justify-center mt-4'>
              {image && (
                <Image
                  src={image}
                  height={200}
                  width={250}
                  className='object-cover rounded-lg'
                  loading='eager'
                  preview={false}
                />
              )}
            </div>
          </div>
        </div>
      </Modal>
    </>
  )
}

export default ModalDelete
