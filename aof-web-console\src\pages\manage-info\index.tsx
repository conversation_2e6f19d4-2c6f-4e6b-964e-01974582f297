import { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { Button, Input, Pagination, Segmented, Select } from 'antd'
import { PlusOutlined } from '@ant-design/icons'
import LayoutCotent from '@/layouts/LayoutCotent'
import { MESSAGE_STATUS } from '@/constants'
import Svg from '@/components/Svg'
import { InfoType, initialProperty, pageSize, TYPE_COURSE } from '@/types/components'
import { addNewGroup, getListGroups, updateGroup } from '@/services/group'
import CourseTable from '@/components/Tables/CourseTable'
import AcademicYearTable from '@/components/Tables/AcademicYearTable'
import { showCustomNotification } from '@/common/Notification'
import { getListAcademicYears } from '@/services/academicYear'
import { useSegmentStore } from '@/stores/useSegmentStore'
import { useInfoStore } from '@/stores/info'
import { ResponseList } from '@/types/common'
import { enumToOptions } from '@/helpers'
import useLayoutStore from '@/stores/layout'
import {
  checkFeature,
  FF_DEV_0009,
  FF_DEV_0010,
  FF_DEV_0029,
  FF_DEV_0030,
  FF_DEV_0033,
  FF_DEV_0034
} from '@/utils/feature-flags'
import useAuthStore from '@/stores/auth'
import CreateCourseModal from '@/components/Modals/CourseModal'

const ManageInfomation = () => {
  const navigate = useNavigate()

  const { isMobile } = useLayoutStore()
  const { setAcademicYears } = useInfoStore()
  const { activeInfo, setActiveInfo } = useSegmentStore()
  const { user, featFlagsAction, featFlagsChild } = useAuthStore()
  const isFFA = (code: string) => checkFeature(code, user, featFlagsAction)
  const isFFC = (code: string) => checkFeature(code, user, featFlagsChild)

  const [keySearch, setKeySearch] = useState('')
  const [dataSource, setDataSource] = useState<ResponseList<any>>()
  const [pageProperty, setPageProperty] = useState<{
    page: number
    maxSize: number
    totalElement: number
  }>(initialProperty)

  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [openCourseModal, setOpenCourseModal] = useState<boolean>(false)
  const [updateCourse, setUpdateCourse] = useState<boolean>(false)
  const [groupName, setGroupName] = useState<string>('')
  const [uuid, setUuid] = useState<string>('')

  const typeOptions = enumToOptions(InfoType)

  useEffect(() => {
    if (isFFA(FF_DEV_0029) || isFFA(FF_DEV_0033)) {
      getData()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeInfo, pageProperty])

  const getData = async () => {
    setIsLoading(true)
    try {
      let res
      if (activeInfo === InfoType['Quản lý khóa']) {
        res = await getListGroups({
          ...pageProperty,
          keySearch,
          groupType: InfoType['Quản lý khóa'],
          countStudent: 1
        })
      } else {
        res = await getListAcademicYears({
          ...pageProperty,
          keySearch,
          countSemester: 1
        })
        setAcademicYears(res.object.data)
      }

      if (res.message === MESSAGE_STATUS.SUCCESS) {
        setDataSource(res.object)
      }
    } catch {
      showCustomNotification({
        status: 'error',
        message: 'Thất bại!',
        description: 'Có lỗi xảy ra!'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleOk = async (data: string) => {
    setIsLoading(true)
    try {
      const res = updateCourse
        ? await updateGroup(uuid, { groupName: data })
        : await addNewGroup({ groupName: data, groupType: TYPE_COURSE })

      if (res.message === MESSAGE_STATUS.SUCCESS) {
        showCustomNotification({
          status: 'success',
          message: 'Thành công',
          description: updateCourse ? 'Cập nhật khóa học thành công!' : 'Thêm khóa học thành công!'
        })
        setUuid('')
        getData()
        setOpenCourseModal(false)
      }
    } catch {
      showCustomNotification({ status: 'error', message: 'Thất bại', description: 'Có lỗi xảy ra!' })
    } finally {
      setIsLoading(false)
    }
  }

  const handleChange = (e: any) => {
    setActiveInfo(e)
    setKeySearch('')
    setPageProperty({ ...pageProperty, page: 1, maxSize: 50 })
  }
  const isEnable = () => {
    if (activeInfo === InfoType['Quản lý khóa'] && isFFA(FF_DEV_0029)) return true
    if (activeInfo === InfoType['Quản lý năm học'] && isFFA(FF_DEV_0033)) return true
    return false
  }

  return (
    <LayoutCotent title={['Quản lý thông tin']}>
      <CreateCourseModal
        isLoading={isLoading}
        isModalOpen={openCourseModal}
        update={updateCourse}
        groupName={groupName}
        handleOk={handleOk}
        handleCancel={() => {
          setOpenCourseModal(false)
          setUpdateCourse(false)
        }}
      />
      <Segmented
        value={activeInfo}
        onChange={(e) => handleChange(e)}
        size='large'
        options={typeOptions.filter(
          (item) =>
            (item.value === InfoType['Quản lý khóa'] && isFFC(FF_DEV_0009)) ||
            (item.value === InfoType['Quản lý năm học'] && isFFC(FF_DEV_0010))
        )}
        className='mb-5 border max-sm:flex max-sm:[&_.ant-segmented-item]:w-1/2'
      />
      <div className='flex gap-2 sm:gap-5 items-end mb-5'>
        {isEnable() && (
          <>
            <div className='w-full'>
              <label htmlFor='search'>Tìm kiếm</label>
              <Input
                id='search'
                size='large'
                className='w-full border-neutral-7'
                placeholder='Nhập nội dung tìm kiếm'
                value={keySearch}
                onChange={(e) => {
                  setKeySearch(e.target.value)
                }}
                onPressEnter={() => {
                  setPageProperty({ ...pageProperty, page: 1 })
                }}
              />
            </div>
            <Button
              size='large'
              type='primary'
              icon={<Svg src='/assets/icons/common/search.svg' className='h-5 w-5 mt-1' />}
              onClick={() => {
                setPageProperty({ ...pageProperty, page: 1 })
              }}
            >
              <span className='hidden sm:block'>Tìm kiếm</span>
            </Button>
          </>
        )}
        {(isFFA(FF_DEV_0030) || isFFA(FF_DEV_0034)) && (
          <Button
            size='large'
            icon={<PlusOutlined />}
            color='primary'
            variant='outlined'
            onClick={() => {
              if (activeInfo === InfoType['Quản lý khóa'] && isFFA(FF_DEV_0030)) {
                setGroupName('')
                setUpdateCourse(false)
                setOpenCourseModal(true)
              } else if (isFFA(FF_DEV_0034)) {
                navigate('/manage-info/academic-year/create', { state: { update: false } })
              }
            }}
          >
            <span
              className={`${(!isFFA(FF_DEV_0029) && activeInfo === InfoType['Quản lý khóa']) || (!isFFA(FF_DEV_0033) && activeInfo === InfoType['Quản lý năm học']) ? 'block' : 'hidden'} sm:block`}
            >
              Thêm mới
            </span>
          </Button>
        )}
      </div>
      {activeInfo === InfoType['Quản lý khóa']
        ? isFFA(FF_DEV_0029) && (
            <CourseTable
              data={dataSource?.data || []}
              page={dataSource?.page || 1}
              maxSize={dataSource?.maxSize || 10}
              isLoading={isLoading}
              openCourseModal={openCourseModal}
              refreshData={getData}
              setIsLoading={setIsLoading}
              setOpenCourseModal={setOpenCourseModal}
              onEditCourse={(groupName: string, uuid: string) => {
                setGroupName(groupName)
                setUuid(uuid)
                setUpdateCourse(true)
                setOpenCourseModal(true)
              }}
            />
          )
        : isFFA(FF_DEV_0033) && (
            <AcademicYearTable
              refreshData={getData}
              setIsLoading={setIsLoading}
              isLoading={isLoading}
              data={dataSource?.data || []}
              page={dataSource?.page || 1}
              maxSize={dataSource?.maxSize || 10}
            />
          )}

      {isEnable() && dataSource?.data && dataSource.data.length > 0 && (
        <div className='my-4 flex max-sm:flex-col max-sm:items-center items-center justify-between gap-4'>
          <div className='flex items-center gap-2'>
            <p className='text-xs font-normal text-gray-400'>Số bản ghi mỗi trang </p>
            <Select
              size='small'
              options={pageSize}
              value={pageProperty.maxSize}
              onChange={(e) => setPageProperty({ ...pageProperty, maxSize: e, page: 1 })}
            />
            <p className='text-xs font-normal text-gray-400 hidden sm:block'>
              trên tổng {dataSource.totalElement} dữ liệu{' '}
            </p>
          </div>
          <Pagination
            onChange={(page) => {
              setPageProperty({ ...pageProperty, page })
            }}
            align='end'
            showSizeChanger={isMobile}
            current={dataSource.page}
            defaultCurrent={1}
            pageSize={dataSource.maxSize}
            defaultPageSize={10}
            total={dataSource.totalElement}
          />
        </div>
      )}
    </LayoutCotent>
  )
}

export default ManageInfomation
