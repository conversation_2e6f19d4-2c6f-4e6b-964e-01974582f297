import { CustomField } from '@/types/common'
import { customFieldGroupList, customFieldPersonList, TYPE_PERSON } from '@/types/components'
import { convertDatetoString, findCustomField } from '.'
import dayjs, { Dayjs } from 'dayjs'
import { AttendanceRecord, AttendanceRecordParams } from '@/types/session'
import { Person, PersonGroup } from '@/types/person'
export const setParams = (
  values: Record<string, any>,
  personType: number,
  listCustomFields: CustomField[],
  face?: any
) => {
  const params = {
    personCode: values.personCode.trim(),
    fullName: values.fullName.trim(),
    status: values.status,
    personType,
    email: values.email.trim(),
    gender: values.gender,
    phoneNo: values.phoneNo ? values.phoneNo : undefined,
    remark: values.remark,
    dob: values.dob ? convertDatetoString(values.dob) : undefined,
    uuidGroup: values.uuidGroup,
    customFields: [] as CustomField[],
    faces: face ? [face] : []
  }

  if (personType === TYPE_PERSON['Sinh viên']) {
    const customFields: CustomField[] = [
      values[customFieldPersonList[1]] && {
        uuidCustomField: findCustomField(customFieldPersonList[1], listCustomFields),
        customFieldValue: values[customFieldPersonList[1]].trim()
      },
      values[customFieldPersonList[2]] && {
        uuidCustomField: findCustomField(customFieldPersonList[2], listCustomFields),
        customFieldValue: values[customFieldPersonList[2]].trim()
      }
    ].filter(Boolean) as CustomField[] // Remove falsy values

    params.customFields = [...params.customFields, ...customFields]
  } else {
    const customFields: CustomField[] = [
      values[customFieldPersonList[0]] && {
        uuidCustomField: findCustomField(customFieldPersonList[0], listCustomFields),
        customFieldValue: values[customFieldPersonList[0]].trim()
      }
    ].filter(Boolean) as CustomField[]

    params.customFields = [...params.customFields, ...customFields]
  }

  return params
}

export const setGroupParams = (values: Record<string, any>, groupType: number, listCustomFields: CustomField[]) => {
  const params = {
    groupName: values.groupName,
    groupType,
    groupCode: values.groupCode,
    uuidSemester: values.uuidSemester,
    startDate: dayjs(values.rangeTime[0]).format('DD/MM/YYYY'),
    endDate: dayjs(values.rangeTime[1]).format('DD/MM/YYYY'),
    customFields: [] as CustomField[]
  }

  const customFields: CustomField[] = [
    values[customFieldGroupList[0]] && {
      uuidCustomField: findCustomField(customFieldGroupList[0], listCustomFields),
      customFieldValue: values[customFieldGroupList[0]]
    },
    values[customFieldGroupList[1]] && {
      uuidCustomField: findCustomField(customFieldGroupList[1], listCustomFields),
      customFieldValue: values[customFieldGroupList[1]]
    }
  ].filter(Boolean) as CustomField[]

  params.customFields = [...params.customFields, ...customFields]

  return params
}

export const setSessionParams = (
  status: boolean,
  date: Dayjs,
  pickTime: [Dayjs | null, Dayjs | null],
  uuidGroup: string,
  dataTable: AttendanceRecord[]
) => {
  const params = {
    sessionDate: dayjs(date).format('DD/MM/YYYY'),
    uuidGroup,
    sessionAttendance: {
      startTime: dayjs(pickTime[0]).format('DD/MM/YYYY HH:mm:ss'),
      endTime: dayjs(pickTime[1]).format('DD/MM/YYYY HH:mm:ss'),
      status: status ? 2 : 1,
      attendanceRecords: dataTable.map((data) => ({
        uuidPerson: data.uuidPerson,
        checkInStatus: data.checkInStatus,
        uuidCheckInRecord: data.uuidCheckInRecord,
        attendanceStatus: data.attendanceStatus,
        approvalReason: data.approvalReason
      }))
    }
  }
  return params
}

export const setSessionAttendanceParams = (
  uuidSession: string,
  status: boolean,
  pickTime: [Dayjs | null, Dayjs | null],
  dataTable: AttendanceRecord[]
) => {
  const params = {
    uuidSession,
    startTime: dayjs(pickTime[0]).format('DD/MM/YYYY HH:mm:ss'),
    endTime: dayjs(pickTime[1]).format('DD/MM/YYYY HH:mm:ss'),
    status: status ? 2 : 1,
    attendanceRecords: dataTable.map((data) => ({
      uuidPerson: data.uuidPerson,
      checkInStatus: data.checkInStatus,
      uuidCheckInRecord: data.uuidCheckInRecord,
      attendanceStatus: data.attendanceStatus,
      approvalReason: data.approvalReason
    }))
  }
  return params
}

export const setAddAttendanceRecordParams = (
  uuidSessionAttendance: string,
  persons: AttendanceRecord[],
  dataTable: AttendanceRecord[]
): AttendanceRecordParams[] => {
  return persons.reduce((acc, person) => {
    const record = dataTable.find((data) => data.uuidPerson === person.uuidPerson)
    if (record) {
      acc.push({
        uuidSessionAttendance: uuidSessionAttendance,
        uuidPerson: person.uuidPerson,
        checkInStatus: record.checkInStatus,
        uuidCheckInRecord: record.uuidCheckInRecord,
        attendanceStatus: record.attendanceStatus,
        approvalReason: record.approvalReason
      })
    }
    return acc
  }, [] as AttendanceRecordParams[])
}

export const setUpdateAttendanceRecordParams = (
  records: AttendanceRecord[],
  dataTable: AttendanceRecord[]
): AttendanceRecordParams[] => {
  return records.reduce((acc, record) => {
    const data = dataTable.find((data) => data.uuidPerson === record.uuidPerson)
    if (data) {
      acc.push({
        uuidAttendanceRecord: record.uuidAttendanceRecord,
        checkInStatus: data.checkInStatus,
        uuidCheckInRecord: data.uuidCheckInRecord,
        attendanceStatus: data.attendanceStatus,
        approvalReason: data.approvalReason
      })
    }
    return acc
  }, [] as AttendanceRecordParams[])
}

export const setMultiplePersonParams = (persons: Person[], uuidGroup: string): PersonGroup[] => {
  return persons.map((person) => ({ personId: person.uuidPerson || person.personCode, groupId: uuidGroup }))
}
