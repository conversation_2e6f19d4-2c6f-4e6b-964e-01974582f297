import { MESSAGE_STATUS } from '@/constants'
import { checkInforUser, getInfo, getLicenseStatus } from '@/services/auth'
import { UserInfo } from '@/types/user'
import { create } from 'zustand'
import { createJSONStorage, persist } from 'zustand/middleware'
import ff from '@/constants/feature_flags.json'
import { useSegmentStore } from './useSegmentStore'
import { InfoType, PremissionType, TYPE_CLASS_CREDIT, TYPE_COURSE, TYPE_PERSON } from '@/types/components'
import { useInfoStore } from './info'
import useGroupStore from './group'
import { checkFeature, FF_DEV_0010, FF_DEV_0011, FF_DEV_0012 } from '@/utils/feature-flags'
import Cookies from 'js-cookie'

interface AuthState {
  isLoggedIn: boolean
  code: string
  accessToken: string
  accessTokenSSO: string
  id_token: string
  refreshToken: string
  user: UserInfo
  licenseStatus: number
  featFlagsAction: Record<string, any>
  featFlagsChild: Record<string, any>
  featFlagsParent: Record<string, any>
  login: (accessToken: string, refreshToken: string) => void
  setCode: (code: string) => void
  setTokenSSO: (accessToken: string, id_token: string) => void
  fetchUsers: () => Promise<void>
  checkUser: () => Promise<void>
  fetchLicenseStatus: () => Promise<void>
}

const cookieStorage = createJSONStorage(() => ({
  getItem: (name: string) => {
    const value = Cookies.get(name)
    return value ?? null
  },
  setItem: (name: string, value: string) => {
    Cookies.set(name, value, {
      expires: 7,
      path: '/',
      secure: true,
      sameSite: 'Lax'
    })
  },
  removeItem: (name: string) => {
    Cookies.remove(name)
  }
}))

const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      isLoggedIn: false,
      code: '',
      accessToken: '',
      accessTokenSSO: '',
      id_token: '',
      refreshToken: '',
      licenseStatus: 2,
      user: {} as UserInfo,
      featFlagsAction: {},
      featFlagsChild: {},
      featFlagsParent: {},

      checkUser: async () => {
        const { accessTokenSSO } = get()
        if (accessTokenSSO.trim() !== '') {
          const res = await checkInforUser(accessTokenSSO)
          if (res.message === MESSAGE_STATUS.SUCCESS) return
          else {
            resetAllStores()
            window.location.replace('/')
            return
          }
        }
      },
      fetchUsers: async () => {
        const res = await getInfo()
        if (res.message === MESSAGE_STATUS.SUCCESS) {
          const apiFlags = Object.fromEntries(
            (res.object.featureActions ?? []).map((action) => [action.actionCode, { ...action, on: true }])
          )

          const mergedFlagsAction = Object.fromEntries(
            Object.entries(ff.feat_flags_action).map(([key, defaultFlag]) => {
              const apiFlag = apiFlags[key]

              if (
                apiFlag &&
                (apiFlag.uuidFeatureAction === defaultFlag.uuidFeature || apiFlag.actionCode === defaultFlag.actionCode)
              ) {
                return [key, { ...defaultFlag, on: apiFlag.on }]
              }

              return [key, defaultFlag ?? apiFlag]
            })
          )
          const mergeFlagsChild = Object.fromEntries(
            Object.entries(ff.feat_flags_child).map(([key, flagParent]) => {
              const hasActiveChild = Object.values(mergedFlagsAction).some(
                (flagAction) => flagAction.uuidFeatureAction === flagParent.uuidFeature && flagAction.on === true
              )

              return [key, { ...flagParent, on: hasActiveChild }]
            })
          )
          const mergeFlagsParent = Object.fromEntries(
            Object.entries(ff.feat_flags).map(([key, flagParent]) => {
              const hasActiveChild = Object.values(mergeFlagsChild).some(
                (flagAction) => flagAction.uuidParentFeature === flagParent.uuidFeature && flagAction.on === true
              )
              const hasActiveChild2 = Object.values(mergedFlagsAction).some(
                (flagAction) => flagAction.uuidFeatureAction === flagParent.uuidFeature && flagAction.on === true
              )

              return [key, { ...flagParent, on: hasActiveChild || hasActiveChild2 }]
            })
          )

          set((state: any) => ({
            ...state,
            user: res.object,
            featFlagsAction: mergedFlagsAction,
            featFlagsChild: mergeFlagsChild,
            featFlagsParent: mergeFlagsParent
          }))

          const activeInfo = checkFeature(FF_DEV_0010, res.object, mergeFlagsChild)
          const activeRoom = checkFeature(FF_DEV_0011, res.object, mergeFlagsChild)
          const activeDevice = checkFeature(FF_DEV_0012, res.object, mergeFlagsChild)

          useSegmentStore.setState(() => ({
            activeInfo: activeInfo ? InfoType['Quản lý năm học'] : InfoType['Quản lý khóa'],
            active: activeRoom ? 'Quản lý phòng học' : activeDevice ? 'Máy điểm danh' : 'Camera',
            activePerson: TYPE_PERSON['Sinh viên'],
            activeClass: 'Lớp học',
            activeList: PremissionType['Danh sách nhóm quyền'],
            activeClassDetail: 'Thông tin chung',
            activePermissionDetail: 'Phân quyền chức năng',
            activePermissionPerson: 'Danh sách nhóm quyền',
            personType: TYPE_PERSON['Sinh viên'],
            groupType: TYPE_CLASS_CREDIT
          }))
        }
      },
      fetchLicenseStatus: async () => {
        try {
          const res = await getLicenseStatus()
          if (res.message === MESSAGE_STATUS.SUCCESS) {
            set({ licenseStatus: res.object.status })
          }
        } catch {
          console.log('Failed to fetch license status')
        }
      },

      login: async (accessToken, refreshToken) => {
        set({ isLoggedIn: true, accessToken, refreshToken })
        await get().fetchUsers()
        await get().fetchLicenseStatus()
      },

      setCode: (code) => {
        set({ code })
      },

      setTokenSSO: (accessToken, id_token) => {
        set({ accessTokenSSO: accessToken, id_token })
      }
    }),
    {
      name: 'auth-storage',
      storage: cookieStorage,
      partialize: (state: AuthState): Partial<AuthState> => ({
        isLoggedIn: state.isLoggedIn,
        code: state.code,
        accessTokenSSO: state.accessTokenSSO,
        id_token: state.id_token,
        accessToken: state.accessToken,
        refreshToken: state.refreshToken
      })
    }
  )
)

export default useAuthStore

export const resetAllStores = async () => {
  useSegmentStore.setState(() => ({
    activeInfo: InfoType['Quản lý khóa'],
    activePerson: TYPE_PERSON['Sinh viên'],
    activeClass: 'Lớp học',
    active: 'Quản lý phòng học',
    activeList: PremissionType['Danh sách nhóm quyền'],
    activeClassDetail: 'Thông tin chung',
    activePermissionDetail: 'Phân quyền chức năng',
    activePermissionPerson: 'Danh sách nhóm quyền',
    personType: TYPE_PERSON['Sinh viên'],
    groupType: TYPE_CLASS_CREDIT,
    session: { sessionDate: '', uuidSession: undefined }
  }))

  useInfoStore.setState(() => ({
    loadingData: false,
    academicYears: [],
    semesters: [],
    listStudents: []
  }))

  useAuthStore.setState(() => ({
    isLoggedIn: false,
    code: '',
    accessToken: '',
    refreshToken: '',
    user: {} as UserInfo,
    featFlagsAction: {},
    featFlagsChild: {},
    featFlagsParent: {}
  }))

  useGroupStore.setState(() => ({
    loading: false,
    groupType: TYPE_COURSE,
    listGroups: [],
    groupName: ''
  }))

  sessionStorage.clear()

  if ('persist' in useAuthStore) {
    await useAuthStore.persist.clearStorage()
  }
}
