import { Button, DatePicker, Divider, Modal } from 'antd'
import { ModalProps } from '@/types/common'
import { useState } from 'react'
import dayjs from 'dayjs'
import Svg from '../Svg'

const ModalSelectDate = ({ isModalOpen, handleOk, handleCancel }: ModalProps) => {
  const [selectDate, setSelectDate] = useState<dayjs.Dayjs>(dayjs())

  const footerModal = (
    <div className='flex items-center justify-center gap-4 p-2 pt-4'>
      <Button
        className='min-w-[126px] bg-neutral-9 hover:opacity-70'
        size='large'
        variant='outlined'
        color='primary'
        onClick={handleCancel}
      >
        Hủy
      </Button>
      <Button
        type='primary'
        size='large'
        className='min-w-[126px] shadow-none'
        disabled={!selectDate}
        onClick={() => handleOk && handleOk(dayjs(selectDate).format('DD/MM/YYYY'))}
      >
        <PERSON><PERSON><PERSON> nhận
      </Button>
    </div>
  )
  return (
    <>
      <Modal
        open={isModalOpen}
        onCancel={handleCancel}
        footer={footerModal}
        width={444}
        title={<p className='font-semibold text-md text-neutral-1'>Thêm mới</p>}
      >
        <Divider className='mt-0' />
        <div>
          <p className='mb-1'>
            Ngày <span className='text-error'>*</span>
          </p>
          <DatePicker
            className='w-full'
            size='large'
            format='DD/MM/YYYY'
            value={selectDate}
            suffixIcon={<Svg src='/assets/icons/common/datepicker.svg' className='h-4 w-4' />}
            onChange={(e) => setSelectDate(e)}
          />
        </div>
      </Modal>
    </>
  )
}

export default ModalSelectDate
