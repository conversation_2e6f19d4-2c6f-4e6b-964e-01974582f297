import { ApiResponse, ResponseList } from '@/types/common'
import { ExamStatus } from '@/types/components'
import {
  FaceParams,
  ImageCollection,
  ImageAcception,
  ImageSearchParams,
  MultiplePersonParam,
  Person,
  PersonParams,
  PersonParamsSearch,
  PersonStatus,
  ExamCheatSearchParams,
  ExamCheating,
  ImageParams
} from '@/types/person'
import axiosInstance from '@/utils/axios'

export type propertiesSort = 'fullName' | 'personCode' | 'email' | 'status'

//2.17. Thêm mới person.
export async function addNewPerson(params: PersonParams): Promise<ApiResponse<any>> {
  const response = await axiosInstance.post('/web-service/v1/people', params)
  return response.data
}

//2.18. Chỉnh sửa person.
export async function updatePerson(uuidPerson: string, params: PersonParams): Promise<ApiResponse<any>> {
  const response = await axiosInstance.put(`/web-service/v1/people/${uuidPerson}`, params)
  return response.data
}

//2.19. Xóa person.
export async function deletePerson(uuidPerson: string): Promise<ApiResponse<any>> {
  const response = await axiosInstance.delete(`/web-service/v1/people/${uuidPerson}`)
  return response.data
}

//2.20. Xem chi tiết person.
export async function getDetailPerson(uuidPerson: string): Promise<ApiResponse<Person>> {
  const response = await axiosInstance.get(`/web-service/v1/people/${uuidPerson}`)
  return response.data
}

//2.21. Danh sách person.
export async function getListPersons(params: PersonParamsSearch): Promise<ApiResponse<ResponseList<Person>>> {
  const response = await axiosInstance.get('/web-service/v1/people', { params })
  return response.data
}

//2.22. Thêm mới nhiều person.
export async function addMultiplePerson(people: PersonParams[]): Promise<ApiResponse<any>> {
  const response = await axiosInstance.post('/web-service/v1/people/multiple', { people })
  return response.data
}

//2.23. Cập nhật trạng thái nhiều person.
export async function updateMultipleStatus(people: PersonStatus[], idType?: number): Promise<ApiResponse<any>> {
  const response = await axiosInstance.put('/web-service/v1/people/multiple/status', { people, idType })
  return response.data
}

//2.36. Thêm mới nhiều person group
export async function addMultiplePersonGroup(params: MultiplePersonParam): Promise<ApiResponse<any>> {
  const response = await axiosInstance.post('/web-service/v1/person-groups/multiple', params)
  return response.data
}

//2.37. Xóa nhiều person group
export async function removeMultiplePersonGroup(params: MultiplePersonParam): Promise<ApiResponse<any>> {
  const response = await axiosInstance.post('/web-service/v1/person-groups/multiple/deletion', params)
  return response.data
}

//2.27. Thêm mới face.
export async function addFace(params: FaceParams): Promise<ApiResponse<any>> {
  const response = await axiosInstance.post('/web-service/v1/faces', params)
  return response.data
}

//2.28. Chỉnh sửa face.
export async function updateFaces(uuidFace: string, params: FaceParams): Promise<ApiResponse<any>> {
  const response = await axiosInstance.put(`/web-service/v1/faces/${uuidFace}`, params)
  return response.data
}

//2.51. Danh sách ảnh thu thập.
export async function getListImagesCollection(
  params: ImageSearchParams
): Promise<ApiResponse<ResponseList<ImageCollection>>> {
  const response = await axiosInstance.get('/web-service/v1/images', { params })
  return response.data
}

//2.52. Phê duyệt nhiều ảnh thu thập.
export async function approveMultipleImages(params: ImageAcception): Promise<ApiResponse<any>> {
  const response = await axiosInstance.put('/web-service/v1/images/multiple', params)
  return response.data
}

//2.53. Danh sách gian lận phòng thi.
export async function getListExamCheats(
  params: ExamCheatSearchParams
): Promise<ApiResponse<ResponseList<ExamCheating>>> {
  const response = await axiosInstance.get('/web-service/v1/exam-cheatings', { params })
  return response.data
}

//2.54. Cập nhật trạng thái nhiều gian lận phòng thi.
export async function confirmExamCheating(
  uuidExamCheating: string[],
  cheatingStatus: ExamStatus,
  uuidPerson: string
): Promise<ApiResponse<any>> {
  const response = await axiosInstance.put(`/web-service/v1/exam-cheatings/${uuidExamCheating}`, {
    cheatingStatus,
    uuidPerson
  })
  return response.data
}

//2.55. Xóa nhiều gian lận phòng thi.
export async function removeMultipleExamCheating(uuidExamCheatings: string[]): Promise<ApiResponse<any>> {
  const response = await axiosInstance.post('/web-service/v1/exam-cheatings/multiple/deletion', { uuidExamCheatings })
  return response.data
}

//2.82. Thu thập ảnh
export async function collectImage(params: ImageParams): Promise<ApiResponse<any>> {
  const response = await axiosInstance.post('/web-service/v1/images', params)
  return response.data
}
