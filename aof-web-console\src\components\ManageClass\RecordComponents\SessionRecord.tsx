import { MESSAGE_STATUS } from '@/constants'
import dayjs, { Dayjs } from 'dayjs'
import { useEffect, useState, useCallback } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { Button, Col, DatePicker, Divider, Pagination, Row, Select, Spin, Table, TableProps, TimePicker } from 'antd'
import RecordInfo from '../../RecordInfo'
import { initialProperty, MAX_SIZE_PAGE, pageSize } from '@/types/components'
import { AttendanceStatusField, CheckinStatusField } from '../../StatusField'
import { ResponseList } from '@/types/common'
import { addSession, addSessionAttendance, getPreviewAttendanceRecord } from '@/services/session'
import { AttendanceRecord } from '@/types/session'
import { showCustomNotification } from '@/common/Notification'
import { Person } from '@/types/person'
import Svg from '../../Svg'
import { setSessionAttendanceParams, setSessionParams } from '@/helpers/configData'
import useLayoutStore from '@/stores/layout'
import ModalCheckinRecordInfo from '@/components/Modals/CheckinRecordInfoModal'
import { SyncOutlined } from '@ant-design/icons'
import { useSegmentStore } from '@/stores/useSegmentStore'

const SessionRecord = () => {
  const navigate = useNavigate()
  const { session } = useSegmentStore()
  const { uuidGroup } = useParams()
  const { isMobile, isTablet } = useLayoutStore()

  const [date, setDate] = useState(dayjs())
  const [pickTime, setPickTime] = useState<[Dayjs | null, Dayjs | null]>()
  const [openModal, setOpenModal] = useState<boolean>(false)

  const [personAttendace, setPersonAttendace] = useState<AttendanceRecord>()
  const [dataSource, setDataSource] = useState<ResponseList<AttendanceRecord>>()
  const [pageProperty, setPageProperty] = useState({ ...initialProperty })

  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    const newDate = dayjs(session.sessionDate, 'DD/MM/YYYY')
    if (!newDate.isValid()) return

    const now = dayjs() // Lấy thời gian hiện tại
    setPickTime([
      newDate
        .hour(now.hour() - 2)
        .minute(now.minute())
        .second(now.second()),
      newDate
        .hour(now.hour())
        .minute(now.minute() + 5)
        .second(now.second())
    ])
    setDate(newDate)
  }, [session.sessionDate])

  const fetchRecords = useCallback(async () => {
    if (!uuidGroup || !pickTime?.[0] || !pickTime?.[1]) return
    setIsLoading(true)
    try {
      const res = await getPreviewAttendanceRecord({
        page: 1,
        maxSize: MAX_SIZE_PAGE,
        startTime: pickTime[0]?.format('DD/MM/YYYY HH:mm:ss'),
        endTime: pickTime[1]?.format('DD/MM/YYYY HH:mm:ss'),
        uuidGroup
      })
      if (res.message === MESSAGE_STATUS.SUCCESS) {
        setDataSource(res.object)
      }
    } catch {
      showCustomNotification({
        status: 'error',
        message: 'Thất bại!',
        description: 'Có lỗi xảy ra!'
      })
    } finally {
      setIsLoading(false)
    }
  }, [uuidGroup, pickTime])

  useEffect(() => {
    fetchRecords()
  }, [fetchRecords])

  const handleConfirm = async (status: boolean) => {
    try {
      if (!uuidGroup || !pickTime || !dataSource) return
      setIsLoading(true)
      let res
      if (session.uuidSession) {
        // TH1: Đã có uuidSession => Thêm mới điểm danh session attendance
        const sessionAttendParams = setSessionAttendanceParams(session.uuidSession, status, pickTime, dataSource.data)
        res = await addSessionAttendance(sessionAttendParams)
      } else {
        // TH1: Ko có uuidSession => Thêm mới điểm danh session
        const sessionParams = setSessionParams(status, date, pickTime, uuidGroup, dataSource.data)
        res = await addSession(sessionParams)
      }

      if (res.message === MESSAGE_STATUS.SUCCESS) {
        navigate(-1)
        showCustomNotification({
          status: 'success',
          message: 'Thành công!',
          description: `Thêm mới bản ghi điểm danh thành công.`
        })
      }
    } catch {
      showCustomNotification({
        status: 'error',
        message: 'Thất bại!',
        description: `Thêm mới bản ghi điểm danh thất bại.`
      })
    } finally {
      setIsLoading(false)
    }
  }

  const columns: TableProps<AttendanceRecord>['columns'] = [
    { title: 'STT', key: 'index', width: 48, align: 'center', render: (_, __, index) => index + 1 },
    {
      title: 'Ảnh đại diện',
      dataIndex: 'person',
      key: 'person',
      width: 110,
      render: (value: Person) => (
        <div className='flex justify-center'>
          {value?.faces && value?.faces.length > 0 ? (
            <img src={value.faces[0].objectUrl} alt='avatar' className='w-10 h-10 rounded-full object-cover' />
          ) : (
            <Svg src='/assets/icons/common/image-avatar.svg' className='w-10 h-10' />
          )}
        </div>
      )
    },
    {
      title: 'Họ tên',
      dataIndex: 'person',
      key: 'person',
      render: (value: Person, record) => (
        <p
          className='text-text-3 underline cursor-pointer'
          onClick={() => {
            setPersonAttendace(record)
            if (isMobile || isTablet) setOpenModal(true)
          }}
        >
          {value.fullName}
        </p>
      )
    },
    {
      title: 'ID',
      dataIndex: 'person',
      key: 'person',
      render: (value: Person) => value.personCode.toLocaleUpperCase()
    },
    {
      title: 'Trạng thái',
      dataIndex: 'checkInStatus',
      key: 'checkInStatus',
      render: (status) => <CheckinStatusField status={status} />
    },
    {
      title: 'Xác nhận của giáo viên',
      dataIndex: 'attendanceStatus',
      key: 'attendanceStatus',
      render: (status) => <AttendanceStatusField status={status} />
    },
    {
      title: 'Lý do',
      dataIndex: 'approvalReason',
      key: 'approvalReason',
      render: (value) => (value?.trim() === '' ? '-' : value)
    }
  ]

  return (
    <>
      <Spin spinning={isLoading}>
        {(isMobile || isTablet) && personAttendace?.uuidPerson && (
          <ModalCheckinRecordInfo
            isModalOpen={openModal}
            handleCancel={() => setOpenModal(false)}
            personAttendace={personAttendace}
            setPersonAttendace={setPersonAttendace}
            setData={setDataSource}
          />
        )}
        <div className='flex gap-5 mb-5 items-end'>
          <div className='max-sm:w-1/2'>
            <label>
              Ngày <span className='text-error'>*</span>
            </label>
            <DatePicker
              size='large'
              disabled
              value={date}
              className='w-full border-neutral-7'
              format='DD/MM/YYYY'
              suffixIcon={<Svg src='/assets/icons/common/datepicker.svg' className='h-4 w-4' />}
            />
          </div>
          <div className='max-sm:w-1/2'>
            <label>
              Giờ điểm danh <span className='text-error'>*</span>
            </label>
            <TimePicker.RangePicker
              size='large'
              value={pickTime}
              className='w-full border-neutral-7'
              format='HH:mm'
              onChange={(values) => setPickTime(values || pickTime)}
            />
          </div>
          <Button size='large' icon={<SyncOutlined spin={isLoading} />} onClick={fetchRecords} type='primary'>
            Tải dữ liệu
          </Button>
        </div>

        <Row gutter={[16, 16]}>
          <Col span={personAttendace?.uuidPerson && !isMobile && !isTablet ? 16 : 24}>
            <h1 className='mb-5 text-lg font-semibold'>Danh sách sinh viên</h1>
            <Table
              columns={columns}
              dataSource={dataSource?.data}
              pagination={false}
              rowKey={(record) => record.uuidPerson}
              scroll={{ x: 600, y: 570 }}
            />
            {dataSource?.data && dataSource.data.length > 0 && (
              <div className='mt-4 flex max-xs:flex-col items-center justify-between'>
                <div className='flex items-center gap-2'>
                  <p className='text-xs font-normal text-gray-400'>Số bản ghi mỗi trang</p>
                  <Select
                    size='small'
                    options={pageSize}
                    value={pageProperty.maxSize}
                    onChange={(e) => setPageProperty((prev) => ({ ...prev, maxSize: e, page: 1 }))}
                  />
                  <p className='text-xs font-normal text-gray-400 hidden xs:block'>
                    trên tổng {dataSource.totalElement} dữ liệu{' '}
                  </p>
                </div>
                <Pagination
                  onChange={(page) => setPageProperty((prev) => ({ ...prev, page }))}
                  current={dataSource.page}
                  className='mt-4'
                  pageSize={dataSource.maxSize}
                  total={dataSource.totalElement}
                  showSizeChanger={false}
                />
              </div>
            )}
          </Col>
          {personAttendace?.uuidPerson && !isMobile && !isTablet && (
            <Col span={8}>
              <h1 className='mb-5 text-lg font-semibold'>Thông tin điểm danh</h1>
              <RecordInfo
                personAttendace={personAttendace}
                setPersonAttendace={setPersonAttendace}
                setData={setDataSource}
              />
            </Col>
          )}
        </Row>
        {dataSource?.data && dataSource.data.length > 0 && (
          <>
            <Divider className='hidden xs:block' />
            <Row>
              <Col span={24}>
                <div className='flex items-center justify-center mt-5 gap-5'>
                  <Button
                    variant='outlined'
                    color='primary'
                    size='large'
                    style={{ width: 160 }}
                    onClick={() => handleConfirm(false)}
                  >
                    Lưu
                  </Button>

                  <Button type='primary' size='large' style={{ width: 160 }} onClick={() => handleConfirm(true)}>
                    Xác nhận lớp học
                  </Button>
                </div>
              </Col>
            </Row>
          </>
        )}
      </Spin>
    </>
  )
}

export default SessionRecord
