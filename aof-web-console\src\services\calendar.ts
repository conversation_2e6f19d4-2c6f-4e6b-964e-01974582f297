import { CalendarEvent, CalendarEventParams } from '@/types/calendar'
import { ApiResponse } from '@/types/common'
import axiosInstance from '@/utils/axios'

//2.31. Thêm mới calendar event
export async function addCalendarEvent(params: CalendarEventParams): Promise<ApiResponse<CalendarEvent[]>> {
  const response = await axiosInstance.post(`/web-service/v1/calendar-events`, params)
  return response.data
}

//2.32. Chỉnh sửa calendar event
export async function updateCalendar(uuidCalendarEvent: string, params: CalendarEvent): Promise<ApiResponse<any>> {
  const response = await axiosInstance.put(`/web-service/v1/calendar-events/${uuidCalendarEvent}`, params)
  return response.data
}

//2.33. Xóa calendar event.
export async function deleteCalendarEvent(uuidCalendarEvent: string): Promise<ApiResponse<CalendarEvent[]>> {
  const response = await axiosInstance.delete(`/web-service/v1/calendar-events/${uuidCalendarEvent}`)
  return response.data
}

//2.34. Xem chi tiết calendar event.
export async function getCalendarEventDetail(uuidCalendarEvent: string): Promise<ApiResponse<CalendarEvent>> {
  const response = await axiosInstance.delete(`/web-service/v1/calendar-events/${uuidCalendarEvent}`)
  return response.data
}

//2.35. Danh sách calendar event.
export async function getCalendarEvent(uuidCalendar: string): Promise<ApiResponse<CalendarEvent[]>> {
  const response = await axiosInstance.get(`/web-service/v1/calendar-events`, { params: { uuidCalendar } })
  return response.data
}
