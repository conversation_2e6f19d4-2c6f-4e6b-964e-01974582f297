import { Button, Modal } from 'antd'
import Svg from '../Svg'
import { ModalProps } from '@/types/common'

const ModalError = ({ isModalOpen, handleCancel }: ModalProps) => {
  const footerModal = (
    <div className='flex items-center justify-center gap-4 p-2 pt-4'>
      <Button onClick={handleCancel} type='primary' size='large' className='min-w-[126px] shadow-none'>
        Xác nhận
      </Button>
    </div>
  )
  return (
    <>
      <Modal open={isModalOpen} footer={footerModal} closable={false} width={335}>
        <div className='mt-6 flex flex-col items-center gap-4'>
          <Svg src='/assets/icons/common/icon-delete.svg' className='h-20 w-20' />
          <div>
            <h2 className='text-center font-semibold text-md'>Thất bại!</h2>
            <p className='text-center font-normal text-sm'>L<PERSON>y thông tin dữ liệu thất bại!</p>
            <p className='text-center font-normal text-sm'>Quay lại trang trước.</p>
          </div>
        </div>
      </Modal>
    </>
  )
}

export default ModalError
