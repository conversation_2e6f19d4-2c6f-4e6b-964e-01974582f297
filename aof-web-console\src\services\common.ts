import { ApiResponse, CustomField, Image } from '@/types/common'
import { EntityType } from '@/types/components'
import axiosInstance from '@/utils/axios'
import axios from 'axios'

export async function uploadFileToPresignedUrl(postUrl: string, formData: FormData) {
  return axios.post(postUrl, formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  })
}

//2.24. L<PERSON>y thông tin để upload file.
export async function uploadImage(contentType: string): Promise<ApiResponse<Image>> {
  const response = await axiosInstance.post('/web-service/v1/file/presigned-post-form-data', {
    contentType
  })
  return response.data
}

//2.25. Danh sách custom field.
export async function getCustomFields(entityType: EntityType): Promise<ApiResponse<any>> {
  const response = await axiosInstance.get('/web-service/v1/custom-fields', { params: { entityType } })
  return response.data
}

//2.26. Chỉnh sửa custom fields của một person.
export async function updateCustomFieldsPerson(
  uuidPerson: string,
  customFields: CustomField[]
): Promise<ApiResponse<any>> {
  const response = await axiosInstance.put(`/web-service/v1/people/${uuidPerson}/custom-fields`, { customFields })
  return response.data
}

//2.30. Chỉnh sửa custom fields của một group.
export async function updateCustomFieldsGroup(
  uuidGroup: string,
  customFields: CustomField[]
): Promise<ApiResponse<any>> {
  const response = await axiosInstance.put(`/web-service/v1/groups/${uuidGroup}/custom-fields`, { customFields })
  return response.data
}

//2.56. Lấy ảnh từ hikvison.
export async function getImageFromHik(picUri: string): Promise<ApiResponse<any>> {
  const response = await axiosInstance.post('/web-service/v1/hik-person-in-out-events/pictures/retrieve', { picUri })
  return response.data
}
