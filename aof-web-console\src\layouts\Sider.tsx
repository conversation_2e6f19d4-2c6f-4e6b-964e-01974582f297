/* eslint-disable react-refresh/only-export-components */
import { useEffect, useState } from 'react'
import Svg from '@/components/Svg'
import { Layout, Menu, MenuProps } from 'antd'
import { useLocation, useNavigate } from 'react-router-dom'
import { PATH } from '@/types/common'
import { InfoType, TYPE_PERSON, URL } from '@/types/components'
import { useSegmentStore } from '@/stores/useSegmentStore'
import {
  checkFeature,
  FF_DEV_0000,
  FF_DEV_0001,
  FF_DEV_0002,
  FF_DEV_0003,
  FF_DEV_0004,
  FF_DEV_0005,
  FF_DEV_0006,
  FF_DEV_0029,
  FF_DEV_0033,
  FF_DEV_0045,
  isAdmin
} from '@/utils/feature-flags'
import useAuthStore from '@/stores/auth'
import LoadingComponent from '@/common/Loading'

export const getMenu = (user: any): PATH[] => {
  const baseMenu: PATH[] = [
    {
      label: 'Quản lý thông tin',
      icon: '/assets/icons/menu/info.svg',
      activeIcon: '/assets/icons/menu/info-active.svg',
      actionCode: FF_DEV_0001,
      url: '/manage-info'
    },
    {
      label: 'Quản lý người dùng',
      icon: '/assets/icons/menu/users.svg',
      activeIcon: '/assets/icons/menu/users-active.svg',
      actionCode: FF_DEV_0002,
      url: '/manage-user'
    },
    {
      label: 'Quản lý lớp',
      icon: '/assets/icons/menu/class.svg',
      activeIcon: '/assets/icons/menu/class-active.svg',
      actionCode: FF_DEV_0003,
      url: '/manage-class'
    },
    {
      label: 'Phòng học và Thiết bị',
      icon: '/assets/icons/menu/device.svg',
      activeIcon: '/assets/icons/menu/device-active.svg',
      actionCode: FF_DEV_0004,
      url: '/manage-room-device'
    },
    {
      label: 'Quản lý gian lận phòng thi',
      icon: '/assets/icons/menu/safeexam.svg',
      activeIcon: '/assets/icons/menu/safeexam-active.svg',
      actionCode: FF_DEV_0005,
      url: '/manage-exam'
    },
    {
      label: 'Phân quyền',
      icon: '/assets/icons/menu/feature.svg',
      activeIcon: '/assets/icons/menu/feature-active.svg',
      actionCode: FF_DEV_0006,
      url: '/manage-feature'
    }
  ]

  if (isAdmin(user)) {
    baseMenu.push({
      label: 'Cài đặt cấu hình',
      icon: '/assets/icons/menu/settings.svg',
      activeIcon: '/assets/icons/menu/settings-active.svg',
      actionCode: FF_DEV_0045,
      url: '/settings'
    })
  } else {
    baseMenu.push({
      label: 'Thông tin cá nhân',
      icon: '/assets/icons/menu/users.svg',
      activeIcon: '/assets/icons/menu/users-active.svg',
      actionCode: FF_DEV_0000,
      url: '/personal-info'
    })
  }

  return baseMenu
}

export default function Sider({ isMobile }: { isMobile: boolean }) {
  const { Sider } = Layout
  const navigate = useNavigate()
  const location = useLocation()

  const { setActivePerson, setActiveInfo, setActiveClass, setActiveClassDetail } = useSegmentStore()
  const { user, featFlagsParent, featFlagsAction } = useAuthStore()
  const isFFP = (code: string) => checkFeature(code, user, featFlagsParent)
  const isFFA = (code: string) => checkFeature(code, user, featFlagsAction)

  const [collapsed, setCollapsed] = useState(isMobile ? true : false)
  const [currentPath, setCurrentPath] = useState<URL>(location.pathname as URL)

  useEffect(() => {
    const check = getMenu(user).find((path) => location.pathname.includes(path.url))
    if (check) {
      setCurrentPath(check.url)
    }
  }, [location.pathname, user])

  useEffect(() => {
    if (isMobile) {
      setCollapsed(true)
    } else {
      setCollapsed(false)
    }
  }, [isMobile])

  const filteredMenu = getMenu(user).filter(
    (feature) => isFFP(feature.actionCode) || feature.actionCode === FF_DEV_0045 || feature.actionCode === FF_DEV_0000
  )
  if (!featFlagsParent || Object.keys(featFlagsParent).length === 0) {
    return <LoadingComponent />
  }

  const menuItems: MenuProps['items'] = filteredMenu.map((item) => ({
    key: item.url,
    label: item.label,
    icon: <Svg src={currentPath === item.url ? item.activeIcon : item.icon} className='h-5 w-5' />
  }))

  const info = !isFFA(FF_DEV_0029) && isFFA(FF_DEV_0033) ? InfoType['Quản lý năm học'] : InfoType['Quản lý khóa']

  return (
    <Sider
      trigger={null}
      collapsible
      collapsed={collapsed}
      width={312}
      className={`bg-background-1 ${!collapsed && 'p-6'} hidden xs:block`}
      style={{ borderRight: '1px solid #DCDEEF' }}
    >
      <div className={`flex mb-2 items-center ${!collapsed ? 'justify-between' : 'justify-center pt-6'}`}>
        {!collapsed && <h1 className='uppercase text-sm font-semibold'>Menu</h1>}
        <Svg
          src='/assets/icons/menu/arrow-left.svg'
          className={`h-5 w-5 ${collapsed && 'rotate-180'} cursor-pointer`}
          onClick={() => setCollapsed((pre) => !pre)}
        />
      </div>
      <Menu
        mode='inline'
        className='!border-none'
        items={menuItems}
        selectedKeys={[currentPath]}
        onSelect={(e: any) => {
          navigate(e.key)
          setActivePerson(TYPE_PERSON['Sinh viên'])
          setActiveInfo(info)
          setActiveClass('Lớp học')
          setActiveClassDetail('Thông tin chung')
          setCurrentPath(e.key)
        }}
        defaultSelectedKeys={[location.pathname]}
      />
    </Sider>
  )
}
