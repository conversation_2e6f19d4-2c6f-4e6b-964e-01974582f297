import Svg from '@/components/Svg'
import CameraTable from '@/components/Tables/CameraTable'
import DeviceTable from '@/components/Tables/DeviceTable'
import RoomTable from '@/components/Tables/RoomTable'
import LayoutCotent from '@/layouts/LayoutCotent'
import useAuthStore from '@/stores/auth'
import useLayoutStore from '@/stores/layout'
import { useSegmentStore } from '@/stores/useSegmentStore'
import { initialProperty, roomDeviceList } from '@/types/components'
import { checkFeature, FF_DEV_0011, FF_DEV_0012, FF_DEV_0013, FF_DEV_0046 } from '@/utils/feature-flags'
import { PlusOutlined } from '@ant-design/icons'
import { Button, Form, Input, Segmented, Spin } from 'antd'
import { useEffect, useState } from 'react'

const ManageRoomftDevices = () => {
  const { isMobile } = useLayoutStore()
  const { user, featFlagsChild, featFlagsAction } = useAuthStore()
  const { active, setActive } = useSegmentStore()
  const isFFC = (code: string) => checkFeature(code, user, featFlagsChild)
  const isFFA = (code: string) => checkFeature(code, user, featFlagsAction)

  const [keySearch, setKeySearch] = useState<string>('')
  const [params, setParams] = useState({ ...initialProperty, keySearch: '' })

  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [openModal, setOpenModal] = useState<boolean>(false)

  useEffect(() => {
    setParams({ ...initialProperty, keySearch: '' })
    setKeySearch('')
  }, [active])

  const view = (() => {
    switch (active) {
      case 'Quản lý phòng học':
        return <RoomTable params={params} setIsLoading={setIsLoading} setParams={setParams} />
      case 'Máy điểm danh':
        return <DeviceTable params={params} setIsLoading={setIsLoading} setParams={setParams} />
      case 'Camera':
        return (
          <CameraTable
            params={params}
            setIsLoading={setIsLoading}
            setParams={setParams}
            openCameraModal={openModal}
            setOpenCameraModal={setOpenModal}
          />
        )
      default:
        return <></>
    }
  })()

  console.log(isFFA(FF_DEV_0046))

  return (
    <LayoutCotent title={['Phòng học và thiết bị']}>
      <Spin spinning={isLoading}>
        <div className='overflow-x-auto' style={{ scrollbarWidth: 'none' }}>
          <Segmented
            value={active}
            onChange={(value) => {
              setActive(value)
              setKeySearch('')
            }}
            size='large'
            options={roomDeviceList.filter(
              (item) =>
                (item === 'Quản lý phòng học' && isFFC(FF_DEV_0011)) ||
                (item === 'Máy điểm danh' && isFFC(FF_DEV_0012)) ||
                (item === 'Camera' && isFFC(FF_DEV_0013))
            )}
            className='mb-5 border'
          />
        </div>
        <div className='flex gap-5 items-end mb-5'>
          <Form layout='vertical' className='w-full flex gap-5'>
            <Form.Item label='Tìm kiếm' className='mb-0 min-w-[220px] w-full'>
              <Input
                size='large'
                className='w-full border-neutral-7'
                placeholder='Nhập tìm kiếm'
                value={keySearch}
                onChange={(e) => setKeySearch(e.target.value)}
                onPressEnter={() => setParams({ ...params, keySearch, page: 1, maxSize: 50 })}
              />
            </Form.Item>
          </Form>
          <Button
            size='large'
            type='primary'
            icon={<Svg src='/assets/icons/common/search.svg' className='h-5 w-5 mt-1' />}
            onClick={() => setParams({ ...params, keySearch, page: 1, maxSize: 50 })}
          >
            {!isMobile && 'Tìm kiếm'}
          </Button>
          {active === 'Camera' && isFFA(FF_DEV_0046) && (
            <Button
              size='large'
              color='primary'
              variant='outlined'
              icon={<PlusOutlined />}
              onClick={() => setOpenModal(true)}
            >
              {!isMobile && 'Thêm mới'}
            </Button>
          )}
        </div>
        {view}
      </Spin>
    </LayoutCotent>
  )
}

export default ManageRoomftDevices
