import { Location } from './room_device'

export interface CalendarEventParams {
  uuidCalendar: string
  title?: string
  description?: string
  startTime: string
  endTime: string
  recurrenceRule?: RecurrenceRule
  location?: Location
  uuidLocation?: string
}

export interface CalendarEvent extends CalendarEventParams {
  uuidCalendarEvent?: string
}
interface RecurrenceRule {
  uuidRecurrenceRule?: string
  byday: string
  startDate: string
  endDate: string
}
