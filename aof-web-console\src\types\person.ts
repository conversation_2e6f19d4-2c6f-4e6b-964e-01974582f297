import { CustomField, ParamsSearch } from './common'
import { ExamStatus, Gender, ImageStatus, UserStatus } from './components'
import { Group } from './group'
import { Location } from './room_device'

export interface PersonParams {
  personCode: string
  fullName: string
  status: UserStatus
  personType: number
  email: string
  gender?: Gender
  phoneNo?: string
  remark?: string
  dob?: string
  groupName?: string
  username?: string
  uuidGroup?: string
  customFields?: CustomField[]
  faces?: Face[]
}

export interface Person extends PersonParams {
  uuidPerson: string
}

export interface Face {
  bucketName: string
  objectName: string
  uuidFace?: string
  objectUrl?: string
  isProfile?: number
  uuidFile?: string
}

export interface FaceParams {
  bucketName: string
  objectName: string
  uuidPerson?: string
}

export interface PersonParamsSearch extends ParamsSearch {
  status?: number
  uuidGroups?: string
  personTypes?: string
}

export interface PersonGroup {
  personId: string
  groupId: string
}

export interface MultiplePersonParam {
  personGroups: PersonGroup[]
  personIdType?: number
  groupIdType?: number
}

export interface PersonStatus {
  id: string
  status: UserStatus
}

export interface PersonStatusParams {
  people: PersonStatus[]
  idType: number
}

export interface Creator {
  uuidPerson: string
  personCode: string
  username: string
  fullName: string
}

export interface ImageCollection {
  uuidImage: string
  approvalStatus: ImageStatus
  approvedAt: string
  approver: Person
  createdAt: string
  person: Person
  file: Face
}

export interface ImageSearchParams extends ParamsSearch {
  startTime: string
  endTime: string
  approvalStatuses?: string
}

export interface ImageAcception {
  uuidImages: any[]
  approvalStatus: number
}

export interface ExamCheatingPerson {
  uuidPerson: string
  matchRate: string
  confirmed: number
  person: Person
}

export interface ExamCheating {
  uuidExamCheating: string
  cheatingType: number
  cheatedAt: string
  cheatingStatus: ExamStatus
  statusUpdatedAt: string
  file: Face
  location: Location
  imageUrl: string
  thumbnailUrl: string
  videoUrl: string
  group: Group
  persons: ExamCheatingPerson[]
}

export interface ExamCheatSearchParams extends ParamsSearch {
  startTime: string
  endTime: string
  cheatingStatuses?: string
  uuidLocations?: string
}

export interface ImageParams {
  bucketName: string
  objectName: string
  uuidPerson: string
}
