import useAuthStore, { resetAllStores } from '@/stores/auth'
import axios, { AxiosInstance } from 'axios'
import { getConfig } from '@/utils/config' // Thêm import này

const axiosInstance: AxiosInstance = axios.create({
  baseURL: getConfig('API_URL'),
  headers: {
    'Content-Type': 'application/json'
  }
})

axiosInstance.interceptors.request.use(
  (config) => {
    const token = useAuthStore.getState().accessToken
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

axiosInstance.interceptors.response.use(
  (response) => {
    return response
  },
  async (error) => {
    const { response } = error
    if (response && response.status === 401) {
      resetAllStores()
      window.location.replace('/')
      return
    } else {
      console.error('Error:', error.message)
      return Promise.reject(error)
    }
  }
)

export default axiosInstance
