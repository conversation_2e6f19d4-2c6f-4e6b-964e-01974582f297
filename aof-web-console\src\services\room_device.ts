import { ApiResponse, ParamsSearch, ResponseList } from '@/types/common'
import { Camera, Device, Location, ParamsCamera } from '@/types/room_device'
import axiosInstance from '@/utils/axios'

//2.47. Danh sách location.
export async function getListRooms(params: ParamsSearch): Promise<ApiResponse<ResponseList<Location>>> {
  const response = await axiosInstance.get('/web-service/v1/locations', { params })
  return response.data
}

//2.48. Danh sách acs devices (thiết bị điểm danh).
export async function getListDevices(params: ParamsSearch): Promise<ApiResponse<ResponseList<Device>>> {
  const response = await axiosInstance.get('/web-service/v1/hik-acs-devs', { params })
  return response.data
}

//2.49. Danh sách camera.
export async function getListCameras(params: ParamsSearch): Promise<ApiResponse<ResponseList<Camera>>> {
  const response = await axiosInstance.get('/web-service/v1/hik-cameras', { params })
  return response.data
}

//2.50. Chỉnh sửa camera.
export async function updateCamera(cameraIndexCode: string, body: ParamsCamera): Promise<ApiResponse<any>> {
  const response = await axiosInstance.put(`/web-service/v1/hik-cameras/${cameraIndexCode}`, body)
  return response.data
}

//2.83. Thêm mới camera.
export async function addCamera(body: ParamsCamera): Promise<ApiResponse<any>> {
  const response = await axiosInstance.post(`/web-service/v1/hik-cameras`, body)
  return response.data
}

//2.84. Xóa camera.
export async function deleteCamera(cameraIndexCode: string): Promise<ApiResponse<any>> {
  const response = await axiosInstance.delete(`/web-service/v1/hik-cameras/${cameraIndexCode}`)
  return response.data
}
