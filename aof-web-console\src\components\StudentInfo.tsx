import { <PERSON><PERSON>, Col, Image, Row, Spin } from 'antd'
import { CloseOutlined } from '@ant-design/icons'
import { getDetailPerson } from '@/services/person'
import { showCustomNotification } from '@/common/Notification'
import { Person } from '@/types/person'
import { useEffect, useState } from 'react'
import { UserStatusField } from './StatusField'

const StudentInformation = ({
  uuidPerson,
  setUuidPerson
}: {
  uuidPerson: string
  setUuidPerson: (e: string) => void
}) => {
  const [data, setData] = useState<Person>()
  const [isLoading, setIsLoading] = useState<boolean>(false)

  useEffect(() => {
    if (uuidPerson) {
      getData()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [uuidPerson])

  const getData = async () => {
    try {
      setIsLoading(true)
      const res = await getDetailPerson(uuidPerson)
      setData(res.object)
    } catch {
      showCustomNotification({
        status: 'error',
        message: 'Thất bại!',
        description: '<PERSON><PERSON><PERSON> thông tin học sinh thất bại!'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const renderGender = () => {
    switch (data?.gender) {
      case 0:
        return 'Khác'
      case 1:
        return 'Nam'
      case 2:
        return 'Nữ'
      default:
        return ''
    }
  }

  const valueCustomFields = (): Record<string, string> => {
    if (data?.customFields?.length) {
      return data.customFields.reduce<Record<string, string>>((acc, field) => {
        if (field.customFieldName && field.customFieldValue) {
          acc[field.customFieldName] = field.customFieldValue
        }
        return acc
      }, {})
    }
    return { school: '-', specialization: '-' }
  }

  const imageUrl = data?.faces?.[0]?.objectUrl || '/assets/icons/common/image-avatar.svg'

  return (
    <div className='border-dashed border border-text-primary rounded-2xl bg-background-row'>
      <Spin spinning={isLoading}>
        <div className='bg-background-tick p-3 rounded-t-2xl flex items-center justify-between'>
          <div>
            <h1 className='text-sm font-semibold text-center'>Ảnh đại diện</h1>
          </div>
          <Button type='text' shape='circle' icon={<CloseOutlined />} onClick={() => setUuidPerson('')} />
        </div>
        <div className='pt-4 flex justify-center'>
          <Image src={imageUrl} className='max-w-[200px] max-h-[200px]' preview={false} />
        </div>
        <div className='p-4'>
          <Row gutter={[24, 8]}>
            <Col span={9}>Họ và tên:</Col>
            <Col span={15} className='text-neutral-2'>
              {data?.fullName || '-'}
            </Col>

            <Col span={9}>Email:</Col>
            <Col span={15} className='text-neutral-2'>
              {data?.email || '-'}
            </Col>

            <Col span={9}>ID:</Col>
            <Col span={15} className='text-neutral-2'>
              {data?.personCode.toLocaleUpperCase() || '-'}
            </Col>

            <Col span={9}>Ngày sinh:</Col>
            <Col span={15} className='text-neutral-2'>
              {data?.dob || '--/--/----'}
            </Col>

            <Col span={9}>Giới tính:</Col>
            <Col span={15} className='text-neutral-2'>
              {renderGender()}
            </Col>

            <Col span={9}>Khoa:</Col>
            <Col span={15} className='text-neutral-2'>
              {valueCustomFields()?.school}
            </Col>

            <Col span={9}>Chuyên ngành:</Col>
            <Col span={15} className='text-neutral-2'>
              {valueCustomFields()?.specialization}
            </Col>

            <Col span={9}>Số điện thoại:</Col>
            <Col span={15} className='text-neutral-2'>
              {data?.phoneNo || ''}
            </Col>

            <Col span={9}>Trạng thái tài khoản:</Col>
            <Col span={15} className='text-neutral-2'>
              <UserStatusField status={data?.status || 0} />
            </Col>
          </Row>
        </div>
      </Spin>
    </div>
  )
}

export default StudentInformation
