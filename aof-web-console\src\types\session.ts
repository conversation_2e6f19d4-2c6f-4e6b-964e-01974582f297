import { ParamsSearch } from './common'
import { Creator, Person } from './person'

export interface SessionParamsSearch extends ParamsSearch {
  startTime: string
  endTime: string
  uuidGroup: string
}

export interface RecordParams extends ParamsSearch {
  uuidSessionAttendance?: string
  uuidSession?: string
}

export interface SessionParams {
  sessionDate: string
  uuidGroup: string
  sessionAttendance: SessionAttendance
}

export interface SessionAttendanceParams {
  uuidSession: string
  startTime: string
  endTime: string
  attendanceRecords: AttendanceRecord[]
  status: number
}

export interface Session {
  uuidSession: string
  sessionDate: string
  creator: Creator
  ordinalNoInDate: number
  studentCount: number
  presentStudentCount: number
  sessionAttendanceCount: number
  confirmedSessionAttendanceCount: number
}

export interface SessionAttendance {
  startTime: string
  endTime: string
  attendanceRecords?: AttendanceRecord[]
  status: number
}

export interface AttendanceRecord {
  uuidAttendanceRecord?: string
  uuidPerson: string
  checkInStatus: number
  uuidCheckInRecord?: string
  attendanceStatus: number
  person?: Person
  checkInRecord?: CheckInRecord
  approvalReason: string
}

export interface AttendanceRecordParams {
  uuidSessionAttendance?: string
  uuidAttendanceRecord?: string
  uuidPerson?: string
  checkInStatus: number
  uuidCheckInRecord?: string
  attendanceStatus: number
  approvalReason?: string
}
export interface CheckInRecord {
  uuidCheckInRecord: string
  uuidPerson: string
  checkInTime: string
  deviceTime: string
  picUri: string
}

export interface Attendance {
  uuidSessionAttendance: string
  uuidSession: string
  startTime: string
  endTime: string
  status: number
}

export interface StudentCheckIn extends Person, AttendanceRecord {}
