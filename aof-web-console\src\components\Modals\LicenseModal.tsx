import { Button, Form, Input, message, Modal, Spin } from 'antd'
import { ModalProps } from '@/types/common'
import { showCustomNotification } from '@/common/Notification'
import Svg from '../Svg'
import { CheckCircleFilled } from '@ant-design/icons'
import { useCallback, useEffect, useState } from 'react'
import { MESSAGE_STATUS } from '@/constants'
import { Lisence } from '@/types/user'
import { getListCameras } from '@/services/room_device'
import { initialProperty } from '@/types/components'
import { addMultipleLicense, getListLicense } from '@/services/auth'
import useAuthStore from '@/stores/auth'

const LicenseModal = ({ isModalOpen, handleCancel, isLoading }: ModalProps) => {
  const { licenseStatus, fetchLicenseStatus } = useAuthStore()
  const [form] = Form.useForm()
  const [active, setActive] = useState(licenseStatus)
  const [infoLicense, setInfoLicense] = useState<Lisence[]>()
  const [numbersCamemra, setNumbersCamera] = useState(0)
  const [params] = useState({ ...initialProperty, keySearch: '' })
  const [loadingLicense, setLoadingLicense] = useState(false)

  const getNumbersCamera = async () => {
    try {
      const res = await getListCameras(params)
      if (res.message === MESSAGE_STATUS.SUCCESS) {
        setNumbersCamera(res.object.data.length)
      }
    } catch {
      showCustomNotification({
        status: 'error',
        message: 'Thất bại',
        description: 'Lỗi khi lấy danh sách!'
      })
    }
  }

  const getLicenses = async () => {
    setLoadingLicense(true)
    try {
      const res = await getListLicense()
      if (res.message === MESSAGE_STATUS.SUCCESS) {
        setInfoLicense(res.object.data)
        form.setFieldValue('licenseSystem', res.object.data[0]?.licenseValue)
        form.setFieldValue('licenseCam', res.object.data[1]?.licenseValue)
      }
    } catch {
      showCustomNotification({
        status: 'error',
        message: 'Thất bại!',
        description: 'Lấy danh sách license thất bại!'
      })
    } finally {
      setLoadingLicense(false)
    }
  }

  useEffect(() => {
    if (!isModalOpen) return
    setActive(licenseStatus)
    getLicenses()
    getNumbersCamera()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isModalOpen])

  const handleOk = async () => {
    if (active === 1) {
      setActive(2)
      return
    }
    await form.validateFields()
    try {
      const data = form.getFieldsValue()
      const body = [{ licenseValue: data.licenseSystem }, { licenseValue: data.licenseCam }]
      const res = await addMultipleLicense(body)
      if (res.message === MESSAGE_STATUS.SUCCESS) {
        setActive(1)
        showCustomNotification({
          status: 'success',
          message: 'Thành công!',
          description: 'Kích hoạt license thành công!'
        })
        await fetchLicenseStatus()
      }
    } catch {
      showCustomNotification({
        status: 'error',
        message: 'Thất bại!',
        description: 'Kich hoạt license thất bại!'
      })
    }
  }

  const handleCopy = useCallback(
    async (fieldName: 'licenseSystem' | 'licenseCam') => {
      try {
        const value = form.getFieldValue(fieldName)
        if (!value) {
          message.warning('Không có dữ liệu để copy')
          return
        }

        await navigator.clipboard.writeText(value)
        message.success('Đã copy license vào clipboard')
      } catch (err) {
        console.error(err)
        message.error('Copy thất bại')
      }
    },
    [form]
  )

  const footerModal = (
    <div className='flex items-center justify-center gap-4 p-2 pt-4'>
      <Button
        className='min-w-[126px] bg-neutral-9 hover:opacity-70'
        size='large'
        variant='outlined'
        hidden={active !== 2}
        color='primary'
        onClick={handleCancel}
      >
        Hủy
      </Button>
      <Button loading={isLoading} onClick={handleOk} type='primary' size='large' className='min-w-[126px] shadow-none'>
        {active === 1 ? 'Kích hoạt lại' : 'Kích hoạt'}
      </Button>
    </div>
  )
  return (
    <>
      <Modal
        open={isModalOpen}
        footer={footerModal}
        onCancel={handleCancel}
        afterClose={() => (form.resetFields(), setActive(0))}
        width={676}
        title={<p className='text-lg font-semibold text-center'>Quản lý engine</p>}
      >
        <Spin spinning={loadingLicense && licenseStatus === 1}>
          <Form
            form={form}
            requiredMark={false}
            layout='vertical'
            initialValues={{ licenseSystem: '', licenseCam: '' }}
          >
            <div className='border rounded-lg mb-5'>
              <div className='p-2 md:p-4 border-b text-base'>
                <span>1. License engine Hệ thống Phân tích hình ảnh thông minh</span>
              </div>
              <div className='p-2 md:p-4'>
                <Form.Item
                  name='licenseSystem'
                  className='mb-2'
                  rules={[
                    { required: true, message: 'Vui lòng nhập license!' },
                    {
                      pattern: /^[^|]+\|[^|]+$/,
                      message: 'License không hợp lệ!'
                    }
                  ]}
                >
                  <Input
                    placeholder='Nhập thông tin license'
                    size='large'
                    disabled={active === 1}
                    suffix={
                      <Svg
                        src='/assets/icons/common/copy.svg'
                        className='h-5 w-5 cursor-pointer'
                        onClick={() => handleCopy('licenseSystem')}
                      />
                    }
                  />
                </Form.Item>
                {active === 1 && infoLicense && infoLicense[0] ? (
                  <LicenseActive time={infoLicense[0].createdAt ?? ''} version='1.0.1' />
                ) : (
                  <LicenseNotActive />
                )}
              </div>
            </div>
            <div className='border rounded-lg'>
              <div className='p-2 md:p-4 border-b text-base'>
                <span>2. License engine kết nối camera phòng học và hệ thống xử lý trung tâm</span>
                <div className='flex items-center gap-1 ml-4'>
                  <Svg src='/assets/icons/common/camera.svg' className='h-4 w-4' />
                  <span>Số lượng sử dụng:</span>
                  <span className='font-semibold'>{numbersCamemra} camera</span>
                </div>
              </div>
              <div className='p-2 md:p-4'>
                <Form.Item
                  name='licenseCam'
                  className='mb-2'
                  rules={[
                    { required: true, message: 'Vui lòng nhập license!' },
                    {
                      pattern: /^[^|]+\|[^|]+\|[^|]+$/,
                      message: 'License không hợp lệ!'
                    }
                  ]}
                >
                  <Input
                    placeholder='Nhập thông tin license'
                    size='large'
                    disabled={active === 1}
                    suffix={
                      <Svg
                        src='/assets/icons/common/copy.svg'
                        className='h-5 w-5 cursor-pointer'
                        onClick={() => handleCopy('licenseCam')}
                      />
                    }
                  />
                </Form.Item>
                {active === 1 && infoLicense && infoLicense[1] ? (
                  <LicenseActive time={infoLicense[1].createdAt ?? ''} version='1.0.1' />
                ) : (
                  <LicenseNotActive />
                )}
              </div>
            </div>
          </Form>
        </Spin>
      </Modal>
    </>
  )
}

export default LicenseModal

const LicenseNotActive = () => {
  return (
    <div className='flex items-center gap-2 text-sm'>
      <Svg src='/assets/icons/common/info.svg' className='h-4 w-4' />
      <span>License chưa được kích hoạt</span>
    </div>
  )
}

const LicenseActive = ({ time, version }: { time: string; version: string }) => {
  return (
    <div className='text-sm'>
      <div className='text-sucess flex items-center gap-2'>
        <CheckCircleFilled className='h-4 w-4' />
        <span>License đã được kích hoạt</span>
      </div>
      <div className='ml-6'>
        <p>
          Ngày kích hoạt: <span>{time}</span>
        </p>
        <p>
          Phiên bản: <span>{version}</span>
        </p>
      </div>
    </div>
  )
}
