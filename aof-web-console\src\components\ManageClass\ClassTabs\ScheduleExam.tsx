import { MoreOutlined, PlusOutlined } from '@ant-design/icons'
import { But<PERSON>, DatePicker, Divider, Dropdown, MenuProps, message, Select, Table, TableProps, TimePicker } from 'antd'
import { useEffect, useState } from 'react'
import { CalendarEvent, CalendarEventParams } from '@/types/calendar'
import { getListRooms } from '@/services/room_device'
import { ERROR_FROM_USER, MESSAGE_STATUS } from '@/constants'
import { showCustomNotification } from '@/common/Notification'
import dayjs, { Dayjs } from 'dayjs'
import { addCalendarEvent, deleteCalendarEvent, updateCalendar } from '@/services/calendar'
import { normalizeExcelDataExam } from '@/helpers/index'
import ModalImport from '@/components/Modals/ImportModal'
import Svg from '@/components/Svg'
import { Location } from '@/types/room_device'
import { useDeleteItem } from '@/hooks/useDeleteItem'
import ModalDelete from '@/components/Modals/DeleteModal'
import useLayoutStore from '@/stores/layout'
import { Lecturer } from '@/types/user'
import { checkFeature, FF_DEV_0041 } from '@/utils/feature-flags'
import useAuthStore from '@/stores/auth'

const ScheduleExam = ({
  groupName,
  lecturers,
  uuidCalendar,
  calendarData,
  refreshData
}: {
  groupName: string
  lecturers: Lecturer[]
  uuidCalendar?: string
  calendarData: CalendarEvent[]
  refreshData: () => Promise<void>
}) => {
  const { isMobile, isTablet } = useLayoutStore()
  const { user, featFlagsAction } = useAuthStore()
  const isFFA = (code: string) => checkFeature(code, user, featFlagsAction)

  const [selectedRoom, setSelectedRoom] = useState('')
  const [dataImport, setDataImport] = useState<any[]>([])
  const [uuidCalendarEvent, setUuidCalendarEvent] = useState<string>('')
  const [selectedDate, setSelectedDate] = useState<Dayjs | null>(null)
  const [location, setLocation] = useState<{ label: string; value: string }[]>([])
  const [selectedTimeRange, setSelectedTimeRange] = useState<[Dayjs | null, Dayjs | null] | null>(null)

  const { loading, openDeleteModal, setOpenDeleteModal, handleDelete } = useDeleteItem(
    deleteCalendarEvent,
    `Xóa lịch thi thành công!`,
    `Xóa lịch thi thất bại!`
  )

  const [showCreate, setShowCreate] = useState<boolean>(false)
  const [isEditing, setIsEditing] = useState<boolean>(false)
  const [openImportModal, setOpenImportModal] = useState<boolean>(false)

  const getListLocationFields = async () => {
    try {
      const res = await getListRooms({
        page: 1,
        maxSize: 999
      })
      if (res.message === MESSAGE_STATUS.SUCCESS) {
        const listLocation = res.object.data.map((l) => ({
          label: l.name,
          value: l.uuidLocation
        }))

        setLocation(listLocation)
      }
    } catch {
      console.log('Lỗi khi lấy thông tin phòng thi')
    }
  }

  useEffect(() => {
    if (isFFA(FF_DEV_0041)) {
      getListLocationFields()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const handleEdit = (record: CalendarEvent) => {
    setSelectedDate(dayjs(record.startTime, 'DD/MM/YYYY'))
    setSelectedTimeRange([dayjs(record.startTime, 'DD/MM/YYYY HH:mm:ss'), dayjs(record.endTime, 'DD/MM/YYYY HH:mm:ss')])
    if (record.location) {
      setSelectedRoom(record.location.uuidLocation)
    }
    setIsEditing(true)
    setShowCreate(true)
  }

  const handleOk = async (data?: any[]) => {
    let success
    try {
      if (data && Array.isArray(data)) {
        data = normalizeExcelDataExam(uuidCalendar || '', data, location)
        const promises = data.map((event) => addCalendarEvent(event))
        await Promise.all(promises)
        success = 1
        setOpenImportModal(false)
      } else {
        if (!selectedDate || !selectedTimeRange || !selectedTimeRange[0] || !selectedTimeRange[1]) {
          message.error('Vui lòng điền đầy đủ thông tin cần thiết!')
          return
        }
        const startTime = selectedDate.hour(selectedTimeRange[0].hour()).minute(selectedTimeRange[0].minute())
        const endTime = selectedDate.hour(selectedTimeRange[1].hour()).minute(selectedTimeRange[1].minute())

        const body: CalendarEventParams = {
          uuidCalendar: uuidCalendar || '',
          startTime: startTime.format('DD/MM/YYYY HH:mm:ss'),
          endTime: endTime.format('DD/MM/YYYY HH:mm:ss'),
          uuidLocation: selectedRoom
        }
        let res
        if (isEditing) {
          res = await updateCalendar(uuidCalendarEvent, body)
          setIsEditing(false)
        } else {
          res = await addCalendarEvent(body)
        }
        if (res.message === MESSAGE_STATUS.SUCCESS) {
          success = 1
          setShowCreate(false)
        }
      }
      if (success) {
        setDataImport([])
        setUuidCalendarEvent('')
        showCustomNotification({
          status: 'success',
          message: 'Thành công',
          description: `${isEditing ? 'Sửa' : data ? 'Import' : 'Thêm'} lịch thi thành công!`
        })
      }
      await refreshData()
    } catch (e: any) {
      const errorMessages: { [key: string]: { message: string; description: string } } = {
        [ERROR_FROM_USER.INVALID_ROOM]: {
          message: 'Lỗi phòng học!',
          description: 'Không tìm thấy phòng học phù hợp với dữ liệu nhập vào!'
        },
        [ERROR_FROM_USER.INVALID_TIME]: {
          message: 'Lỗi thời gian!',
          description: 'Thời gian không hợp lệ!'
        }
      }

      const errorInfo = errorMessages[e.message] || {
        message: 'Thất bại!',
        description: 'Có lỗi xảy ra trong quá trình nhập dữ liệu!'
      }

      showCustomNotification({
        status: 'error',
        message: errorInfo.message,
        description: errorInfo.description
      })
    }
  }
  const resetFields = () => {
    setSelectedDate(null)
    setSelectedTimeRange(null)
    setSelectedRoom('')
    setShowCreate(true)
  }
  const handleCancel = () => {
    setUuidCalendarEvent('')
    setShowCreate(false)
  }

  const items = (record: CalendarEvent): MenuProps['items'] => [
    {
      key: 1,
      label: (
        <div
          className='flex items-center gap-2'
          onClick={() => {
            setUuidCalendarEvent(record.uuidCalendarEvent || '')
            handleEdit(record)
          }}
        >
          <Svg src='/assets/icons/common/edit.svg' className='h-5 w-5' />
          Sửa
        </div>
      )
    },
    {
      key: 2,
      label: (
        <div
          className='flex items-center gap-2'
          onClick={() => {
            setUuidCalendarEvent(record.uuidCalendarEvent || '')
            setOpenDeleteModal(true)
          }}
        >
          <Svg src='/assets/icons/common/trash.svg' className='h-5 w-5 text-neutral-4' />
          Xóa
        </div>
      )
    }
  ]
  const columns: TableProps<CalendarEvent>['columns'] = [
    {
      title: 'Thời gian',
      dataIndex: 'startTime',
      key: 'startTime',
      render: (value: string) => dayjs(value, 'DD/MM/YYYY').format('DD/MM/YYYY')
    },
    {
      title: 'Giờ Thi',
      dataIndex: 'duration',
      key: 'duration'
    },
    {
      title: 'Phòng',
      dataIndex: 'location',
      key: 'location',
      render: (value: Location) => value?.name
    },
    {
      title: 'Cán bộ trông thi',
      dataIndex: 'lecturer',
      key: 'lecturer',
      render: () => {
        return lecturers.map((lecturer) => (
          <p className='line-clamp-2'>{`${lecturer.fullName} - ${lecturer.personCode.toLocaleUpperCase()}`}</p>
        ))
      }
    },
    ...(isFFA(FF_DEV_0041)
      ? [
          {
            title: 'Thao tác',
            width: isMobile ? 60 : 80,
            key: 'action',
            render: (_: any, record: CalendarEvent) => (
              <Dropdown
                menu={{ items: items(record) }}
                trigger={['click']}
                placement='bottomRight'
                disabled={uuidCalendarEvent === record.uuidCalendarEvent}
              >
                <a onClick={(e) => e.preventDefault()}>
                  <MoreOutlined />
                </a>
              </Dropdown>
            )
          }
        ]
      : [])
  ]

  return (
    <>
      <ModalDelete
        isLoading={loading}
        isModalOpen={openDeleteModal}
        handleCancel={() => {
          setOpenDeleteModal(false)
        }}
        title='Xóa'
        handleOk={() => handleDelete(uuidCalendarEvent, refreshData)}
        subTitle='Bạn có chắc chắn muốn xóa lịch thi này?'
      />
      <ModalImport
        isModalOpen={openImportModal}
        handleOk={handleOk}
        handleCancel={() => {
          setOpenImportModal(false)
        }}
        dataImport={dataImport}
        setDataImport={setDataImport}
        title={'Import lịch thi'}
      />
      <div className='flex justify-between items-center mb-5'>
        <h1 className='font-semibold text-lg'>Lịch thi</h1>
        {isFFA(FF_DEV_0041) && (
          <div className='flex items-center gap-2'>
            <Button
              size='large'
              icon={<Svg src='/assets/icons/common/import-status.svg' className='h-5 w-5 mt-1' />}
              color='primary'
              className='max-sm:w-10'
              variant='outlined'
              onClick={() => setOpenImportModal(true)}
            >
              <span className='hidden xs:block'>Import lịch thi</span>
            </Button>
            <Button
              size='large'
              icon={<PlusOutlined />}
              color='primary'
              className='max-sm:w-10'
              variant='outlined'
              onClick={resetFields}
            >
              <span className='hidden xs:block'>Thêm mới</span>
            </Button>
          </div>
        )}
      </div>
      {showCreate && (
        <div className='rounded-lg bg-background-card mb-5'>
          <div className='px-6 py-3'>
            <h2 className='text-base text-neutral-1 font-medium'>
              {isEditing ? 'Chỉnh sửa lịch  ' : 'Thêm mới lịch thi'}
            </h2>
          </div>
          <Divider className='m-0' />
          <div className='p-1 flex flex-col gap-1'>
            <h2 className='px-5 py-2 text-base text-neutral-1 font-normal'>
              Lớp: <span className='text-neutral-2 ml-2'>{groupName}</span>
            </h2>
            <div className='flex md:items-center bg-white rounded-lg p-2'>
              <p className='w-40'>
                Thời gian thi <span className='text-error'>*</span>
              </p>
              <div className='flex items-center gap-2 max-md:grid w-full xl:w-2/3'>
                <DatePicker
                  size='large'
                  className='w-full'
                  value={selectedDate}
                  format={'DD/MM/YYYY'}
                  onChange={(e) => setSelectedDate(e)}
                  popupClassName={isMobile ? 'custom-range-picker' : undefined}
                />
                <span className='max-md:hidden'>-</span>
                <TimePicker.RangePicker
                  size='large'
                  className='w-full'
                  format={'HH:mm'}
                  value={selectedTimeRange}
                  onChange={(time) => setSelectedTimeRange(time)}
                  popupClassName={isMobile ? 'custom-range-picker' : undefined}
                />
              </div>
            </div>
            <div className='flex items-center bg-white rounded-lg p-2'>
              <p className='w-40'>Phòng học</p>
              <div className='w-full xl:w-2/3'>
                <Select
                  className='w-full'
                  size='large'
                  options={location}
                  value={selectedRoom}
                  onChange={(room) => setSelectedRoom(room)}
                  placeholder='Chọn phòng học'
                />
              </div>
            </div>
            <div className='flex gap-4 xs:p-4 max-xs:py-4'>
              <Button
                className='min-w-[126px] max-xs:flex-1 bg-neutral-9 hover:opacity-70'
                size='large'
                variant='outlined'
                color='primary'
                onClick={handleCancel}
              >
                Hủy
              </Button>
              <Button
                onClick={() => handleOk()}
                type='primary'
                size='large'
                className='min-w-[126px] max-xs:flex-1 shadow-none'
              >
                Xác nhận
              </Button>
            </div>
          </div>
        </div>
      )}

      <Table
        columns={columns}
        dataSource={calendarData}
        pagination={false}
        rowKey={(record) => record.uuidLocation || Math.random()}
        scroll={{ x: isMobile || isTablet ? 'max-content' : 'auto' }}
      />
    </>
  )
}

export default ScheduleExam
