import { AttendanceStatus, BehaviorStatus, CheckinStatus, ClassStatus, Device, UserStatus } from '@/types/components'
import Svg from './Svg'

export const UserStatusField = ({ status }: { status: number }) => {
  return (
    <>
      {status === UserStatus['Hoạt động'] && (
        <div className='flex items-center gap-2'>
          <div>
            <Svg src='/assets/icons/status/active.svg' className='h-5 w-5' />
          </div>
          Hoạt động
        </div>
      )}
      {status === UserStatus['Bảo lưu'] && (
        <div className='flex items-center gap-2'>
          <div>
            <Svg src='/assets/icons/status/reserved.svg' className='h-5 w-5' />
          </div>
          Bảo lưu
        </div>
      )}
      {status === UserStatus['Thôi học'] && (
        <div className='flex items-center gap-2'>
          <div>
            <Svg src='/assets/icons/status/deactive.svg' className='h-5 w-5' />
          </div>
          Thôi học
        </div>
      )}
      {status === UserStatus['Đã tốt nghiệp'] && (
        <div className='flex items-center gap-2'>
          <div>
            <Svg src='/assets/icons/status/graduated.svg' className='h-5 w-5' />
          </div>
          Đã tốt nghiệp
        </div>
      )}
      {status === UserStatus['Đã nghỉ'] && (
        <div className='flex items-center gap-2'>
          <div>
            <Svg src='/assets/icons/status/deactive.svg' className='h-5 w-5' />
          </div>
          Đã nghỉ
        </div>
      )}
    </>
  )
}

export const ClassStatusField = ({ status, exam }: { status: number; exam: boolean }) => {
  return (
    <>
      {status === ClassStatus['Chưa bắt đầu'] && (
        <div className='flex items-center gap-2'>
          <div>
            <Svg src='/assets/icons/status/no_start.svg' className='h-5 w-5' />
          </div>
          Chưa bắt đầu
        </div>
      )}
      {exam && status === ClassStatus['Đang học'] && (
        <div className='flex items-center gap-2'>
          <div>
            <Svg src='/assets/icons/status/on_study.svg' className='h-5 w-5' />
          </div>
          Đang học
        </div>
      )}
      {!exam && status === ClassStatus['Đang thi'] && (
        <div className='flex items-center gap-2'>
          <div>
            <Svg src='/assets/icons/status/on_study.svg' className='h-5 w-5' />
          </div>
          Đang thi
        </div>
      )}
      {status === ClassStatus['Kết thúc'] && (
        <div className='flex items-center gap-2'>
          <div>
            <Svg src='/assets/icons/status/graduated.svg' className='h-5 w-5' />
          </div>
          Kết thúc
        </div>
      )}
    </>
  )
}

export const DeviceStatusField = ({ status }: { status: number }) => {
  return (
    <>
      {status === Device['Không xác định'] && (
        <div className='flex items-center gap-2'>
          <div>
            <Svg src='/assets/icons/status/no_start.svg' className='h-5 w-5' />
          </div>
          Không xác định
        </div>
      )}
      {status === Device['Hoạt động'] && (
        <div className='flex items-center gap-2'>
          <div>
            <Svg src='/assets/icons/status/graduated.svg' className='h-5 w-5' />
          </div>
          Hoạt động
        </div>
      )}
      {status === Device['Không hoạt động'] && (
        <div className='flex items-center gap-2'>
          <div>
            <Svg src='/assets/icons/status/deactive.svg' className='h-5 w-5' />
          </div>
          Không hoạt động
        </div>
      )}
    </>
  )
}

export const CheckinStatusField = ({ status }: { status: number }) => {
  switch (status) {
    case CheckinStatus['Đã điểm danh máy']:
      return (
        <div className='flex items-center gap-2'>
          <div>
            <Svg src='/assets/icons/status/graduated.svg' className='h-5 w-5' />
          </div>
          Đã điểm danh máy
        </div>
      )
    case CheckinStatus['Chưa điểm danh']:
      return (
        <div className='flex items-center gap-2'>
          <div>
            <Svg src='/assets/icons/status/reserved.svg' className='h-5 w-5' />
          </div>
          Chưa điểm danh
        </div>
      )
    default:
      return (
        <div className='flex items-center gap-2'>
          <div>
            <Svg src='/assets/icons/status/reserved.svg' className='h-5 w-5' />
          </div>
          Chưa điểm danh
        </div>
      )
  }
}

export const AttendanceStatusField = ({ status }: { status: number }) => {
  switch (status) {
    case AttendanceStatus['Đi học']:
      return (
        <div className='flex items-center gap-2'>
          <div>
            <Svg src='/assets/icons/status/graduated.svg' className='h-5 w-5' />
          </div>
          Đi học
        </div>
      )
    case AttendanceStatus['Nghỉ học']:
      return (
        <div className='flex items-center gap-2'>
          <div>
            <Svg src='/assets/icons/status/reserved.svg' className='h-5 w-5' />
          </div>
          Nghỉ học
        </div>
      )

    default:
      return (
        <div className='flex items-center gap-2'>
          <div>
            <Svg src='/assets/icons/status/reserved.svg' className='h-5 w-5' />
          </div>
          Nghỉ học
        </div>
      )
  }
}

export const BehaviorField = ({ status }: { status: number }) => {
  return (
    <>
      {status === BehaviorStatus['Sử dụng điện thoại'] && (
        <div className='flex items-center gap-2'>Sử dụng điện thoại</div>
      )}
      {status === BehaviorStatus['Trao đổi bài'] && <div className='flex items-center gap-2'>Trao đổi bài</div>}
    </>
  )
}
