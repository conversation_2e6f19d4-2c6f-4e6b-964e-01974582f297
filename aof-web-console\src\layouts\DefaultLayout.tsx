import { Layout } from 'antd'
import Header from './Header'
import Sider from './Sider'
import { useCallback, useEffect, useState } from 'react'
import useLayoutStore from '@/stores/layout'
import { Outlet } from 'react-router-dom'

const { Content } = Layout

const DefaultLayout = ({ children }: any) => {
  const { setMobile, setTablet } = useLayoutStore()
  const [device, setDevice] = useState({
    isMobile: false,
    isTablet: false
  })

  const handleResize = useCallback(() => {
    const isMobile = window.matchMedia('(max-width: 767px)').matches
    const isTablet = window.matchMedia('(min-width: 768px) and (max-width: 1279px)').matches

    setDevice({ isMobile, isTablet })
    setMobile(isMobile)
    setTablet(isTablet)
  }, [setMobile, setTablet])

  useEffect(() => {
    const mobileQuery = window.matchMedia('(max-width: 767px)')
    const tabletQuery = window.matchMedia('(min-width: 768px) and (max-width: 1279px)')

    mobileQuery.addEventListener('change', handleResize)
    tabletQuery.addEventListener('change', handleResize)

    handleResize()

    return () => {
      mobileQuery.removeEventListener('change', handleResize)
      tabletQuery.removeEventListener('change', handleResize)
    }
  }, [handleResize])

  return (
    <Layout className='min-h-content overflow-hidden'>
      <Header showMenu={device.isTablet || device.isMobile} />
      <Content className='overflow-hidden'>
        <Layout className='min-h-content bg-background-1'>
          {!device.isTablet && !device.isMobile && <Sider isMobile={device.isMobile} />}
          <Content>
            <Outlet context={children} />
          </Content>
        </Layout>
      </Content>
    </Layout>
  )
}

export default DefaultLayout
