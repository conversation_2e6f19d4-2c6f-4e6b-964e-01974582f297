import { Button, Image } from 'antd'
import logoImage from '/assets/images/logo2.png'
import image_403 from '/assets/images/image_403.png'
import { resetAllStores } from '@/stores/auth'

const Error403 = () => {
  const handleLogout = () => {
    resetAllStores()
    window.location.replace('/')
  }
  return (
    <div
      className='min-h-screen flex flex-col items-center justify-center'
      style={{
        background: 'linear-gradient(180deg, #f7fafc 0%, #E5F6F8 100%)'
      }}
    >
      <Image src={logoImage} preview={false} height={100} width={100} />
      <h1 className='font-bold text-2xl sm:text-4xl text-text-primary mt-4 mb-8'>HỌC VIỆN TÀI CHÍNH</h1>
      <div className='w-[784px] h-[460px] flex flex-col items-center justify-center mt-4 overflow-hidden'>
        <Image src={image_403} preview={false} />
      </div>
      <div className='font-semibold text-lg text-[#1B3B5A] mt-4 mb-2 text-center'>
        <PERSON><PERSON> x<PERSON>y ra lỗi, xin vui lòng thử lại!
      </div>
      <div className='pt-1 flex flex-col items-center'>
        <Button variant='outlined' color='primary' size='large' onClick={handleLogout}>
          Quay lại
        </Button>
      </div>
    </div>
  )
}

export default Error403
