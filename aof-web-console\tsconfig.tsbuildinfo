{"root": ["./vite.config.ts", "./src/env.d.ts", "./src/main.tsx", "./src/vite-env.d.ts", "./src/common/Loading/index.tsx", "./src/common/Notification/index.tsx", "./src/components/AvatarImage.tsx", "./src/components/CheckboxPermission.tsx", "./src/components/InputsField.tsx", "./src/components/MenuDrawer.tsx", "./src/components/RecordInfo.tsx", "./src/components/StatusField.tsx", "./src/components/StudentInfo.tsx", "./src/components/ViolationCard.tsx", "./src/components/ViolationInfo.tsx", "./src/components/ManageClass/AddNewClass.tsx", "./src/components/ManageClass/AddPersonToClass.tsx", "./src/components/ManageClass/DetailsClass.tsx", "./src/components/ManageClass/ClassTabs/GeneralInfo.tsx", "./src/components/ManageClass/ClassTabs/ListPersons.tsx", "./src/components/ManageClass/ClassTabs/ListSessions.tsx", "./src/components/ManageClass/ClassTabs/Schedule.tsx", "./src/components/ManageClass/ClassTabs/ScheduleExam.tsx", "./src/components/ManageClass/RecordComponents/AddSessionRecord.tsx", "./src/components/ManageClass/RecordComponents/SessionAttendance.tsx", "./src/components/ManageClass/RecordComponents/SessionRecord.tsx", "./src/components/ManageInfo/ManageSchoolYears.tsx", "./src/components/ManageInfo/ManageStudents.tsx", "./src/components/ManageUser/ManageInfomationUser.tsx", "./src/components/MangePermission/DetailsPermission.tsx", "./src/components/MangePermission/DetailsPermissionPerson.tsx", "./src/components/MangePermission/Permission/Class.tsx", "./src/components/MangePermission/Permission/Feature.tsx", "./src/components/MangePermission/Permission/User.tsx", "./src/components/Modals/CameraModal.tsx", "./src/components/Modals/ChangePasswordModal.tsx", "./src/components/Modals/CheckinRecordInfoModal.tsx", "./src/components/Modals/CourseModal.tsx", "./src/components/Modals/DeleteModal.tsx", "./src/components/Modals/ErrorLoadingModal.tsx", "./src/components/Modals/ImportModal.tsx", "./src/components/Modals/LicenseModal.tsx", "./src/components/Modals/NotificationModal.tsx", "./src/components/Modals/PermissionGroupModal.tsx", "./src/components/Modals/ResultImportModal.tsx", "./src/components/Modals/SelectDateModal.tsx", "./src/components/Modals/StudentInfomationModal.tsx", "./src/components/Modals/WebcamModal.tsx", "./src/components/Profile/index.tsx", "./src/components/Svg/index.tsx", "./src/components/Svg/use.tsx", "./src/components/Tables/AcademicYearTable.tsx", "./src/components/Tables/AttendaceRecordTable.tsx", "./src/components/Tables/CameraTable.tsx", "./src/components/Tables/CourseTable.tsx", "./src/components/Tables/DeviceTable.tsx", "./src/components/Tables/ImageTable.tsx", "./src/components/Tables/LogTable.tsx", "./src/components/Tables/PersonTable.tsx", "./src/components/Tables/RoomTable.tsx", "./src/components/Tables/Permission/GroupTable.tsx", "./src/components/Tables/Permission/PersonTable.tsx", "./src/constants/index.ts", "./src/helpers/configData.ts", "./src/helpers/index.ts", "./src/hooks/useAuthInitialization.ts", "./src/hooks/useDebounce.ts", "./src/hooks/useDeleteItem.ts", "./src/layouts/DefaultLayout.tsx", "./src/layouts/Header.tsx", "./src/layouts/LayoutCotent.tsx", "./src/layouts/Sider.tsx", "./src/pages/App.tsx", "./src/pages/ProtectedRoutes.tsx", "./src/pages/auth/index.tsx", "./src/pages/check-license/index.tsx", "./src/pages/error-page/403.tsx", "./src/pages/manage-class/index.tsx", "./src/pages/manage-exam/index.tsx", "./src/pages/manage-feature/index.tsx", "./src/pages/manage-info/index.tsx", "./src/pages/manage-room&device/index.tsx", "./src/pages/manage-user/index.tsx", "./src/pages/personal-info/index.tsx", "./src/pages/system-log/index.tsx", "./src/services/academicYear.ts", "./src/services/auth.ts", "./src/services/calendar.ts", "./src/services/common.ts", "./src/services/group.ts", "./src/services/log.ts", "./src/services/person.ts", "./src/services/room_device.ts", "./src/services/session.ts", "./src/stores/auth.ts", "./src/stores/group.ts", "./src/stores/info.ts", "./src/stores/layout.ts", "./src/stores/useSegmentStore.ts", "./src/types/calendar.ts", "./src/types/common.ts", "./src/types/components.ts", "./src/types/group.ts", "./src/types/infomation.ts", "./src/types/log.ts", "./src/types/person.ts", "./src/types/room_device.ts", "./src/types/session.ts", "./src/types/user.ts", "./src/utils/axios.ts", "./src/utils/config.ts", "./src/utils/feature-flags.ts"], "version": "5.6.3"}