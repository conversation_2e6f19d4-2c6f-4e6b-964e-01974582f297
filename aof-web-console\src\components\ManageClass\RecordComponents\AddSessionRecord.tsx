import LayoutCotent from '@/layouts/LayoutCotent'
import SessionRecord from './SessionRecord'
import { useParams } from 'react-router-dom'
import { useEffect, useState } from 'react'
import { getDetailsGroup } from '@/services/group'
import { MESSAGE_STATUS } from '@/constants'

const AddSessionRecord = () => {
  const [groupName, setGroupName] = useState('')
  const [typeName, setTypeName] = useState('')
  const { uuidGroup } = useParams()
  const getDetails = async () => {
    try {
      if (!uuidGroup) return
      const res = await getDetailsGroup(uuidGroup)
      if (res.message === MESSAGE_STATUS.SUCCESS) {
        setGroupName(res.object.groupName)
        setTypeName(res.object.groupType === 2 ? 'Lớp học' : 'Lớp thi')
      }
    } catch {
      console.log('Lỗi không lấy được thông tin khóa học!')
    }
  }

  useEffect(() => {
    getDetails()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [uuidGroup])
  return (
    <LayoutCotent title={['Quản lý lớp', typeName, groupName, 'Thêm mới bản ghi điểm danh']} btnBack>
      <SessionRecord />
    </LayoutCotent>
  )
}

export default AddSessionRecord
