import { MoreOutlined, PlusOutlined } from '@ant-design/icons'
import {
  But<PERSON>,
  Checkbox,
  DatePicker,
  Divider,
  Dropdown,
  MenuProps,
  message,
  Select,
  Table,
  TableProps,
  TimePicker
} from 'antd'
import { useEffect, useState } from 'react'
import { CalendarEvent } from '@/types/calendar'
import { dayMap, dayLearn, MAX_SIZE_PAGE } from '@/types/components'
import { getListRooms } from '@/services/room_device'
import { ERROR_FROM_USER, MESSAGE_STATUS } from '@/constants'
import { showCustomNotification } from '@/common/Notification'
import dayjs, { Dayjs } from 'dayjs'
import { addCalendarEvent, deleteCalendarEvent, updateCalendar } from '@/services/calendar'
import { normalizeExcelData } from '@/helpers/index'
import ModalImport from '@/components/Modals/ImportModal'
import Svg from '@/components/Svg'
import ModalDelete from '@/components/Modals/DeleteModal'
import { useDeleteItem } from '@/hooks/useDeleteItem'
import { Location } from '@/types/room_device'
import useLayoutStore from '@/stores/layout'
import { Lecturer } from '@/types/user'
import useAuthStore from '@/stores/auth'
import { checkFeature, FF_DEV_0041 } from '@/utils/feature-flags'

const { RangePicker } = DatePicker

interface Props {
  groupName: string
  lecturers: Lecturer[]
  uuidCalendar?: string
  calendarData: CalendarEvent[]
  refreshData: () => Promise<void>
}

const Schedule = ({ groupName, lecturers, uuidCalendar, calendarData, refreshData }: Props) => {
  const [uuidCalendarEvent, setUuidCalendarEvent] = useState<string>('')
  const [dataImport, setDataImport] = useState<any[]>([])
  const [location, setLocation] = useState<Location[]>([])
  const [selectedDateRange, setSelectedDateRange] = useState<[Dayjs | null, Dayjs | null] | null>(null)
  const [selectedTimeRange, setSelectedTimeRange] = useState<[Dayjs | null, Dayjs | null] | null>(null)
  const [selectedDays, setSelectedDays] = useState<string[]>([])
  const [selectedRoom, setSelectedRoom] = useState<string>()

  const { loading, openDeleteModal, setOpenDeleteModal, handleDelete } = useDeleteItem(
    deleteCalendarEvent,
    `Xóa lịch học thành công!`,
    `Xóa lịch học thất bại!`
  )
  const { isMobile, isTablet } = useLayoutStore()
  const { user, featFlagsAction } = useAuthStore()
  const isFFA = (code: string) => checkFeature(code, user, featFlagsAction)

  const [isEditing, setIsEditing] = useState<boolean>(false)
  const [showCreate, setShowCreate] = useState<boolean>(false)
  const [openImportModal, setOpenImportModal] = useState<boolean>(false)

  useEffect(() => {
    if (isFFA(FF_DEV_0041)) {
      getListLocations()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const getListLocations = async () => {
    try {
      const res = await getListRooms({
        page: 1,
        maxSize: MAX_SIZE_PAGE
      })
      if (res.message === MESSAGE_STATUS.SUCCESS) {
        setLocation(res.object.data)
      }
    } catch {
      console.log('Lỗi khi lấy thông tin phòng học')
    }
  }
  const handleEdit = (record: CalendarEvent) => {
    if (!record.recurrenceRule) return
    setSelectedDateRange([
      dayjs(record.recurrenceRule.startDate, 'DD-MM-YYYY'),
      dayjs(record.recurrenceRule.endDate, 'DD-MM-YYYY')
    ])
    setSelectedTimeRange([dayjs(record.startTime, 'DD/MM/YYYY HH:mm:ss'), dayjs(record.endTime, 'DD/MM/YYYY HH:mm:ss')])
    const daysArray =
      typeof record.recurrenceRule.byday === 'string'
        ? record.recurrenceRule.byday
            .split(',')
            .map((day) => Object.keys(dayMap).find((key) => dayMap[key] === day) || day)
        : []
    setSelectedDays(daysArray)
    if (record.location) {
      setSelectedRoom(record.location.uuidLocation)
    }
    setIsEditing(true)
    setShowCreate(true)
  }

  const handleOk = async (data?: any[]) => {
    let success
    try {
      if (data && Array.isArray(data)) {
        data = normalizeExcelData(uuidCalendar || '', data, location)
        const promises = data.map((event) => addCalendarEvent(event))
        await Promise.all(promises)
        success = 1
        setOpenImportModal(false)
      } else {
        if (!selectedDateRange || !selectedTimeRange || !selectedDays.length) {
          message.error('Vui lòng điền đầy đủ thông tin cần thiết!')
          return
        }
        const body: CalendarEvent = {
          uuidCalendar: uuidCalendar || '',
          startTime: selectedTimeRange?.[0]?.format('DD/MM/YYYY HH:mm:ss') || '',
          endTime: selectedTimeRange?.[1]?.format('DD/MM/YYYY HH:mm:ss') || '',
          recurrenceRule: {
            byday: selectedDays.map((day) => dayMap[day] || day).join(','),
            startDate: selectedDateRange?.[0]?.format('DD/MM/YYYY') || '',
            endDate: selectedDateRange?.[1]?.format('DD/MM/YYYY') || ''
          },
          uuidLocation: selectedRoom
        }
        let res
        if (isEditing) {
          res = await updateCalendar(uuidCalendarEvent, body)
          setIsEditing(false)
        } else {
          res = await addCalendarEvent(body)
        }
        if (res.message === MESSAGE_STATUS.SUCCESS) {
          success = 1
          setShowCreate(false)
        }
      }
      if (success) {
        setDataImport([])
        setUuidCalendarEvent('')
        showCustomNotification({
          status: 'success',
          message: 'Thành công',
          description: `${isEditing ? 'Sửa' : data ? 'Import' : 'Thêm'} thời khóa biểu thành công!`
        })
      }
      await refreshData()
    } catch (e: any) {
      const errorMessages: { [key: string]: { message: string; description: string } } = {
        [ERROR_FROM_USER.INVALID_ROOM]: {
          message: 'Lỗi phòng học!',
          description: 'Không tìm thấy phòng học phù hợp với dữ liệu nhập vào!'
        },
        [ERROR_FROM_USER.INVALID_TIME]: {
          message: 'Lỗi thời gian!',
          description: 'Thời gian không hợp lệ!'
        }
      }
      const errorInfo = errorMessages[e.message] || {
        message: 'Thất bại!',
        description: 'Có lỗi xảy ra trong quá trình nhập dữ liệu!'
      }

      showCustomNotification({
        status: 'error',
        message: errorInfo.message,
        description: errorInfo.description
      })
    }
  }

  const handleCancel = () => {
    setUuidCalendarEvent('')
    setShowCreate(false)
  }
  const resetFields = () => {
    setSelectedDateRange(null)
    setSelectedTimeRange(null)
    setSelectedDays([])
    setSelectedRoom(undefined)
    setShowCreate(true)
    setIsEditing(false)
  }

  const items = (record: CalendarEvent): MenuProps['items'] => [
    {
      key: 1,
      label: (
        <div
          className='flex items-center gap-2'
          onClick={() => {
            setUuidCalendarEvent(record.uuidCalendarEvent || '')
            handleEdit(record)
          }}
        >
          <Svg src='/assets/icons/common/edit.svg' className='h-5 w-5 text-neutral-4' />
          Sửa
        </div>
      )
    },
    {
      key: 2,
      label: (
        <div
          className='flex items-center gap-2'
          onClick={() => {
            setUuidCalendarEvent(record.uuidCalendarEvent || '')
            setOpenDeleteModal(true)
          }}
        >
          <Svg src='/assets/icons/common/trash.svg' className='h-5 w-5 text-neutral-4' />
          Xóa
        </div>
      )
    }
  ]

  const columns: TableProps<CalendarEvent>['columns'] = [
    {
      title: 'Thời gian',
      dataIndex: 'timeSlot',
      key: 'timeSlot'
    },
    {
      title: 'Thứ',
      dataIndex: 'dayOfWeek',
      key: 'dayOfWeek',
      width: isMobile || isTablet ? 200 : undefined,
      render: (value: string) => <p className='line-clamp-2'>{value}</p>
    },
    {
      title: 'Giờ học',
      dataIndex: 'duration',
      key: 'duration'
    },
    {
      title: 'Phòng',
      dataIndex: 'location',
      key: 'location',
      render: (value: Location) => value?.name
    },
    {
      title: 'Giảng viên',
      dataIndex: 'lecturer',
      key: 'lecturer',
      render: () => {
        return lecturers.map((lecturer) => (
          <p key={lecturer.uuidPerson} className='line-clamp-2'>
            {`${lecturer.fullName} - ${lecturer.personCode.toLocaleUpperCase()}`}
          </p>
        ))
      }
    },
    ...(isFFA(FF_DEV_0041)
      ? [
          {
            title: 'Thao tác',
            width: isMobile ? 60 : 80,
            key: 'action',
            render: (_: any, record: CalendarEvent) => (
              <Dropdown
                menu={{ items: items(record) }}
                trigger={['click']}
                placement='bottomRight'
                disabled={uuidCalendarEvent === record.uuidCalendarEvent}
              >
                <a onClick={(e) => e.preventDefault()} className='flex justify-center'>
                  <MoreOutlined />
                </a>
              </Dropdown>
            )
          }
        ]
      : [])
  ]

  return (
    <>
      <ModalDelete
        isLoading={loading}
        isModalOpen={openDeleteModal}
        handleCancel={() => {
          setOpenDeleteModal(false)
        }}
        title='Xóa'
        handleOk={() => handleDelete(uuidCalendarEvent, refreshData)}
        subTitle='Bạn có chắc chắn muốn xóa lịch học này?'
      />
      <ModalImport
        isModalOpen={openImportModal}
        handleOk={handleOk}
        handleCancel={() => {
          setOpenImportModal(false)
        }}
        dataImport={dataImport}
        setDataImport={setDataImport}
        title={'Import thời khóa biểu'}
      />
      <div className='flex justify-between items-center mb-5'>
        <h1 className='font-semibold text-lg'>Thời khóa biểu</h1>
        {isFFA(FF_DEV_0041) && (
          <div className='flex items-center gap-2'>
            <Button
              size='large'
              icon={<Svg src='/assets/icons/common/import-status.svg' className='h-5 w-5 mt-1' />}
              color='primary'
              variant='outlined'
              className='max-sm:w-10'
              onClick={() => setOpenImportModal(true)}
            >
              <span className='hidden sm:block'>Import thời khóa biểu</span>
            </Button>
            <Button
              size='large'
              icon={<PlusOutlined />}
              color='primary'
              className='max-sm:w-10'
              variant='outlined'
              onClick={resetFields}
            >
              <span className='hidden sm:block'>Thêm mới</span>
            </Button>
          </div>
        )}
      </div>
      {showCreate && (
        <div className='rounded-lg bg-background-card mb-5'>
          <div className='px-6 py-3'>
            <h2 className='text-base text-neutral-1 font-medium'>
              {isEditing ? 'Chỉnh sửa lịch học' : 'Thêm mới lịch học'}
            </h2>
          </div>
          <Divider className='m-0' />
          <div className='p-1 flex flex-col gap-1'>
            <h2 className='px-5 py-2 text-base text-neutral-1 font-normal'>Lớp: {groupName}</h2>
            <div className='flex max-sm:flex-col sm:items-center bg-white rounded-lg p-2'>
              <p className='w-40 max-sm:mb-2'>
                Thời gian áp dụng <span className='text-error'>*</span>
              </p>
              <div className='w-full xl:w-2/3'>
                <RangePicker
                  size='large'
                  className='w-full'
                  format={'DD/MM/YYYY'}
                  value={selectedDateRange}
                  suffixIcon={<Svg src='/assets/icons/common/datepicker.svg' className='h-4 w-4' />}
                  onChange={(dates) => setSelectedDateRange(dates)}
                  popupClassName={isMobile ? 'custom-range-picker' : undefined}
                />
              </div>
            </div>
            <div className='flex max-sm:flex-col sm:items-center bg-white rounded-lg p-2'>
              <p className='w-40 max-sm:mb-2'>
                Giờ học <span className='text-error'>*</span>
              </p>
              <div className='w-full xl:w-2/3'>
                <TimePicker.RangePicker
                  size='large'
                  className='w-full'
                  format={'HH:mm'}
                  value={selectedTimeRange}
                  onChange={(time) => setSelectedTimeRange(time)}
                />
              </div>
            </div>
            <div className='flex items-center bg-white rounded-lg p-2'>
              <p className='w-40'>
                Ngày học <span className='text-error'>*</span>
              </p>
              <div className='w-full xl:w-2/3'>
                <Checkbox.Group
                  className='max-xs:grid max-xs:grid-cols-2 max-xs:gap-x-10 max-xs:gap-y-3'
                  options={dayLearn}
                  onChange={(days) => setSelectedDays(days)}
                  value={selectedDays}
                />
              </div>
            </div>
            <div className='flex max-sm:flex-col sm:items-center bg-white rounded-lg p-2'>
              <p className='w-40 max-sm:mb-2'>Phòng học</p>
              <div className='w-full xl:w-2/3'>
                <Select
                  className='w-full'
                  size='large'
                  options={[
                    ...location.map((item) => ({
                      label: item.name,
                      value: item.uuidLocation
                    }))
                  ]}
                  value={selectedRoom}
                  onChange={(room) => setSelectedRoom(room)}
                  placeholder='Chọn phòng học'
                />
              </div>
            </div>
            <div className='flex gap-4 p-2 xs:p-4'>
              <Button
                className='min-w-[126px] max-xs:w-1/2 bg-neutral-9 hover:opacity-70'
                size='large'
                variant='outlined'
                color='primary'
                onClick={handleCancel}
              >
                Hủy
              </Button>
              <Button
                onClick={() => handleOk()}
                type='primary'
                size='large'
                className='min-w-[126px] max-xs:w-1/2 shadow-none'
              >
                Xác nhận
              </Button>
            </div>
          </div>
        </div>
      )}

      <Table
        columns={columns}
        dataSource={calendarData}
        pagination={false}
        rowKey={(record) => record.uuidCalendarEvent || ''}
        scroll={{ x: isMobile || isTablet ? 'max-content' : 'auto' }}
      />
    </>
  )
}

export default Schedule
