import { create } from 'zustand'
import { produce } from 'immer'

interface State {
  isMobile: boolean
  isTablet: boolean
  setMobile: (value: boolean) => void
  setTablet: (value: boolean) => void
}

const useLayoutStore = create<State>((set) => ({
  isMobile: false,
  isTablet: false,

  setMobile: (value) =>
    set((state) =>
      produce(state, (draft) => {
        draft.isMobile = value
      })
    ),

  setTablet: (value) =>
    set((state) =>
      produce(state, (draft) => {
        draft.isTablet = value
      })
    )
}))

export default useLayoutStore
