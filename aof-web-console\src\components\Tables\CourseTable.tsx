import { Group } from '@/types/group'
import { MoreOutlined } from '@ant-design/icons'
import { Dropdown, MenuProps, Table, TableProps } from 'antd'
import { useState } from 'react'
import Svg from '../Svg'
import { deleteGroup } from '@/services/group'
import { MESSAGE_STATUS } from '@/constants'
import { showCustomNotification } from '@/common/Notification'
import { useNavigate } from 'react-router-dom'
import ModalDelete from '../Modals/DeleteModal'
import useLayoutStore from '@/stores/layout'
import useAuthStore from '@/stores/auth'
import { checkFeature, FF_DEV_0031, FF_DEV_0032 } from '@/utils/feature-flags'

export interface InfoTableProps {
  data: Group[]
  page: number
  maxSize: number
  isLoading: boolean
  openCourseModal: boolean
  setIsLoading: (value: boolean) => void
  setOpenCourseModal: (value: boolean) => void
  refreshData: () => Promise<void>
  onEditCourse: (groupName: string, uuid: string) => void
}

const CourseTable = ({ data, page, maxSize, isLoading, setIsLoading, refreshData, onEditCourse }: InfoTableProps) => {
  const navigate = useNavigate()

  const { isMobile } = useLayoutStore()
  const { user, featFlagsAction } = useAuthStore()
  const isFFA = (code: string) => checkFeature(code, user, featFlagsAction)

  const [uuid, setUuid] = useState<string>('')
  const [openDeleteModal, setOpenDeleteModal] = useState<boolean>(false)
  const [noStudent, setNoStudent] = useState<boolean>(false)

  const handleDeleteGroup = async () => {
    if (noStudent) {
      showCustomNotification({
        status: 'warning',
        message: 'Thông báo!',
        description: 'Vẫn còn sinh viên trong khóa, không thể xóa khóa học.'
      })
      return
    }
    setIsLoading(true)
    try {
      const res = await deleteGroup(uuid)
      if (res.message === MESSAGE_STATUS.SUCCESS) {
        refreshData()
        showCustomNotification({ status: 'success', message: 'Thành công', description: 'Xóa khóa thành công!' })
      }
    } catch {
      showCustomNotification({ status: 'error', message: 'Thất bại', description: 'Xóa khóa thất bại!' })
    } finally {
      setIsLoading(false)
      setOpenDeleteModal(false)
    }
  }

  const items = (record: Group): MenuProps['items'] => [
    ...(isFFA(FF_DEV_0031)
      ? [
          {
            key: 'edit',
            label: (
              <div
                className='flex items-center gap-2'
                onClick={() => {
                  onEditCourse(record.groupName, record.uuidGroup)
                }}
              >
                <Svg src='/assets/icons/common/edit.svg' className='h-5 w-5 text-neutral-4' /> Sửa
              </div>
            )
          }
        ]
      : []),
    ...(isFFA(FF_DEV_0032)
      ? [
          {
            key: 'delete',
            label: (
              <div
                className='flex items-center gap-2'
                onClick={() => {
                  setOpenDeleteModal(true)
                  setUuid(record.uuidGroup)
                  setNoStudent(record.studentCount > 0)
                }}
              >
                <Svg src='/assets/icons/common/trash.svg' className='h-5 w-5 text-neutral-4' /> Xóa
              </div>
            )
          }
        ]
      : []),
    {
      key: 'studentList',
      label: (
        <div className='flex items-center gap-2' onClick={() => navigate(`/manage-info/students/${record.uuidGroup}`)}>
          <Svg src='/assets/icons/common/list.svg' className='h-5 w-5 text-neutral-4' /> Danh sách sinh viên
        </div>
      )
    }
  ]

  const columns: TableProps<Group>['columns'] = [
    {
      title: 'STT',
      key: 'key',
      width: 48,
      align: 'center',
      render: (_, __, index) => (page - 1) * maxSize + (index + 1)
    },
    { title: 'Tên khóa', dataIndex: 'groupName', key: 'groupName' },
    { title: 'Số lượng sinh viên', dataIndex: 'studentCount', key: 'studentCount' },
    {
      title: 'Thao tác',
      width: isMobile ? 60 : 80,
      key: 'action',
      align: 'center',
      render: (_, record) => (
        <Dropdown menu={{ items: items(record) }} trigger={['click']}>
          <a onClick={(e) => e.preventDefault()}>
            <MoreOutlined />
          </a>
        </Dropdown>
      )
    }
  ]

  return (
    <>
      <ModalDelete
        title='Xóa'
        isLoading={isLoading}
        isModalOpen={openDeleteModal}
        handleOk={handleDeleteGroup}
        subTitle='Bạn có chắc chắn muốn xóa khóa học?'
        handleCancel={() => setOpenDeleteModal(false)}
      />

      <Table
        loading={isLoading}
        columns={columns}
        dataSource={data}
        pagination={false}
        scroll={{ y: 550 }}
        rowKey={(record) => record.uuidGroup}
      />
    </>
  )
}

export default CourseTable
