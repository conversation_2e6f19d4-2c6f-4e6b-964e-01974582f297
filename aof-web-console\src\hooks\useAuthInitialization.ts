import { MESSAGE_STATUS } from '@/constants'
import { getToken, getTokenSSO } from '@/services/auth'
import useAuthStore from '@/stores/auth'
import { useEffect, useRef } from 'react'
import { useNavigate } from 'react-router-dom'

function useAuthInitialization() {
  const isRun = useRef(false)
  const auth = useAuthStore()
  const navigate = useNavigate()

  useEffect(() => {
    if (isRun.current) return
    isRun.current = true

    const initAuth = async () => {
      const urlParams = new URLSearchParams(window.location.search)
      const code = auth.code ? auth.code : urlParams.get('code')
      if (auth.accessToken) {
        return
      } else {
        if (code) {
          const token = await handleExchangeCodeForToken(code)
          if (token) {
            await handleGetTokenAccess(token)
          }
        }
      }
    }

    initAuth()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const handleExchangeCodeForToken = async (code: string) => {
    try {
      auth.setCode(code)
      const res = await getTokenSSO({
        grant_type: 'authorization_code',
        code,
        redirect_uri: window.location.origin + '/sso/identifier'
      })

      if (res.message === MESSAGE_STATUS.SUCCESS) {
        const { access_token, id_token } = res.object
        auth.setTokenSSO(access_token, id_token)
        return access_token
      }
    } catch (error) {
      console.log(error)
    }
  }

  const handleGetTokenAccess = async (token: string) => {
    try {
      const res = await getToken(token)
      if (res.message === MESSAGE_STATUS.SUCCESS) {
        auth.login(res.object.accessToken, res.object.refreshToken)
        navigate('/')
      }
    } catch (error) {
      console.log(error)
      navigate('/403')
    }
  }
}

export default useAuthInitialization
