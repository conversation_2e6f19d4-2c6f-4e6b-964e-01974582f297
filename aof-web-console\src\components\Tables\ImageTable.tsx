import { CloseOutlined, MoreOutlined } from '@ant-design/icons'
import { Dropdown, Image, MenuProps, Table, TableProps } from 'antd'
import { useState } from 'react'
import Svg from '@/components/Svg'
import { Face, ImageCollection, Person } from '@/types/person'
import { ImageStatus } from '@/types/components'
import useLayoutStore from '@/stores/layout'
import { approveMultipleImages } from '@/services/person'
import { MESSAGE_STATUS } from '@/constants'
import { showCustomNotification } from '@/common/Notification'
// import Avatar from '../AvatarImage'

export interface InfoTableProps {
  data: ImageCollection[]
  page: number
  maxSize: number
  selectedRow: ImageCollection[]
  refreshData: () => void
  setParams: (e: any) => void
  setOnAction: (value: boolean) => void
  setSelectedRows: (value: ImageCollection[]) => void
}

const ImageTable = ({ data, page, maxSize, selectedRow, setParams, setOnAction, setSelectedRows }: InfoTableProps) => {
  const { isMobile } = useLayoutStore()
  const [uuid, setUuid] = useState<string>('')

  const approveMultipleImage = async (status: number) => {
    try {
      setOnAction(true)
      const res = await approveMultipleImages({ uuidImages: [uuid], approvalStatus: status })
      if (res.message === MESSAGE_STATUS.SUCCESS) {
        showCustomNotification({
          status: 'success',
          message: `${status === ImageStatus['Đã duyệt'] ? 'Duyệt' : 'Từ chối'} thành công!`,
          description: `${status === ImageStatus['Đã duyệt'] ? 'Duyệt' : 'Từ chối duyệt'} ảnh thành công!`
        })
        setSelectedRows([])
        setParams((pre: any) => ({ ...pre, page: 1 }))
      }
    } catch {
      showCustomNotification({
        status: 'error',
        message: 'Thất bại!',
        description: 'Có lỗi xảy ra trong quá trình duyệt ảnh!'
      })
    } finally {
      setOnAction(false)
    }
  }

  const rowSelection = isMobile
    ? undefined
    : {
        selectedRow,
        onChange: (_: React.Key[], image: ImageCollection[]) => {
          setSelectedRows(image)
        }
      }

  const items: MenuProps['items'] = [
    {
      key: 1,
      label: (
        <div className='flex items-center gap-2' onClick={() => approveMultipleImage(ImageStatus['Đã duyệt'])}>
          <Svg src='/assets/icons/common/tick-circle.svg' className='h-5 w-5 text-neutral-4' />
          Phê duyệt
        </div>
      )
    },
    {
      key: 2,
      label: (
        <div className='flex items-center gap-2' onClick={() => approveMultipleImage(ImageStatus['Từ chối'])}>
          <CloseOutlined className='text-neutral-4' />
          Từ chối
        </div>
      )
    }
  ]

  const columns: TableProps<ImageCollection>['columns'] = [
    {
      title: 'STT',
      key: 'personCode',
      align: 'center',
      width: 48,
      render: (_, __, index) => (page - 1) * maxSize + (index + 1)
    },
    {
      title: 'Ảnh đại diện',
      dataIndex: 'person',
      key: 'person',
      width: 120,
      align: 'center',
      render: (person: Person) => (
        <div className='flex justify-center'>
          <Image
            src={person.faces?.[0]?.objectUrl}
            alt='avatar'
            className='w-10 h-10 rounded-full object-cover border'
            style={{ width: 40, height: 40 }}
            preview={{ mask: false }}
          />
        </div>
      )
    },
    {
      title: 'Ảnh đại diện mới',
      dataIndex: 'file',
      key: 'file',
      width: 150,
      align: 'center',
      render: (file: Face) => (
        <div className='flex justify-center'>
          {file?.objectUrl ? (
            <Image
              src={file?.objectUrl}
              alt='avatar'
              className='w-10 h-10 rounded-full object-cover border'
              style={{ width: 40, height: 40 }}
              preview={{ mask: false }}
            />
          ) : (
            <Svg src='/assets/icons/common/image-avatar.svg' className='w-10 h-10' />
          )}
        </div>
      )
    },
    {
      title: 'Họ tên',
      dataIndex: 'person',
      key: 'person',
      render: (person: Person) => person.fullName
    },
    {
      title: 'ID',
      dataIndex: 'person',
      key: 'person',
      render: (person: Person) => <p className='uppercase'>{person.personCode}</p>
    },
    {
      title: 'Khóa',
      dataIndex: 'person',
      key: 'person',
      render: (person: Person) => <p className='line-clamp-2'>{person.groupName}</p>
    },
    {
      title: 'Ngày',
      dataIndex: 'createdAt',
      key: 'createdAt'
    },
    {
      title: 'Người duyệt',
      dataIndex: 'approver',
      key: 'approver',
      render: (person: Person) => <p className='line-clamp-2'>{person?.fullName}</p>
    },
    {
      title: 'Trạng thái',
      dataIndex: 'approvalStatus',
      key: 'approvalStatus',
      render: (status) => {
        return (
          <>
            {status === ImageStatus['Chờ duyệt'] && (
              <div className='flex items-center gap-2'>
                <Svg src='/assets/icons/status/reserved.svg' className='h-5 w-5' />
                Chờ duyệt
              </div>
            )}
            {status === ImageStatus['Đã duyệt'] && (
              <div className='flex items-center gap-2'>
                <Svg src='/assets/icons/status/graduated.svg' className='h-5 w-5' />
                Đã duyệt
              </div>
            )}
            {status === ImageStatus['Từ chối'] && (
              <div className='flex items-center gap-2'>
                <Svg src='/assets/icons/status/deactive.svg' className='h-5 w-5' />
                Từ chối
              </div>
            )}
          </>
        )
      }
    },
    {
      title: 'Thao tác',
      width: isMobile ? 60 : 80,
      key: 'action',
      align: 'center',
      fixed: 'right',
      render: (_, record) => (
        <Dropdown menu={{ items }} trigger={['click']} placement='bottomRight'>
          <a
            onClick={(e) => {
              e.preventDefault()
              setUuid(record.uuidImage || '')
            }}
          >
            <MoreOutlined />
          </a>
        </Dropdown>
      )
    }
  ]

  return (
    <>
      <Table
        columns={columns}
        dataSource={data}
        pagination={false}
        rowKey={(record) => record.uuidImage}
        rowSelection={rowSelection}
        scroll={{ x: 1000, y: 560 }}
      />
    </>
  )
}

export default ImageTable
