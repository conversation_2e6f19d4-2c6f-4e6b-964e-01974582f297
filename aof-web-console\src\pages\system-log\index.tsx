import { showCustomNotification } from '@/common/Notification'
import LogTable from '@/components/Tables/LogTable'
import { MESSAGE_STATUS } from '@/constants'
import LayoutCotent from '@/layouts/LayoutCotent'
import { getSystemConfigs, updateSystemConfigs } from '@/services/log'
import { useSegmentStore } from '@/stores/useSegmentStore'
import { logList } from '@/types/components'
import { SystemConfig } from '@/types/log'
import { Button, Form, Input, Segmented, Spin, Switch } from 'antd'
import { useEffect, useState } from 'react'

const SystemLog = () => {
  const [form] = Form.useForm()
  const { activeLog, setActiveLog } = useSegmentStore()
  const [systemConfigLists, setSystemConfigLists] = useState<SystemConfig[]>([])
  const [loading, setLoading] = useState<boolean>(false)

  const fetchData = async () => {
    try {
      setLoading(true)
      const res = await getSystemConfigs()
      if (res.message === MESSAGE_STATUS.SUCCESS) {
        form.setFieldValue('date', JSON.parse(res.object.data[0].value))
        form.setFieldValue('autoApprove', JSON.parse(res.object.data[1].value))
        setSystemConfigLists(res.object.data)
      }
    } catch {
      showCustomNotification({
        status: 'error',
        message: 'Thất bại',
        description: 'Có lỗi xảy ra!'
      })
    } finally {
      setLoading(false)
    }
  }
  useEffect(() => {
    fetchData()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeLog])

  const handleUpdate = async () => {
    if (!systemConfigLists.length) return
    await form.validateFields()
    try {
      const res1 = await updateSystemConfigs(
        systemConfigLists[0].uuidSystemConfig,
        JSON.stringify({ value: form.getFieldValue('date') })
      )

      const res2 = await updateSystemConfigs(
        systemConfigLists[1].uuidSystemConfig,
        JSON.stringify({ value: form.getFieldValue('autoApprove') })
      )

      if (res1?.message === MESSAGE_STATUS.SUCCESS || res2?.message === MESSAGE_STATUS.SUCCESS) {
        showCustomNotification({
          status: 'success',
          message: 'Thành công',
          description: 'Cập nhật cấu hình thành công!'
        })
      }
    } catch {
      showCustomNotification({
        status: 'error',
        message: 'Thất bại',
        description: 'Có lỗi xảy ra!'
      })
    }
  }

  return (
    <LayoutCotent title={['Cài đặt cấu hình']}>
      <Segmented
        value={activeLog}
        onChange={(e) => setActiveLog(e)}
        options={logList}
        size='large'
        className='mb-5 border max-sm:flex max-sm:[&_.ant-segmented-item]:w-1/2'
      />
      <Spin spinning={loading}>
        {activeLog === 'Nhật ký hệ thống' ? (
          <LogTable />
        ) : (
          <Form form={form}>
            <div className='bg-background-card mb-4 border rounded-lg max-sm:w-full w-1/2'>
              <div className='flex items-center gap-1 md:gap-2 p-4'>
                <span className='text-md font-medium'>Thời gian lưu trữ nhật ký </span>
                <Form.Item
                  name='date'
                  className='mb-0'
                  rules={[
                    {
                      required: true,
                      message: ''
                    },
                    {
                      pattern: /^\d+$/,
                      message: 'Chỉ số'
                    },
                    {
                      pattern: /^[1-9][0-9]{0,1}$/,
                      message: ''
                    },
                    {
                      validator: (_, value) =>
                        value && (Number(value) < 1 || Number(value) > 90) ? Promise.reject('') : Promise.resolve()
                    }
                  ]}
                >
                  <Input className='rounded-md p-2 w-10' size='small' maxLength={2} />
                </Form.Item>

                <span className='text-md font-medium'>ngày</span>
              </div>
              <div className='p-4 bg-white rounded-es-lg rounded-ee-lg'>
                <span className='font-medium text-[#282D57]'>Lưu ý:</span> Thời gian lưu trữ nhật ký tối thiểu 1 ngày,
                tối đa 90 ngày
              </div>
            </div>

            <div className='flex items-center justify-between px-6 py-3 bg-background-card max-sm:w-full w-1/2 rounded-lg mb-4'>
              <span className='text-md font-medium'>Tự động duyệt dữ liệu hình ảnh</span>
              <Form.Item name='autoApprove' className='mb-0'>
                <Switch />
              </Form.Item>
            </div>
            <Button
              type='primary'
              size='large'
              className='max-xs:!w-full'
              style={{ width: 126 }}
              onClick={handleUpdate}
            >
              Lưu
            </Button>
          </Form>
        )}
      </Spin>
    </LayoutCotent>
  )
}
export default SystemLog
