import { Button, Image } from 'antd'
import logoImage from '/assets/images/logo2.png'
import noAccess from '/assets/images/no_access.png'
import useAuthStore, { resetAllStores } from '@/stores/auth'
import { isAdmin } from '@/utils/feature-flags'
import { useState } from 'react'
import LicenseModal from '@/components/Modals/LicenseModal'
import LoadingComponent from '@/common/Loading'

const CheckLicense = () => {
  const { user } = useAuthStore()
  const [openLicenseModal, setOpenLicenseModal] = useState<boolean>(false)
  if (!user || !user.username) {
    return <LoadingComponent />
  }
  return (
    <>
      <LicenseModal isModalOpen={openLicenseModal} handleCancel={() => setOpenLicenseModal(false)} />
      <div
        className='min-h-screen flex flex-col items-center justify-center'
        style={{
          background: 'linear-gradient(180deg, #f7fafc 0%, #E5F6F8 100%)'
        }}
      >
        <Image src={logoImage} preview={false} height={100} width={100} />
        <h1 className='font-bold text-2xl sm:text-4xl text-text-primary mt-4 mb-8'>HỌC VIỆN TÀI CHÍNH</h1>
        <div className='w-96 h-96 flex flex-col items-center justify-center mt-4 rounded-full bg-[#E5F6F8] overflow-hidden'>
          <Image src={noAccess} preview={false} />
          {!isAdmin(user) ? (
            <>
              <div className='font-semibold text-lg text-[#1B3B5A] mt-4 mb-2 text-center'>Không có quyền truy cập</div>
              <div className='text-text-4 text-base text-center'>Vui lòng liên hệ với admin</div>
            </>
          ) : (
            <>
              <div className='font-semibold text-lg text-[#1B3B5A] mt-4 mb-2 text-center'>
                Vui lòng nhập license để sử dụng
              </div>
            </>
          )}
          <div className='pt-1 flex flex-col items-center'>
            <Button
              variant='outlined'
              color='primary'
              size='large'
              hidden={!isAdmin(user)}
              onClick={() => {
                setOpenLicenseModal(true)
              }}
            >
              Nhập license
            </Button>
            <Button size='large' type='link' className='underline' onClick={() => resetAllStores()}>
              Đăng xuất
            </Button>
          </div>
        </div>
      </div>
    </>
  )
}

export default CheckLicense
