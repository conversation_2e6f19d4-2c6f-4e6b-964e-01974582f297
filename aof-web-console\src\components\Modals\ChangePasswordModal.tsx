import { Button, Form, Input, Modal } from 'antd'
import { ModalProps } from '@/types/common'
import { showCustomNotification } from '@/common/Notification'
import { changePassword } from '@/services/auth'
import { MESSAGE_STATUS } from '@/constants'
import useAuthStore from '@/stores/auth'

const ChangePasswordModal = ({ isModalOpen, handleCancel, isLoading }: ModalProps) => {
  const [form] = Form.useForm()
  const { user } = useAuthStore()

  const handleOk = async () => {
    await form.validateFields()
    try {
      const res = await changePassword({
        uuidPerson: user.uuidPerson,
        password: form.getFieldValue('currentPassword'),
        newPassword: form.getFieldValue('newPassword')
      })
      if (res.message === MESSAGE_STATUS.SUCCESS) {
        showCustomNotification({
          status: 'success',
          message: 'Thành công!',
          description: 'Thay đổi mật khẩu thành công!'
        })
        form.resetFields()
        handleCancel()
      }
    } catch {
      showCustomNotification({
        status: 'error',
        message: 'Thất bại!',
        description: 'Thay đổi mật khẩu thất bại!'
      })
    }
  }

  const footerModal = (
    <div className='flex items-center justify-center gap-4 p-2 pt-4'>
      <Button
        className='min-w-[126px] bg-neutral-9 hover:opacity-70'
        size='large'
        variant='outlined'
        color='primary'
        disabled={isLoading}
        onClick={handleCancel}
      >
        Hủy
      </Button>
      <Button loading={isLoading} onClick={handleOk} type='primary' size='large' className='min-w-[126px] shadow-none'>
        Xác nhận
      </Button>
    </div>
  )
  return (
    <>
      <Modal
        open={isModalOpen}
        footer={footerModal}
        width={390}
        closable={false}
        title={<p className='text-lg font-semibold text-center'>Đổi mật khẩu</p>}
      >
        <Form
          form={form}
          requiredMark={false}
          layout='vertical'
          initialValues={{ currentPassword: '', newPassword: '', checkPassword: '' }}
        >
          <Form.Item
            name='currentPassword'
            label={
              <p>
                Mật khẩu hiện tại <span className='text-error'>*</span>
              </p>
            }
            rules={[
              {
                required: true,
                message: 'Vui lòng nhập mật khẩu hiện tại!'
              }
            ]}
          >
            <Input.Password size='large' placeholder='Nhập mật khẩu hiện tại' />
          </Form.Item>
          <Form.Item
            name='newPassword'
            label={
              <p>
                Mật khẩu mới <span className='text-error'>*</span>
              </p>
            }
            rules={[
              {
                required: true,
                message: 'Vui lòng nhập mật khẩu mới!'
              }
            ]}
          >
            <Input.Password size='large' placeholder='Nhập mật khẩu mới' />
          </Form.Item>
          <Form.Item
            name='checkPassword'
            label={
              <p>
                Nhập lại mật khẩu mới <span className='text-error'>*</span>
              </p>
            }
            rules={[
              {
                required: true,
                message: 'Vui lòng nhập lại mật khẩu mới!'
              },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('newPassword') === value) {
                    return Promise.resolve()
                  }
                  return Promise.reject(new Error('Mật khẩu nhập lại không khớp!'))
                }
              })
            ]}
          >
            <Input.Password size='large' placeholder='Nhập lại mật khẩu mới' />
          </Form.Item>
        </Form>
      </Modal>
    </>
  )
}

export default ChangePasswordModal
