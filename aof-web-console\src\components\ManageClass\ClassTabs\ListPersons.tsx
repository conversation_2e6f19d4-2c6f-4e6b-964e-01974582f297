import { PlusOutlined } from '@ant-design/icons'
import { Button, Input, Pagination, Select, Spin, Table, TableProps } from 'antd'
import Svg from '../../Svg'
import { useEffect, useRef, useState } from 'react'
import { getListPersons, removeMultiplePersonGroup } from '@/services/person'
import { ClassType, initialProperty, pageSize, TYPE_PERSON } from '@/types/components'
import { MESSAGE_STATUS } from '@/constants'
import { Face, Person } from '@/types/person'
import { showCustomNotification } from '@/common/Notification'
import ModalDelete from '../../Modals/DeleteModal'
import { useNavigate } from 'react-router-dom'
import { useSegmentStore } from '@/stores/useSegmentStore'
import useLayoutStore from '@/stores/layout'
import { ResponseList } from '@/types/common'
import { useInfoStore } from '@/stores/info'
import { setMultiplePersonParams } from '@/helpers/configData'
import useAuthStore from '@/stores/auth'
import { checkFeature, FF_DEV_0041 } from '@/utils/feature-flags'

const ListPersons = ({
  uuidGroup,
  typeName,
  personType
}: {
  uuidGroup?: string
  typeName?: ClassType
  personType: number
}) => {
  const person = personType === TYPE_PERSON['Sinh viên'] ? 'sinh viên' : 'giáo viên'
  const navigate = useNavigate()
  const { setPersonType } = useSegmentStore()
  const { isMobile, isTablet } = useLayoutStore()
  const { setPersons } = useInfoStore()
  const { user, featFlagsAction } = useAuthStore()
  const isFFA = (code: string) => checkFeature(code, user, featFlagsAction)

  const [keySearch, setKeySearch] = useState('')
  const [selectedRows, setSelectedRows] = useState<Person[]>([])
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([])

  const [dataSource, setDataSource] = useState<ResponseList<Person>>()
  const [pageProperty, setPageProperty] = useState(initialProperty)
  const [onAction, setOnAction] = useState<boolean>(false)
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [openDeleteModal, setOpenDeleteModal] = useState<boolean>(false)

  const isPersonTypeChangeRef = useRef(false)

  useEffect(() => {
    setKeySearch('')
    setSelectedRows([])
    setSelectedRowKeys([])
    isPersonTypeChangeRef.current = true
    setPageProperty((prev) => ({ ...prev, page: 1 }))
  }, [personType])

  useEffect(() => {
    if (!uuidGroup) return

    if (isPersonTypeChangeRef.current) {
      isPersonTypeChangeRef.current = false
      getData()
      return
    }

    getData()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [uuidGroup, pageProperty])

  const getData = async () => {
    setIsLoading(true)
    try {
      const res = await getListPersons({
        ...pageProperty,
        keySearch,
        uuidGroups: uuidGroup,
        personTypes: String(personType)
      })
      if (res.message === MESSAGE_STATUS.SUCCESS) {
        setDataSource(res.object)
        setPersons(res.object.data)
      }
    } catch {
      showCustomNotification({
        status: 'error',
        message: 'Thất bại!',
        description: 'Có lỗi xảy ra!'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleRemoveMultiplePerson = async () => {
    if (!uuidGroup) return
    try {
      setOnAction(true)
      const body = setMultiplePersonParams(selectedRows, uuidGroup)
      const res = await removeMultiplePersonGroup({ personGroups: body, groupIdType: 1, personIdType: 1 })
      if (res.message === MESSAGE_STATUS.SUCCESS) {
        showCustomNotification({
          status: 'success',
          message: 'Xóa thành công!',
          description: `Xóa ${selectedRows.length} ${person.toLocaleLowerCase()} khỏi lớp thành công!`
        })
      }
      setSelectedRows([])
      getData()
    } catch {
      showCustomNotification({
        status: 'error',
        message: 'Thất bại!',
        description: `Xóa ${selectedRows.length} ${person.toLocaleLowerCase()} khỏi lớp thất bại!`
      })
    } finally {
      setOnAction(false)
      setOpenDeleteModal(false)
    }
  }

  const rowSelection = {
    selectedRows,
    selectedRowKeys,
    onChange: (_: React.Key[], selectedRows: Person[]) => {
      setSelectedRows(selectedRows)
      setSelectedRowKeys(_)
    }
  }

  const columns: TableProps<Person>['columns'] = [
    {
      title: 'STT',
      dataIndex: 'key',
      key: 'key',
      align: 'center',
      width: 48,
      render: (_, __, index) => (dataSource ? (dataSource.page - 1) * dataSource.maxSize + (index + 1) : index + 1)
    },
    {
      title: 'Ảnh đại diện',
      dataIndex: 'faces',
      key: 'faces',
      width: 120,
      render: (faces: Face[]) => (
        <div className='flex justify-center'>
          {faces.length > 0 ? (
            <img src={faces[0].objectUrl} alt='avatar' className='w-10 h-10 rounded-full object-cover' />
          ) : (
            <Svg src='/assets/icons/common/image-avatar.svg' className='w-10 h-10' />
          )}
        </div>
      )
    },
    {
      title: 'Họ tên',
      dataIndex: 'fullName',
      key: 'fullName'
    },
    {
      title: 'ID',
      dataIndex: 'personCode',
      key: 'personCode',
      render: (value) => value.toUpperCase()
    },
    ...(personType === TYPE_PERSON['Sinh viên']
      ? [
          {
            title: 'Khóa',
            dataIndex: 'groupName',
            key: 'groupName'
          }
        ]
      : [])
  ]

  return (
    <>
      <ModalDelete
        isModalOpen={openDeleteModal}
        isLoading={onAction}
        handleCancel={() => setOpenDeleteModal(false)}
        title='Xóa'
        handleOk={handleRemoveMultiplePerson}
        subTitle='Bạn có chắc chắn muốn xóa nhóm người dùng này?'
      />
      <Spin spinning={isLoading}>
        <div className='flex items-end gap-4 mb-5'>
          <div className='w-full'>
            <label title='Tìm kiếm' className='mb-0 min-w-[220px] w-full'>
              Tìm kiếm
            </label>
            <Input
              size='large'
              className='w-full border-neutral-7'
              placeholder='Nhập tìm kiếm'
              value={keySearch}
              onChange={(e) => setKeySearch(e.target.value)}
              onPressEnter={() => {
                setPageProperty((pre) => ({ ...pre, page: 1, maxSize: 50 }))
              }}
            />
          </div>
          <Button
            size='large'
            style={!isMobile ? undefined : { width: 40 }}
            type='primary'
            icon={<Svg src='/assets/icons/common/search.svg' className='h-5 w-5 mt-1' />}
            onClick={() => {
              setPageProperty((pre) => ({ ...pre, page: 1, maxSize: 50 }))
            }}
          >
            <span className='hidden md:block'>Tìm kiếm</span>
          </Button>
          {isFFA(FF_DEV_0041) && (
            <>
              {selectedRows.length > 0 && (
                <Button
                  size='large'
                  color='primary'
                  variant='outlined'
                  style={!isMobile ? undefined : { width: 40 }}
                  icon={<Svg src='/assets/icons/common/trash.svg' className='h-5 w-5 mt-1' />}
                  onClick={() => setOpenDeleteModal(true)}
                >
                  <span className='hidden sm:block'>Xóa</span>
                </Button>
              )}

              <Button
                size='large'
                icon={<PlusOutlined />}
                style={!isMobile ? undefined : { width: 40 }}
                color='primary'
                variant='outlined'
                onClick={() => {
                  setPersonType(personType)
                  navigate(`/manage-class/${uuidGroup}/add`, { state: typeName })
                }}
              >
                <span className='hidden md:block'>
                  {personType === TYPE_PERSON['Sinh viên']
                    ? 'Thêm sinh viên vào lớp'
                    : typeName === 'Lớp học'
                      ? 'Thêm giáo viên vào lớp'
                      : 'Thêm cán bộ vào lớp'}
                </span>
              </Button>
            </>
          )}
        </div>

        <Table
          columns={columns}
          dataSource={dataSource?.data || []}
          pagination={false}
          rowKey={(record) => record.uuidPerson}
          rowSelection={isFFA(FF_DEV_0041) ? rowSelection : undefined}
          scroll={{ x: 500, y: isMobile || isTablet ? 'max-content' : 470 }}
        />
        {dataSource?.data && dataSource?.data.length > 0 && (
          <div className='mt-4 flex max-sm:flex-col items-center justify-between gap-4'>
            <div className='flex items-center gap-2'>
              <p className='text-xs font-normal text-gray-400'>Số bản ghi mỗi trang </p>
              <Select
                size='small'
                options={pageSize}
                value={pageProperty.maxSize}
                onChange={(e) => setPageProperty({ ...pageProperty, maxSize: e, page: 1 })}
              />
              <p className='text-xs font-normal text-gray-400 hidden sm:block'>
                trên tổng {dataSource.totalElement} dữ liệu{' '}
              </p>
            </div>
            <Pagination
              onChange={(page) => {
                setPageProperty({ ...pageProperty, page })
              }}
              className='mt-4'
              align='end'
              current={dataSource.page}
              showSizeChanger={false}
              defaultCurrent={1}
              pageSize={dataSource.maxSize}
              defaultPageSize={10}
              total={dataSource.totalElement}
            />
          </div>
        )}
      </Spin>
    </>
  )
}

export default ListPersons
