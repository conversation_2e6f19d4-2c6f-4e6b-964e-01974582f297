import { Button, Input, Select, Spin, Table, TableProps } from 'antd'
import { useCallback, useEffect, useState } from 'react'
import { MAX_SIZE_PAGE } from '@/types/components'
import { MESSAGE_STATUS } from '@/constants'
import { showCustomNotification } from '@/common/Notification'
import { getListPersons } from '@/services/person'
import { Face, Person } from '@/types/person'
import {
  addPersonsToPermissionGroup,
  deletePersonsToPermissionGroup,
  getListPersonsPermissionGroup
} from '@/services/auth'
import { PersonPermissionGroup } from '@/types/user'
import useDebounce from '@/hooks/useDebounce'
import Svg from '@/components/Svg'

const User = ({ uuidPermissionGroup, hidden }: { uuidPermissionGroup: string; hidden: boolean }) => {
  const [filter, setFilter] = useState({ personTypesAll: '2,3', searchAll: '', search: '' })

  const [listPersonsAll, setListPersonsAll] = useState<Person[]>([])
  const [listPersonsGroup, setListPersonsGroup] = useState<Person[]>([])
  const [listPersonsGroupInit, setListPersonsGroupInit] = useState<Person[]>([])
  const [listPersonsGroupUpdate, setListPersonsGroupUpdate] = useState<PersonPermissionGroup[]>([])

  const [selectedRows, setSelectedRows] = useState<Person[]>([])
  const [removeRows, setRemoveRows] = useState<Person[]>([])

  const [loadingAll, setLoadingAll] = useState<boolean>(false)
  const [loadingGroup, setLoadingGroup] = useState<boolean>(false)
  const [onUpdating, setOnUpdating] = useState<boolean>(false)
  const [hasMoreAll, setHasMoreAll] = useState<boolean>(true)
  const [hasMoreGroup, setHasMoreGroup] = useState<boolean>(true)

  const [currentPage, setPageAll] = useState(1)
  const [currentPageGroup, setPageGroup] = useState(1)

  const delaySearchAll = useDebounce(filter.searchAll, 500)
  const delaySearch = useDebounce(filter.search, 500)

  const fetchPersons = useCallback(async () => {
    try {
      setLoadingAll(true)
      const res = await getListPersons({
        page: currentPage,
        maxSize: MAX_SIZE_PAGE,
        keySearch: delaySearchAll,
        personTypes: filter.personTypesAll
      })
      if (res.message === MESSAGE_STATUS.SUCCESS) {
        const newData = res.object.data
        setListPersonsAll((prev) => {
          const existingIds = new Set(prev.map((item) => item.uuidPerson))
          const filteredData = newData.filter((item) => !existingIds.has(item.uuidPerson))
          return [...prev, ...filteredData]
        })
        if (newData.length < MAX_SIZE_PAGE) {
          setHasMoreAll(false)
        }
      }
    } catch {
      showCustomNotification({ status: 'error', message: 'Thất bại!', description: 'Có lỗi xảy ra!' })
    } finally {
      setLoadingAll(false)
    }
  }, [currentPage, delaySearchAll, filter.personTypesAll])

  const fetchPersonsPermissionGroup = useCallback(async () => {
    if (!uuidPermissionGroup) return
    try {
      setLoadingGroup(true)
      const res = await getListPersonsPermissionGroup({
        page: currentPageGroup,
        maxSize: MAX_SIZE_PAGE,
        keySearch: delaySearch,
        uuidPermissionGroup
      })
      if (res.message === MESSAGE_STATUS.SUCCESS) {
        const newData = res.object.data.map((item) => item.person)
        setListPersonsGroup((prev) => {
          const existingIds = new Set(prev.map((item) => item.uuidPerson))
          const filteredData = newData.filter((item) => !existingIds.has(item.uuidPerson))
          return [...prev, ...filteredData]
        })
        setListPersonsGroupInit((prev) => {
          const existingIds = new Set(prev.map((item) => item.uuidPerson))
          const filteredData = newData.filter((item) => !existingIds.has(item.uuidPerson))
          return [...prev, ...filteredData]
        })
        if (newData.length < MAX_SIZE_PAGE) {
          setHasMoreGroup(false)
        }
      }
    } catch {
      showCustomNotification({ status: 'error', message: 'Thất bại', description: 'Có lỗi xảy ra!' })
    } finally {
      setLoadingGroup(false)
    }
  }, [currentPageGroup, delaySearch, uuidPermissionGroup])

  useEffect(() => {
    fetchPersons()
  }, [fetchPersons])

  useEffect(() => {
    fetchPersonsPermissionGroup()
  }, [fetchPersonsPermissionGroup])

  useEffect(() => {
    setPageAll(1)
    setListPersonsAll([])
    setHasMoreAll(true)
  }, [delaySearchAll, filter.personTypesAll])

  useEffect(() => {
    const formatted = listPersonsGroup.map((p) => ({
      uuidPerson: p.uuidPerson,
      uuidPermissionGroup: uuidPermissionGroup!
    }))
    setListPersonsGroupUpdate(formatted)
  }, [listPersonsGroup, uuidPermissionGroup])

  const handleSelect = () => {
    const newPersons = selectedRows.filter((item) => !listPersonsGroup.some((p) => p.uuidPerson === item.uuidPerson))
    const newList = [...listPersonsGroup, ...newPersons]
    setListPersonsGroup(newList)
    setListPersonsGroupUpdate(
      newList.map((p) => ({ uuidPerson: p.uuidPerson, uuidPermissionGroup: uuidPermissionGroup! }))
    )
    setSelectedRows([])
  }

  const handleRemove = () => {
    const newList = listPersonsGroup.filter((item) => !removeRows.some((row) => row.uuidPerson === item.uuidPerson))
    setListPersonsGroup(newList)
    setListPersonsGroupUpdate(
      newList.map((p) => ({ uuidPerson: p.uuidPerson, uuidPermissionGroup: uuidPermissionGroup! }))
    )
    setRemoveRows([])
  }

  const handleSave = async () => {
    try {
      setOnUpdating(true)
      const personsToAdd = listPersonsGroupUpdate.filter(
        (itemUpdate) => !listPersonsGroupInit.some((itemOriginal) => itemOriginal.uuidPerson === itemUpdate.uuidPerson)
      )
      const personsToRemove = listPersonsGroupInit.filter(
        (itemOriginal) =>
          !listPersonsGroupUpdate.some((itemUpdate) => itemUpdate.uuidPerson === itemOriginal.uuidPerson)
      )

      const bodyAdd = personsToAdd.map((item) => ({
        uuidPerson: item.uuidPerson,
        uuidPermissionGroup: uuidPermissionGroup!
      }))
      const bodyRemove = personsToRemove.map((item) => ({
        uuidPerson: item.uuidPerson,
        uuidPermissionGroup: uuidPermissionGroup!
      }))

      const resAdd = await addPersonsToPermissionGroup(bodyAdd)
      const resDel = await deletePersonsToPermissionGroup(bodyRemove)

      if (resAdd.message === MESSAGE_STATUS.SUCCESS && resDel.message === MESSAGE_STATUS.SUCCESS) {
        fetchPersons()
        fetchPersonsPermissionGroup()
        setSelectedRows([])
        setRemoveRows([])
        showCustomNotification({
          status: 'success',
          message: 'Thành công',
          description: 'Chỉnh sửa danh sách người dùng của nhóm quyền thành công!'
        })
      }
    } catch {
      showCustomNotification({ status: 'error', message: 'Thất bại', description: 'Lưu thất bại!' })
    } finally {
      setOnUpdating(false)
    }
  }

  const handleCancel = () => {
    // setListPersonsGroupUpdate(listPersonsGroup)
    setSelectedRows([])
    setRemoveRows([])
  }

  const rowSelectionAll = {
    selectedRowKeys: selectedRows.map((r) => r.uuidPerson),
    onChange: (_: React.Key[], rows: Person[]) => setSelectedRows(rows)
  }

  const rowSelectionGroup = {
    selectedRowKeys: removeRows.map((r) => r.uuidPerson),
    onChange: (_: React.Key[], rows: Person[]) => setRemoveRows(rows)
  }

  const columns: TableProps<Person>['columns'] = [
    {
      title: 'Tất cả',
      dataIndex: 'faces',
      key: 'faces',
      width: 60,
      render: (faces: Face[]) => (
        <div className='flex justify-center'>
          {faces?.length > 0 ? (
            <img src={faces?.[0]?.objectUrl} alt='avatar' className='w-10 h-10 rounded-full object-cover border' />
          ) : (
            <Svg src='/assets/icons/common/image-avatar.svg' className='w-10 h-10' />
          )}
        </div>
      )
    },
    {
      title: ' ',
      dataIndex: 'fullName',
      key: 'fullName',
      render: (value, record) => (
        <div className='flex flex-col'>
          {value}
          <span className='text-text-4 text-xs'>ID: {record?.personCode?.toUpperCase()}</span>
        </div>
      )
    }
  ]

  return (
    <Spin spinning={onUpdating}>
      <div className='w-full flex max-md:flex-col gap-5 h-full'>
        {!hidden && (
          <div className='w-1/2 max-md:w-full bg-background-card rounded-lg p-1 h-full'>
            <h2 className='px-5 py-3 text-neutral-1 text-base font-medium'>Danh sách tất cả người dùng</h2>
            <div className='bg-white rounded-lg p-5 h-full'>
              <div className='flex max-md:flex-col gap-5'>
                <div className='flex flex-col w-full'>
                  <label>Phân loại</label>
                  <Select
                    size='large'
                    placeholder='Chọn loại người dùng'
                    onChange={(e) => setFilter((pre) => ({ ...pre, personTypesAll: e }))}
                    options={[
                      { label: 'Tất cả', value: '2,3' },
                      { label: 'Giảng viên', value: '2' },
                      { label: 'Nhân viên chức năng', value: '3' }
                    ]}
                    defaultValue={'2,3'}
                  />
                </div>
                <div className='flex flex-col w-full'>
                  <label>Tìm kiếm</label>
                  <Input
                    size='large'
                    placeholder='Nhập nội dung tìm kiếm'
                    onChange={(e) => setFilter((pre) => ({ ...pre, searchAll: e.target.value }))}
                    suffix={<Svg src='/assets/search.svg' className='h-5 w-5 text-text-4' />}
                  />
                </div>
              </div>
              <div className='min-h-[550px]'>
                <Table
                  loading={loadingAll}
                  columns={columns}
                  dataSource={listPersonsAll}
                  pagination={false}
                  className='m-2'
                  rowKey={(record) => record.uuidPerson}
                  rowSelection={rowSelectionAll}
                  onScroll={({ target }) => {
                    const { scrollTop, scrollHeight, clientHeight } = target as HTMLDivElement
                    if (scrollTop + clientHeight >= scrollHeight - 50 && hasMoreAll && !loadingAll) {
                      setPageAll((prev) => prev + 1)
                    }
                  }}
                  scroll={{ y: 500 }}
                />
              </div>
              <div className='flex justify-center'>
                <Button
                  style={{ width: 120 }}
                  className='mt-2'
                  size='large'
                  variant='outlined'
                  color='primary'
                  disabled={selectedRows.length === 0}
                  onClick={handleSelect}
                >
                  Chọn
                </Button>
              </div>
            </div>
          </div>
        )}

        <div className={`${hidden ? 'w-full' : 'w-1/2'} max-md:w-full bg-background-card rounded-lg p-1 h-full`}>
          <h2 className='px-5 py-3 text-neutral-1 text-base font-medium'>Danh sách người dùng đã chọn</h2>
          <div className='bg-white rounded-lg p-5 h-full'>
            <div className='flex max-md:flex-col gap-5'>
              <div className='flex flex-col w-full'>
                <label>Tìm kiếm</label>
                <Input
                  size='large'
                  placeholder='Nhập nội dung tìm kiếm'
                  onChange={(e) => setFilter((pre) => ({ ...pre, search: e.target.value }))}
                  suffix={<Svg src='/assets/search.svg' className='h-5 w-5 text-text-4' />}
                />
              </div>
            </div>
            <div className='min-h-[550px]'>
              <Table
                loading={loadingGroup}
                columns={columns}
                dataSource={listPersonsGroup}
                pagination={false}
                className='m-2'
                rowKey={(record) => record.uuidPerson}
                rowSelection={!hidden ? rowSelectionGroup : undefined}
                onScroll={({ target }) => {
                  const { scrollTop, scrollHeight, clientHeight } = target as HTMLDivElement
                  if (scrollTop + clientHeight >= scrollHeight - 50 && hasMoreGroup && !loadingGroup) {
                    setPageGroup((prev) => prev + 1)
                  }
                }}
                scroll={{ y: 500 }}
              />
            </div>
            {!hidden && (
              <div className='flex justify-center'>
                <Button
                  style={{ width: 120 }}
                  className='mt-2'
                  size='large'
                  variant='outlined'
                  color='primary'
                  disabled={removeRows.length === 0}
                  onClick={handleRemove}
                >
                  Xóa
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>

      {!hidden && (
        <div className={'items-center justify-center gap-5 p-2 pt-4 flex'}>
          <Button
            className='min-w-[126px] bg-neutral-9 hover:opacity-70'
            size='large'
            variant='outlined'
            color='primary'
            onClick={handleCancel}
          >
            Hủy
          </Button>
          <Button type='primary' size='large' className='min-w-[126px] shadow-none' onClick={handleSave}>
            Lưu
          </Button>
        </div>
      )}
    </Spin>
  )
}

export default User
