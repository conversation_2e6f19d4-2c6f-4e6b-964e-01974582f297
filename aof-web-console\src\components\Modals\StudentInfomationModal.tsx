import { Col, Image, Modal, Row, Spin } from 'antd'
import { ModalProps } from '@/types/common'
import { useEffect, useState } from 'react'
import { Person } from '@/types/person'
import { getDetailPerson } from '@/services/person'
import { showCustomNotification } from '@/common/Notification'
import { UserStatusField } from '../StatusField'

interface Props extends ModalProps {
  uuidPerson: string
}

const ModalStudentInfo = ({ isModalOpen, uuidPerson, handleCancel }: Props) => {
  const [data, setData] = useState<Person>()
  const [isLoading, setIsLoading] = useState<boolean>(false)

  useEffect(() => {
    if (uuidPerson) {
      getData()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [uuidPerson])

  const getData = async () => {
    try {
      setIsLoading(true)
      const res = await getDetail<PERSON>erson(uuidPerson)
      setData(res.object)
    } catch {
      showCustomNotification({
        status: 'error',
        message: 'Thất bại!',
        description: '<PERSON><PERSON><PERSON> thông tin học sinh thất bại!'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const renderGender = () => {
    switch (data?.gender) {
      case 0:
        return 'Khác'
      case 1:
        return 'Nam'
      case 2:
        return 'Nữ'
      default:
        return 'Khác'
    }
  }
  const valueCustomFields = (): Record<string, string> => {
    if (data?.customFields?.length) {
      return data.customFields.reduce<Record<string, string>>((acc, field) => {
        if (field.customFieldName && field.customFieldValue) {
          acc[field.customFieldName] = field.customFieldValue
        }
        return acc
      }, {})
    }
    return { school: '-', specialization: '-' }
  }

  const imageUrl = data?.faces?.[0]?.objectUrl || '/assets/icons/common/image-avatar.svg'

  return (
    <Modal
      open={isModalOpen}
      onCancel={handleCancel}
      width={343}
      title={
        <div
          className='bg-background-tick rounded-t-2xl text-center flex items-center justify-center'
          style={{ height: '3.4rem' }}
        >
          <p className='bg-background-tick font-semibold text-md text-neutral-1 text-center'>Ảnh đại diện</p>
        </div>
      }
      className='customer-model rounded-2xl'
      footer={false}
    >
      <Spin spinning={isLoading}>
        <div className='pt-4 flex justify-center'>
          <Image src={imageUrl} className='max-w-[200px] max-h-[200px]' preview={false} />
        </div>
        <div className='p-2'>
          <Row gutter={[12, 4]}>
            <Col span={9} className='text-neutral-4'>
              Họ và tên:
            </Col>
            <Col span={15} className='text-neutral-2'>
              {data?.fullName || '-'}
            </Col>

            <Col span={9} className='text-neutral-4'>
              Email:
            </Col>
            <Col span={15} className='text-neutral-2'>
              {data?.email || '-'}
            </Col>

            <Col span={9} className='text-neutral-4'>
              ID:
            </Col>
            <Col span={15} className='text-neutral-2'>
              {data?.personCode.toLocaleUpperCase() || '-'}
            </Col>

            <Col span={9} className='text-neutral-4'>
              Ngày sinh:
            </Col>
            <Col span={15} className='text-neutral-2'>
              {data?.dob || '--/--/----'}
            </Col>

            <Col span={9} className='text-neutral-4'>
              Giới tính:
            </Col>
            <Col span={15} className='text-neutral-2'>
              {renderGender()}
            </Col>

            <Col span={9} className='text-neutral-4'>
              Khoa:
            </Col>
            <Col span={15} className='text-neutral-2'>
              {valueCustomFields().school}
            </Col>

            <Col span={9} className='text-neutral-4'>
              Chuyên ngành:
            </Col>
            <Col span={15} className='text-neutral-2'>
              {valueCustomFields().specialization}
            </Col>

            <Col span={9} className='text-neutral-4'>
              Số điện thoại:
            </Col>
            <Col span={15} className='text-neutral-2'>
              {data?.phoneNo || ''}
            </Col>

            <Col span={9} className='text-neutral-4'>
              Trạng thái:
            </Col>
            <Col span={15} className='text-neutral-2'>
              <UserStatusField status={data?.status || 0} />
            </Col>
          </Row>
        </div>
      </Spin>
    </Modal>
  )
}

export default ModalStudentInfo
