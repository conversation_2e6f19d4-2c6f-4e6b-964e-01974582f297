import { NOTIFICATION_TYPE } from '@/constants'
import { notification } from 'antd'

export interface INotify {
  status: NOTIFICATION_TYPE
  message: string
  description: string
}

export function showCustomNotification({ status, description, message }: INotify) {
  if (status) {
    notification[status]({
      message: message,
      description: description,
      placement: 'bottomRight'
    })
  }
}
