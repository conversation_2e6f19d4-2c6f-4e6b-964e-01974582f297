{"name": "aof-management", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "type-check": "tsc --pretty --noEmit", "lint:fix": "eslint . --fix", "prettier": "prettier --check \"src/**/(*.tsx|*.ts|*.css|*.scss)\"", "prettier:fix": "prettier --write \"src/**/(*.tsx|*.ts|*.css|*.scss)\"", "format": "yarn run lint:fix && yarn run prettier:fix", "prepare": "husky install"}, "dependencies": {"@ant-design/icons": "^5.6.0", "@preact/signals": "^2.0.1", "@schedule-x/calendar": "^2.14.3", "@schedule-x/drag-and-drop": "^2.14.3", "@schedule-x/event-modal": "^2.14.3", "@schedule-x/events-service": "^2.14.3", "@schedule-x/react": "^2.13.3", "@schedule-x/theme-default": "^2.14.3", "antd": "^5.23.1", "autoprefixer": "^10.4.20", "axios": "^1.7.9", "dayjs": "^1.11.13", "immer": "^10.1.1", "js-cookie": "^3.0.5", "postcss": "^8.5.1", "preact": "^10.25.4", "react": "^18.3.1", "react-dom": "^18.3.1", "react-player": "^2.16.0", "react-router-dom": "^7.1.2", "runes2": "^1.1.4", "tailwindcss": "^3.4.17", "terser-webpack-plugin": "^5.3.11", "xlsx": "^0.18.5", "yup": "^1.6.1", "zustand": "^4.5.5"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/js-cookie": "^3.0.6", "@types/node": "^22.10.7", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "@vitejs/plugin-react-swc": "^3.7.2", "eslint": "^9.17.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "husky": "^8.0.0", "prettier": "^3.4.2", "typescript": "~5.6.2", "typescript-eslint": "^8.18.2", "vite": "^6.0.5", "vite-plugin-env-compatible": "^2.0.1", "vite-plugin-svgr": "^4.3.0", "vite-plugin-top-level-await": "^1.4.4", "vite-tsconfig-paths": "^5.1.4"}}