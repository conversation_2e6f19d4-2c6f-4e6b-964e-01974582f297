import { TYPE_COURSE } from '@/types/components'
import { Group } from '@/types/group'
import { create } from 'zustand'

interface State {
  loading: boolean
  groupType: number
  listGroups: Group[]
  groupName: string
  setGroupName: (e: string) => void
  setGroupType: (type: number) => void
  setGroups: (groups: Group[]) => void
  setLoading: (e: boolean) => void
}

const initialState = {
  loading: false,
  groupType: TYPE_COURSE,
  listGroups: [],
  groupName: ''
}

const useGroupStore = create<State>((set) => ({
  ...initialState,
  setGroupName: (e) => set({ groupName: e }),
  setGroups: (group) => set({ listGroups: group }),
  setGroupType: (type) => set({ groupType: type }),
  setLoading: (e) => set({ loading: e })
}))

export default useGroupStore
