import { useEffect, useMemo } from 'react'
import { BrowserRouter as Router } from 'react-router-dom'
import { ConfigProvider, theme } from 'antd'
import useAuthStore from '@/stores/auth'

import ProtectedRoutes from './ProtectedRoutes'

const AppRouter = () => {
  const { fetchUsers, fetchLicenseStatus, isLoggedIn, checkUser } = useAuthStore()

  useEffect(() => {
    if (isLoggedIn) {
      checkUser()
      fetchLicenseStatus()
      fetchUsers()
    }
  }, [isLoggedIn, fetchUsers, fetchLicenseStatus, checkUser])

  const customTheme = useMemo(
    () => ({
      algorithm: theme.defaultAlgorithm,
      token: {
        colorPrimary: '#00707E',
        colorText: '#273266',
        controlItemBgHover: '#E5F6F8',
        rowHoverBg: '#D3ECEE'
      }
    }),
    []
  )

  return (
    <ConfigProvider theme={customTheme}>
      <Router>
        <ProtectedRoutes />
      </Router>
    </ConfigProvider>
  )
}

export default AppRouter
