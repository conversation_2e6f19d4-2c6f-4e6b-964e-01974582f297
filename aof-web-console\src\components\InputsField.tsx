import {
  customFieldPersonList,
  gender,
  statusCreditClass,
  statusExamClass,
  statusOther,
  statusStudent,
  TYPE_CLASS_CREDIT,
  TYPE_PERSON
} from '@/types/components'
import { Group } from '@/types/group'
import { AcademicYear, Semester } from '@/types/infomation'
import { DatePicker, Form, Input, Select } from 'antd'
import dayjs from 'dayjs'
import Svg from './Svg'

const { RangePicker } = DatePicker

export const nameField = (
  <Form.Item
    name='fullName'
    className='mb-5'
    label={
      <p>
        Họ tên <span className='text-error'>*</span>
      </p>
    }
    rules={[
      {
        validator: (_, value) => {
          if (!value?.trim()) {
            return Promise.reject('Vui lòng nhập họ và tên!')
          }
          return Promise.resolve()
        }
      }
    ]}
  >
    <Input placeholder='Nhập họ và tên' size='large' maxLength={30} />
  </Form.Item>
)

export const numberIdField = (update: boolean) => (
  <Form.Item
    name='personCode'
    className='mb-5'
    label={
      <p>
        ID <span className='text-error'>*</span>
      </p>
    }
    rules={[
      {
        validator: (_, value) => {
          if (!value?.trim()) {
            return Promise.reject('Vui lòng nhập person ID!')
          }
          return Promise.resolve()
        }
      }
    ]}
  >
    <Input placeholder='Nhập ID' size='large' disabled={update} maxLength={20} />
  </Form.Item>
)

export const dateField = (
  <Form.Item name='dob' className='mb-5' label='Ngày sinh'>
    <DatePicker
      allowClear
      placeholder='Chọn ngày sinh'
      size='large'
      className='w-full'
      format='DD/MM/YYYY'
      suffixIcon={<Svg src='/assets/icons/common/datepicker.svg' className='h-4 w-4' />}
      disabledDate={(current) => {
        const today = dayjs().endOf('day')
        const oldestDate = dayjs().subtract(120, 'years')
        return current && (current > today || current < oldestDate)
      }}
    />
  </Form.Item>
)

export const emailField = (
  <Form.Item
    name='email'
    className='mb-5'
    label={
      <p>
        Email <span className='text-error'>*</span>
      </p>
    }
    rules={[
      {
        required: true,
        message: 'Vui lòng nhập email!'
      },
      {
        type: 'email',
        message: 'Email không hợp lệ!'
      }
    ]}
  >
    <Input placeholder='Nhập email' size='large' type='email' autoComplete='email' maxLength={50} />
  </Form.Item>
)

export const courseField = (list: Group[]) => (
  <Form.Item
    name='uuidGroup'
    className='mb-5'
    label={
      <p>
        Khóa <span className='text-error'>*</span>
      </p>
    }
    rules={[
      {
        required: true,
        message: 'Vui lòng chọn khóa!'
      }
    ]}
  >
    <Select
      placeholder='Chọn khóa'
      size='large'
      options={list.map((item) => ({
        label: item.groupName,
        value: item.uuidGroup
      }))}
      optionRender={(option) => <div>{option.label}</div>}
    />
  </Form.Item>
)

export const genderField = (
  <Form.Item name='gender' className='mb-5' label='Giới tính'>
    <Select placeholder='Chọn giới tính' size='large' options={gender} allowClear />
  </Form.Item>
)

export const specializationField = (
  <Form.Item name={customFieldPersonList[1]} className='mb-5' label='Chuyên ngành'>
    <Input placeholder='Nhập chuyên ngành' size='large' allowClear maxLength={50} />
  </Form.Item>
)

export const statusField = (data: TYPE_PERSON) => (
  <Form.Item
    name='status'
    className='mb-5'
    label={
      <p>
        Trạng thái tài khoản <span className='text-error'>*</span>
      </p>
    }
    rules={[
      {
        required: true,
        message: 'Vui lòng chọn trạng thái!'
      }
    ]}
  >
    <Select
      placeholder='Chọn trạng thái'
      size='large'
      options={data === TYPE_PERSON['Sinh viên'] ? statusStudent : statusOther}
      allowClear
    />
  </Form.Item>
)

export const schoolField = (
  <Form.Item name={customFieldPersonList[2]} className='mb-5' label='Khoa'>
    <Input placeholder='Nhập khoa' size='large' allowClear maxLength={50} />
  </Form.Item>
)

export const phoneNumberField = (
  <Form.Item
    name='phoneNo'
    className='mb-5'
    label='Số điện thoại'
    rules={[
      {
        required: false,
        message: 'Vui lòng nhập số điện thoại!'
      },
      {
        pattern: /^[0-9]{10,11}$/,
        message: 'Số điện thoại không hợp lệ!'
      }
    ]}
  >
    <Input placeholder='Nhập số điện thoại' size='large' allowClear />
  </Form.Item>
)

export const unitField = (
  <Form.Item name={customFieldPersonList[0]} className='mb-5' label='Đơn vị'>
    <Input placeholder='Nhập tên đơn vị' size='large' allowClear maxLength={50} />
  </Form.Item>
)

export const classNameField = ({ onUpdate }: { onUpdate?: boolean }) => (
  <Form.Item
    name='groupName'
    className='mb-5'
    label={
      <p>
        Tên lớp <span className='text-error'>*</span>
      </p>
    }
    rules={[
      {
        required: true,
        message: 'Tên lớp không được để trống!'
      }
    ]}
  >
    <Input placeholder='Nhập tên lớp' size='large' disabled={onUpdate} />
  </Form.Item>
)

export const classIdField = ({ onUpdate }: { onUpdate?: boolean }) => (
  <Form.Item
    name='groupCode'
    className='mb-5'
    label={
      <p>
        Mã lớp <span className='text-error'>*</span>
      </p>
    }
    rules={[
      {
        required: true,
        message: 'Mã lớp không được để trống!'
      }
    ]}
  >
    <Input placeholder='Nhập mã lớp' size='large' disabled={onUpdate} />
  </Form.Item>
)

export const numberCreditsField = ({ onUpdate }: { onUpdate?: boolean }) => (
  <Form.Item
    name='numberOfCredits'
    className='mb-5'
    label='Số tín chỉ'
    rules={[{ pattern: /^(100|[1-9][0-9]?)$/, message: 'Số tín chỉ phải là số, từ 1 đến 100!' }]}
  >
    <Input placeholder='Nhập số tín chỉ' size='large' disabled={onUpdate} />
  </Form.Item>
)

export const numberLessonsField = ({ onUpdate }: { onUpdate?: boolean }) => (
  <Form.Item
    name='numberOfClassPeriods'
    className='mb-5'
    label='Số tiết học'
    rules={[{ pattern: /^(100|[1-9][0-9]?)$/, message: 'Số tiết học phải là số, từ 1 đến 100!' }]}
  >
    <Input placeholder='Nhập số tiết học' size='large' disabled={onUpdate} />
  </Form.Item>
)

export const numberStudentsField = (
  <Form.Item
    name='studentCount'
    className='mb-5'
    label={
      <p>
        Số sinh viên <span className='text-error'>*</span>
      </p>
    }
  >
    <Input size='large' disabled />
  </Form.Item>
)

export const yearField = ({
  onUpdate,
  academicYears,
  setSelectYear
}: {
  onUpdate?: boolean
  academicYears: AcademicYear[]
  setSelectYear: (e: string) => void
}) => (
  <Form.Item
    name='academicYear'
    className='mb-5'
    label={
      <p>
        Năm học <span className='text-error'>*</span>
      </p>
    }
    rules={[
      {
        required: true,
        message: 'Năm học không được để trống!'
      }
    ]}
  >
    <Select
      placeholder='Chọn năm học'
      size='large'
      disabled={onUpdate}
      options={academicYears.map((item) => ({
        label: item.name,
        value: item.uuidAcademicYear
      }))}
      onChange={(e) => setSelectYear(e)}
    />
  </Form.Item>
)

export const semesterField = ({
  onUpdate,
  semesters,
  loadingData
}: {
  onUpdate?: boolean
  semesters: Semester[]
  loadingData?: boolean
}) => (
  <Form.Item
    name='uuidSemester'
    className='mb-5'
    label={
      <p>
        Kỳ học <span className='text-error'>*</span>
      </p>
    }
    rules={[
      {
        required: true,
        message: 'Kỳ học không được để trống!'
      }
    ]}
  >
    <Select
      loading={loadingData}
      placeholder='Chọn kỳ học'
      size='large'
      disabled={onUpdate}
      options={semesters.map((item) => ({
        label: item.name,
        value: item.uuidSemester
      }))}
    />
  </Form.Item>
)

export const timeField = ({ onUpdate, isMobile }: { onUpdate?: boolean; isMobile: boolean }) => (
  <Form.Item
    name='rangeTime'
    className='mb-5 w-full'
    label={
      <p>
        Thời gian <span className='text-error'>*</span>
      </p>
    }
    rules={[
      {
        required: true,
        message: 'Thời gian không được để trống!'
      }
    ]}
  >
    <RangePicker
      size='large'
      className='!w-full'
      disabled={onUpdate}
      format={'DD/MM/YYYY'}
      allowEmpty
      suffixIcon={<Svg src='/assets/icons/common/datepicker.svg' className='h-4 w-4' />}
      popupClassName={isMobile ? 'custom-range-picker' : undefined}
    />
  </Form.Item>
)

export const statusClassField = ({ onUpdate, groupType }: { onUpdate?: boolean; groupType: number }) => (
  <Form.Item
    name='progressStatus'
    className='mb-5'
    label={
      <p>
        Trạng thái <span className='text-error'>*</span>
      </p>
    }
  >
    <Select
      size='large'
      className='w-full'
      placeholder='Chọn trạng thái'
      options={groupType === TYPE_CLASS_CREDIT ? statusCreditClass : statusExamClass}
      disabled={onUpdate}
    />
  </Form.Item>
)
