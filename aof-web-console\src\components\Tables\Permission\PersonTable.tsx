import { Button, Image, Input, Select, Table, TableProps } from 'antd'
import { useEffect, useState } from 'react'
import Svg from '../../Svg'

import { MESSAGE_STATUS } from '@/constants'
import { showCustomNotification } from '@/common/Notification'

import useLayoutStore from '@/stores/layout'

import { getListPersons } from '@/services/person'
import { Face, Person } from '@/types/person'
import { UserStatusField } from '@/components/StatusField'
import { ResponseList } from '@/types/common'
import { enumToOptions } from '@/helpers'
import { TYPE_PERSON } from '@/types/components'
import useDebounce from '@/hooks/useDebounce'
import { useNavigate } from 'react-router-dom'

export interface InfoTableProps {
  page: number
  maxSize: number
  isLoading: boolean
  dataSource?: ResponseList<any>
  setIsLoading: (value: boolean) => void
  setDataSource: (value: ResponseList<any>) => void
}

const PermissionPersonTable = ({
  page,
  maxSize,
  isLoading,
  dataSource,
  setIsLoading,
  setDataSource
}: InfoTableProps) => {
  const { isMobile, isTablet } = useLayoutStore()

  const [keySearch, setKeySearch] = useState('')
  const searchParams = useDebounce(keySearch, 500)
  const [personTypes, setPersonTypes] = useState('')
  const navigate = useNavigate()

  const personTypeOptions = enumToOptions(TYPE_PERSON)

  useEffect(() => {
    getData()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [personTypes, searchParams, page, maxSize])

  const getData = async () => {
    try {
      setIsLoading(true)
      const res = await getListPersons({ page, maxSize, personTypes, keySearch: searchParams })
      if (res.message === MESSAGE_STATUS.SUCCESS) {
        setDataSource(res.object)
      }
    } catch {
      showCustomNotification({ status: 'error', message: 'Thất bại', description: 'Có lỗi xảy ra!' })
    } finally {
      setIsLoading(false)
    }
  }

  const columns: TableProps<Person>['columns'] = [
    { title: 'STT', align: 'center', width: 48, render: (_, __, index) => (page - 1) * maxSize + (index + 1) },
    {
      title: 'Ảnh đại diện',
      dataIndex: 'faces',
      key: 'faces',
      width: 120,
      render: (faces: Face[]) => (
        <div className='flex justify-center'>
          {faces?.length > 0 ? (
            <Image
              src={faces?.[0]?.objectUrl}
              className='rounded-full object-cover border'
              alt='avatar'
              style={{ width: 32, height: 32 }}
              preview={{ mask: false }}
            />
          ) : (
            <Svg src='/assets/icons/common/image-avatar.svg' className='w-8 h-8' />
          )}
        </div>
      )
    },
    {
      title: 'Họ tên',
      dataIndex: 'fullName',
      key: 'fullName'
    },
    {
      title: 'ID',
      dataIndex: 'personCode',
      key: 'personCode',
      render: (value) => value?.toLocaleUpperCase()
    },

    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email'
    },

    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      render: (status) => <UserStatusField status={status} />
    },
    {
      title: 'Thao tác',
      width: isMobile || isTablet ? 60 : 80,
      key: 'action',
      align: 'center',
      render: (_, record) => (
        <div className='flex justify-center'>
          <Svg
            src='/assets/icons/common/eye.svg'
            className='h-5 w-5 text-neutral-4 cursor-pointer'
            onClick={() =>
              navigate(`/manage-feature/people/${record.uuidPerson}`, {
                state: { fullName: `${record.fullName} - ${record.personCode.toUpperCase()}` }
              })
            }
          />
        </div>
      )
    }
  ]

  return (
    <>
      <div className='grid grid-cols-10 sm:flex gap-1 xs:gap-3 items-end mb-5 lg:w-1/2'>
        <div className={`sm:w-1/2 ${isMobile ? 'col-start-1' : 'col-start-6'} col-end-12`}>
          <p>Trạng thái</p>
          <Select
            size='large'
            className='w-full border-neutral-7'
            placeholder='Chọn trạng thái'
            value={personTypes}
            options={[{ label: 'Tất cả', value: '' }, ...personTypeOptions]}
            onChange={(value: any) => setPersonTypes(value)}
          />
        </div>
        <div className='w-full col-start-1 col-end-11'>
          <p>Tìm kiếm</p>
          <Input
            size='large'
            className='w-full border-neutral-7'
            placeholder='Nhập tìm kiếm'
            value={keySearch}
            onChange={(e) => setKeySearch(e.target.value)}
            suffix={
              <Button
                size='small'
                type='primary'
                icon={<Svg src='/assets/icons/common/search.svg' className='h-4 w-4 mt-1' />}
                onClick={getData}
              />
            }
          />
        </div>
      </div>
      <Table
        loading={isLoading}
        columns={columns}
        dataSource={dataSource?.data}
        pagination={false}
        scroll={{ x: isMobile || isTablet ? 1000 : 470, y: 550 }}
        rowKey={(record) => record.uuidPerson}
      />
    </>
  )
}

export default PermissionPersonTable
