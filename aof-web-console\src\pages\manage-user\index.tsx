/* eslint-disable react-hooks/exhaustive-deps */
import { useNavigate } from 'react-router-dom'
import { useEffect, useState } from 'react'
import { Button, DatePicker, Dropdown, Input, Pagination, Segmented, Select, Spin } from 'antd'
import { CloseOutlined, MoreOutlined, PlusOutlined } from '@ant-design/icons'
import { showCustomNotification } from '@/common/Notification'
import ModalDelete from '@/components/Modals/DeleteModal'
import ModalImport from '@/components/Modals/ImportModal'
import Svg from '@/components/Svg'
import PersonTable from '@/components/Tables/PersonTable'
import { ERROR_FROM_USER, MESSAGE_STATUS } from '@/constants'
import { convertToPersonParams, convertToStatusParams, enumToOptions, exportData, getEnumNameByValue } from '@/helpers'
import LayoutCotent from '@/layouts/LayoutCotent'
import { getCustomFields } from '@/services/common'
import { getListGroups } from '@/services/group'
import {
  addMultiplePerson,
  approveMultipleImages,
  deletePerson,
  getListImagesCollection,
  getListPersons,
  updateMultipleStatus
} from '@/services/person'
import useLayoutStore from '@/stores/layout'
import useGroupStore from '@/stores/group'
import { ImageCollection, Person, PersonParams, PersonStatus } from '@/types/person'
import {
  EntityType,
  ImageStatus,
  MAX_SIZE_PAGE,
  TYPE_CLASS_CREDIT,
  TYPE_COURSE,
  TYPE_PERSON,
  initialProperty,
  pageSize,
  statusImage,
  statusOther,
  statusStudent
} from '@/types/components'
import { ResponseList } from '@/types/common'
import { useSegmentStore } from '@/stores/useSegmentStore'
import ModalResultImport from '@/components/Modals/ResultImportModal'
import useAuthStore from '@/stores/auth'
import { checkFeature, FF_DEV_0007, FF_DEV_0024, FF_DEV_0026 } from '@/utils/feature-flags'
import dayjs, { Dayjs } from 'dayjs'
import ImageTable from '@/components/Tables/ImageTable'

const { RangePicker } = DatePicker

const initial = {
  keySearch: '',
  status: 1,
  uuidGroups: '',
  ...initialProperty,
  personTypes: ''
}

const initialResult = { status: false, numResultSuccess: 0, numResultFail: 0 }

const ManageUser = () => {
  const navigate = useNavigate()

  const { activePerson, setActivePerson } = useSegmentStore()
  const { isMobile, isTablet } = useLayoutStore()
  const { loading, listGroups, groupType, setGroups, setGroupType, setLoading } = useGroupStore()
  const { user, featFlagsAction, featFlagsChild } = useAuthStore()
  const isFFA = (code: string) => checkFeature(code, user, featFlagsAction)
  const isFFC = (code: string) => checkFeature(code, user, featFlagsChild)

  const [params, setParams] = useState(initial)
  const [keySearch, setKeySearch] = useState('')
  const [dataImport, setDataImport] = useState<any[]>([])
  const [dataSource, setDataSource] = useState<ResponseList<Person>>()
  const [listImages, setListImages] = useState<ResponseList<ImageCollection>>()
  const [selectedRows, setSelectedRows] = useState<Person[]>([])
  const [selectedRowImages, setSelectedRowImages] = useState<ImageCollection[]>([])

  const defaultRange: [Dayjs, Dayjs] = [dayjs().startOf('month'), dayjs()]
  const [rangeTime, setRangeTime] = useState<[Dayjs, Dayjs]>(defaultRange)

  const [onAction, setOnAction] = useState<boolean>(false)
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [importUser, setImportUser] = useState<boolean>(false)
  const [openImportModal, setOpenImportModal] = useState<boolean>(false)
  const [openDeleteModal, setOpenDeleteModal] = useState<boolean>(false)
  const [showResult, setShowResult] = useState(initialResult)

  const personTypeName = getEnumNameByValue(TYPE_PERSON, activePerson)
  const personTypeOptions = enumToOptions(TYPE_PERSON)

  useEffect(() => {
    switch (activePerson) {
      case TYPE_PERSON['Sinh viên']:
        setParams({ ...initial, personTypes: String(TYPE_PERSON['Sinh viên']) })
        setGroupType(TYPE_COURSE)
        break
      case TYPE_PERSON['Giảng viên']:
        setParams({ ...initial, personTypes: String(TYPE_PERSON['Giảng viên']) })
        setGroupType(TYPE_CLASS_CREDIT)
        break
      case TYPE_PERSON['Nhân viên chức năng']:
        setParams({ ...initial, personTypes: String(TYPE_PERSON['Nhân viên chức năng']) })
        break
      case TYPE_PERSON['Phê duyệt ảnh']:
        setParams({ ...initial })
        break
      default:
        setParams({ ...initial, personTypes: String(TYPE_PERSON['Sinh viên']) })
        setGroupType(TYPE_COURSE)
        break
    }
  }, [activePerson])

  useEffect(() => {
    if (activePerson !== TYPE_PERSON['Phê duyệt ảnh']) {
      fetchListGroups()
    }
  }, [groupType])

  useEffect(() => {
    if (activePerson !== TYPE_PERSON['Phê duyệt ảnh'] && params.personTypes) {
      getData()
    } else if (activePerson === TYPE_PERSON['Phê duyệt ảnh']) {
      getImageCollection()
    }
  }, [params, rangeTime])

  const fetchListGroups = async () => {
    try {
      setLoading(true)
      const res = await getListGroups({
        page: 1,
        maxSize: MAX_SIZE_PAGE,
        groupType: groupType
      })

      if (res.message === MESSAGE_STATUS.SUCCESS) {
        setGroups(res.object.data)
      }
    } catch {
      setGroups([])
      console.error('Failed to get list of groups!')
    } finally {
      setLoading(false)
    }
  }

  const getData = async () => {
    setIsLoading(true)
    try {
      const res = await getListPersons(params)
      if (res.message === MESSAGE_STATUS.SUCCESS) {
        setDataSource(res.object)
      }
    } catch {
      showCustomNotification({
        status: 'error',
        message: 'Thất bại!',
        description: 'Có lỗi xảy ra!'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const getImageCollection = async () => {
    try {
      setIsLoading(true)
      const res = await getListImagesCollection({
        page: params.page,
        maxSize: params.maxSize,
        keySearch: params.keySearch,
        startTime: dayjs(rangeTime[0]).format('DD/MM/YYYY'),
        endTime: dayjs(rangeTime[1]).format('DD/MM/YYYY'),
        approvalStatuses: String(params.status)
      })
      if (res.message === MESSAGE_STATUS.SUCCESS) {
        setListImages(res.object)
      }
    } catch {
      showCustomNotification({
        status: 'error',
        message: 'Thất bại!',
        description: 'Có lỗi xảy ra!'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleExport = async () => {
    try {
      setIsLoading(true)
      let page = 1
      let allData: any[] = []
      let hasMore = true

      while (hasMore) {
        const res = await getListPersons({ ...params, page, maxSize: MAX_SIZE_PAGE })

        if (res.message !== MESSAGE_STATUS.SUCCESS) {
          throw new Error('Fetch error')
        }

        const currentData = res.object?.data || []
        allData = [...allData, ...currentData]

        hasMore = currentData.length === MAX_SIZE_PAGE
        page += 1
      }

      exportData({
        data: allData,
        filename: personTypeName.toLowerCase()
      })
    } catch {
      showCustomNotification({
        status: 'error',
        message: 'Thất bại!',
        description: 'Có lỗi xảy ra trong quá trình xuất dữ liệu!'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleOk = async (persons: any) => {
    try {
      setOnAction(true)
      const resCustomField = await getCustomFields(EntityType.Person)
      if (resCustomField.message === MESSAGE_STATUS.SUCCESS) {
        if (importUser) {
          const body = persons.map((person: PersonParams) =>
            convertToPersonParams(person, Number(params.personTypes), listGroups, resCustomField.object.data)
          )
          const res = await addMultiplePerson(body)
          if (res.message === MESSAGE_STATUS.SUCCESS) {
            setDataImport([])
            setShowResult({
              status: true,
              numResultFail: res.object.errors.length,
              numResultSuccess: body.length - res.object.errors.length
            })
          }
        } else {
          const body = persons.map((person: PersonStatus) => convertToStatusParams(person))
          const res = await updateMultipleStatus(body, 2)
          if (res.message === MESSAGE_STATUS.SUCCESS) {
            setDataImport([])
            setShowResult({
              status: true,
              numResultFail: res.object.errors.length,
              numResultSuccess: body.length - res.object.errors.length
            })
          }
        }
      }
      setParams({ ...params, page: 1 })
    } catch (e: any) {
      const errorMessages: { [key: string]: { message: string; description: string } } = {
        [ERROR_FROM_USER.INVALID_GROUP]: {
          message: 'Lỗi khóa!',
          description: 'Không tìm thấy khóa phù hợp với dữ liệu nhập vào!'
        },
        [ERROR_FROM_USER.INVALID_STATUS]: {
          message: 'Lỗi trạng thái!',
          description: 'Không tìm thấy trạng thái phù hợp với dữ liệu nhập vào!'
        },
        [ERROR_FROM_USER.INVALID_GENDER]: {
          message: 'Lỗi giới tính!',
          description: 'Không tìm thấy giới tính phù hợp với dữ liệu nhập vào!'
        },
        [ERROR_FROM_USER.MISSING_REQUIRED_FIELD]: {
          message: 'Thiếu dữ liệu!',
          description: 'Vui lòng kiểm tra lại các trường bắt buộc!'
        },
        [ERROR_FROM_USER.INVALID_STUDENT_DATA]: {
          message: 'Thiếu dữ liệu!',
          description: 'Không có dữ liệu để nạp!'
        }
      }

      const errorInfo = errorMessages[e.message] || {
        message: 'Thất bại!',
        description: 'Có lỗi xảy ra trong quá trình nhập dữ liệu!'
      }

      showCustomNotification({
        status: 'error',
        message: errorInfo.message,
        description: errorInfo.description
      })
    } finally {
      setOnAction(false)
      setImportUser(false)
      setOpenImportModal(false)
    }
  }

  const handleRemoveMultiplePerson = async () => {
    try {
      setOnAction(true)
      await Promise.all(selectedRows.map((person) => deletePerson(person.uuidPerson)))
      showCustomNotification({
        status: 'success',
        message: 'Xóa thành công!',
        description: `Xóa ${selectedRows.length} ${personTypeName.toLocaleLowerCase()} thành công!`
      })
      setSelectedRows([])
      setParams({ ...params, page: 1 })
    } catch {
      showCustomNotification({
        status: 'error',
        message: 'Thất bại!',
        description: `Xóa ${selectedRows.length} ${personTypeName.toLocaleLowerCase()} thất bại!`
      })
    } finally {
      setOnAction(false)
      setOpenDeleteModal(false)
    }
  }

  const approveMultipleImage = async (status: number) => {
    try {
      setOnAction(true)
      const res = await approveMultipleImages({
        uuidImages: selectedRowImages.map((image) => image.uuidImage),
        approvalStatus: status
      })
      if (res.message === MESSAGE_STATUS.SUCCESS) {
        setSelectedRowImages([])
        setParams({ ...params, page: 1 })
        showCustomNotification({
          status: 'success',
          message: `${status === ImageStatus['Đã duyệt'] ? 'Đã Duyệt' : 'Từ chối'}`,
          description: `${status === ImageStatus['Đã duyệt'] ? 'Duyệt' : 'Từ chối duyệt'} ${selectedRowImages.length} ảnh thành công!`
        })
      }
    } catch {
      showCustomNotification({
        status: 'error',
        message: 'Thất bại!',
        description: 'Có lỗi xảy ra trong quá trình duyệt ảnh!'
      })
    } finally {
      setOnAction(false)
    }
  }
  const viewTable = () => {
    switch (activePerson) {
      case TYPE_PERSON['Phê duyệt ảnh']:
        return (
          <ImageTable
            selectedRow={selectedRowImages}
            data={listImages?.data || []}
            page={listImages?.page || 1}
            maxSize={listImages?.maxSize || 10}
            setOnAction={setOnAction}
            setParams={setParams}
            setSelectedRows={setSelectedRowImages}
            refreshData={async () => setParams({ ...params, page: 1 })}
          />
        )
      default:
        return (
          <PersonTable
            active={personTypeName}
            data={dataSource?.data || []}
            page={dataSource?.page || 1}
            maxSize={dataSource?.maxSize || 10}
            selectedRow={selectedRows}
            refreshData={async () => setParams({ ...params, page: 1 })}
            setSelectedRows={setSelectedRows}
          />
        )
    }
  }

  const items = [
    {
      key: 'export',
      label: (
        <div onClick={handleExport} className='flex items-center p-1 gap-2'>
          <Svg src='/assets/icons/common/export-excel.svg' className='h-5 w-5' />
          <span>Xuất excel</span>
        </div>
      )
    },
    ...(isFFA(FF_DEV_0024)
      ? [
          {
            key: 'import-user',
            label: (
              <div
                onClick={() => {
                  setOpenImportModal(true)
                  setImportUser(true)
                }}
                className='flex items-center p-1 gap-2'
              >
                <Svg src='/assets/icons/common/import-user.svg' className='h-5 w-5' />
                <span>Import người dùng</span>
              </div>
            )
          },
          {
            key: 'import-status',
            label: (
              <div onClick={() => setOpenImportModal(true)} className='flex items-center p-1 gap-2'>
                <Svg src='/assets/icons/common/import-status.svg' className='h-5 w-5' />
                <span>Import trạng thái</span>
              </div>
            )
          },

          {
            key: 'add',
            label: (
              <div onClick={() => navigate('/manage-user/add')} className='flex items-center p-1 gap-2'>
                <PlusOutlined className=' ml-1 h-5 w-4 text-text-primary' />
                <span>Thêm mới</span>
              </div>
            )
          }
        ]
      : [])
  ]

  return (
    <>
      <ModalDelete
        isModalOpen={openDeleteModal}
        isLoading={onAction}
        handleCancel={() => {
          setOpenDeleteModal(false)
        }}
        title='Xóa'
        handleOk={handleRemoveMultiplePerson}
        subTitle='Bạn có chắc chắn muốn xóa nhóm người dùng này?'
      />
      <ModalResultImport
        isModalOpen={showResult.status}
        handleCancel={() => setShowResult(initialResult)}
        numRecordFail={showResult.numResultFail}
        numRecordSuccess={showResult.numResultSuccess}
      />
      <ModalImport
        isModalOpen={openImportModal}
        handleOk={handleOk}
        isLoading={onAction}
        handleCancel={() => {
          setOpenImportModal(false)
          setImportUser(false)
        }}
        active={activePerson}
        dataImport={dataImport}
        setDataImport={setDataImport}
        title={importUser ? 'Import người dùng' : 'Import trạng thái'}
      />
      <LayoutCotent title={['Quản lý người dùng']}>
        <Spin spinning={isLoading}>
          <div className='overflow-x-auto' style={{ scrollbarWidth: 'none' }}>
            <Segmented
              value={activePerson}
              onChange={(value) => {
                setActivePerson(value)
                setKeySearch('')
              }}
              options={(isFFC(FF_DEV_0007) && personTypeOptions) || []}
              size='large'
              className='mb-2 xs:mb-5 border'
            />
          </div>
          <div className='grid grid-cols-10 sm:flex gap-1 xs:gap-3 items-end mb-5'>
            {activePerson === TYPE_PERSON['Phê duyệt ảnh'] && (
              <div className='sm:w-1/2 col-start-1 col-end-6'>
                <p>Chọn thời gian</p>
                <RangePicker
                  size='large'
                  className='w-full border-neutral-7'
                  value={rangeTime}
                  format='DD/MM/YYYY'
                  suffixIcon={<Svg src='/assets/icons/common/datepicker.svg' className='h-4 w-4' />}
                  popupClassName={isMobile ? 'custom-range-picker' : undefined}
                  onChange={(dates) => {
                    if (dates && dates[0] && dates[1]) {
                      setRangeTime([dates[0], dates[1]])
                    } else {
                      setRangeTime(defaultRange)
                    }
                  }}
                />
              </div>
            )}
            {(activePerson === TYPE_PERSON['Sinh viên'] || activePerson === TYPE_PERSON['Giảng viên']) && (
              <div className='sm:w-1/2 col-start-1 col-end-6'>
                <p>{`Chọn ${activePerson === TYPE_PERSON['Sinh viên'] ? 'khóa' : 'lớp'}`}</p>
                <div>
                  <Select
                    size='large'
                    className='w-full border-neutral-7'
                    placeholder='Chọn khóa'
                    defaultValue=''
                    loading={loading}
                    options={[
                      { label: 'Tất cả', value: '' },
                      ...listGroups.map((item) => ({
                        label: item.groupName,
                        value: item.uuidGroup
                      }))
                    ]}
                    onChange={(value) => setParams((prev) => ({ ...prev, uuidGroups: value, page: 1, maxSize: 50 }))}
                  />
                </div>
              </div>
            )}
            <div
              className={`sm:w-1/2  ${activePerson === TYPE_PERSON['Nhân viên chức năng'] ? 'col-start-1 col-end-12' : 'col-start-6 col-end-12'}`}
            >
              <p>Trạng thái</p>
              <Select
                size='large'
                className='w-full border-neutral-7'
                placeholder='Chọn trạng thái'
                defaultValue={1}
                value={params.status}
                options={[
                  { label: 'Tất cả', value: -1 }, // Thêm option "Tất cả"
                  ...(activePerson === TYPE_PERSON['Sinh viên']
                    ? statusStudent
                    : activePerson === TYPE_PERSON['Phê duyệt ảnh']
                      ? statusImage
                      : (statusOther ?? []))
                ]}
                onChange={(value) => setParams((prev) => ({ ...prev, status: value, page: 1, maxSize: 50 }))}
              />
            </div>
            <div className='w-full col-start-1 col-end-11'>
              <p>Tìm kiếm</p>
              <Input
                size='large'
                className='w-full border-neutral-7'
                placeholder='Nhập tìm kiếm'
                value={keySearch}
                onChange={(e) => setKeySearch(e.target.value)}
                onPressEnter={() => setParams((prev) => ({ ...prev, keySearch, page: 1 }))}
                suffix={
                  (isMobile || isTablet) && (
                    <Button
                      size='small'
                      type='primary'
                      icon={<Svg src='/assets/icons/common/search.svg' className='h-4 w-4 mt-1' />}
                      onClick={() => setParams((prev) => ({ ...prev, keySearch, page: 1 }))}
                    />
                  )
                }
              />
            </div>
            {!isMobile && !isTablet && (
              <Button
                size='large'
                type='primary'
                icon={<Svg src='/assets/icons/common/search.svg' className='h-5 w-5 mt-1' />}
                onClick={() => setParams((prev) => ({ ...prev, keySearch, page: 1 }))}
              >
                <span className='hidden xxl:block'>Tìm kiếm</span>
              </Button>
            )}

            {selectedRowImages.length > 0 && activePerson === TYPE_PERSON['Phê duyệt ảnh'] && (
              <>
                <Button
                  size='large'
                  style={{ width: 140 }}
                  icon={<CloseOutlined />}
                  color='primary'
                  variant='outlined'
                  onClick={() => approveMultipleImage(ImageStatus['Từ chối'])}
                >
                  Từ chối
                </Button>
                <Button
                  size='large'
                  style={{ width: 140 }}
                  icon={<Svg src='/assets/icons/common/tick-circle.svg' className='h-5 w-5 mt-1' />}
                  color='primary'
                  variant='outlined'
                  onClick={() => approveMultipleImage(ImageStatus['Đã duyệt'])}
                >
                  Duyệt
                </Button>
              </>
            )}
            {activePerson !== TYPE_PERSON['Phê duyệt ảnh'] && (
              <div className='flex gap-3'>
                {isFFA(FF_DEV_0026) && selectedRows.length > 0 && (
                  <Button
                    size='large'
                    icon={<Svg src='/assets/icons/common/trash.svg' className='h-5 w-5 mt-1' />}
                    color='primary'
                    variant='outlined'
                    onClick={() => setOpenDeleteModal(true)}
                  >
                    <span className='hidden 3xl:block'>Xóa</span>
                  </Button>
                )}
                <Dropdown menu={{ items }} trigger={['click']} placement='bottomRight' className='3xl:hidden block'>
                  <Button
                    size='large'
                    style={{ width: 40 }}
                    className='min-w-10'
                    color='primary'
                    variant='outlined'
                    icon={<MoreOutlined />}
                  />
                </Dropdown>
                <Button
                  size='large'
                  style={{ width: 140 }}
                  icon={<Svg src='/assets/icons/common/export-excel.svg' className='h-5 w-5 mt-1' />}
                  color='primary'
                  variant='outlined'
                  className='hidden 3xl:flex'
                  onClick={handleExport}
                >
                  Xuất excel
                </Button>
                {isFFA(FF_DEV_0024) && (
                  <>
                    <Button
                      size='large'
                      style={{ width: 200 }}
                      icon={<Svg src='/assets/icons/common/import-user.svg' className='h-5 w-5 mt-1' />}
                      color='primary'
                      variant='outlined'
                      className='hidden 3xl:flex'
                      onClick={() => {
                        setOpenImportModal(true)
                        setImportUser(true)
                      }}
                    >
                      Import người dùng
                    </Button>
                    <Button
                      size='large'
                      style={{ width: 200 }}
                      icon={<Svg src='/assets/icons/common/import-status.svg' className='h-5 w-5 mt-1' />}
                      color='primary'
                      variant='outlined'
                      className='hidden 3xl:flex'
                      onClick={() => {
                        setOpenImportModal(true)
                      }}
                    >
                      Import trạng thái
                    </Button>
                    <Button
                      size='large'
                      style={{ width: 140 }}
                      icon={<PlusOutlined />}
                      color='primary'
                      variant='outlined'
                      className='hidden sm:flex'
                      onClick={() => navigate('/manage-user/add')}
                    >
                      Thêm mới
                    </Button>
                  </>
                )}
              </div>
            )}
          </div>
          {viewTable()}
          {activePerson === TYPE_PERSON['Phê duyệt ảnh']
            ? listImages?.data &&
              listImages.data.length > 0 && (
                <div className='my-4 flex max-sm:flex-col items-end max-sm:items-center justify-between'>
                  <div className='flex items-center gap-2'>
                    <p className='text-xs font-normal text-gray-400'>Số bản ghi mỗi trang </p>
                    <Select
                      size='small'
                      options={pageSize}
                      value={params.maxSize}
                      onChange={(e) => setParams({ ...params, maxSize: e })}
                    />
                    <p className='text-xs font-normal text-gray-400 hidden sm:block'>
                      trên tổng {listImages?.totalElement} dữ liệu
                    </p>
                  </div>

                  <Pagination
                    onChange={(page) => {
                      setParams({ ...params, page })
                    }}
                    current={listImages?.page}
                    showSizeChanger={false}
                    pageSize={listImages?.maxSize}
                    defaultPageSize={10}
                    total={listImages?.totalElement}
                  />
                </div>
              )
            : dataSource?.data &&
              dataSource?.data.length > 0 && (
                <div className='mt-4 flex max-sm:flex-col items-end max-sm:items-center justify-between'>
                  <div className='flex items-center gap-2'>
                    <p className='text-xs font-normal text-gray-400'>Số bản ghi mỗi trang </p>
                    <Select
                      size='small'
                      options={pageSize}
                      value={params.maxSize}
                      onChange={(e) => setParams({ ...params, maxSize: e })}
                    />
                    <p className='text-xs font-normal text-gray-400 hidden sm:block'>
                      trên tổng {dataSource.totalElement} dữ liệu
                    </p>
                  </div>

                  <Pagination
                    onChange={(page) => {
                      setParams({ ...params, page })
                    }}
                    current={dataSource?.page}
                    showSizeChanger={false}
                    pageSize={dataSource.maxSize}
                    defaultPageSize={10}
                    total={dataSource.totalElement}
                  />
                </div>
              )}
        </Spin>
      </LayoutCotent>
    </>
  )
}

export default ManageUser
