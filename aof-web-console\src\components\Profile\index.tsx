import { DownOutlined } from '@ant-design/icons'
import { Avatar, Dropdown, MenuProps } from 'antd'
import Svg from '../Svg'
import useAuthStore, { resetAllStores } from '@/stores/auth'
import ChangePasswordModal from '../Modals/ChangePasswordModal'
import { useState } from 'react'
import LicenseModal from '../Modals/LicenseModal'
import { isAdmin } from '@/utils/feature-flags'
import { TYPE_PERSON } from '@/types/components'
import { getConfig } from '@/utils/config'
import { revokeToken } from '@/services/auth'

export default function Profile() {
  const { user, id_token, accessTokenSSO } = useAuthStore()
  const [openChangePasswordModal, setOpenChangePasswordModal] = useState(false)
  const [openLicenseModal, setOpenLicenseModal] = useState(false)

  const handleSSOLogout = () => {
    const ssoURL = new URL(getConfig('SSO_SERVER') + `/oidc/logout`)
    ssoURL.searchParams.append('id_token_hint', id_token)
    ssoURL.searchParams.append('post_logout_redirect_uri', `${window.location.origin}/sso/identifier-logout`)
    window.location.replace(ssoURL.toString())
  }

  const isValidString = (s: any) => typeof s === 'string' && s.trim() !== ''

  const handleLogout = async () => {
    if (isValidString(id_token) && isValidString(accessTokenSSO)) {
      try {
        await revokeToken(accessTokenSSO, 'access_token')
      } catch {
        /* empty */
      }
      handleSSOLogout()
      return
    }

    resetAllStores()
  }

  const items: MenuProps['items'] = [
    ...(isAdmin(user)
      ? [
          {
            key: '3',
            label: 'Quản lý engine',
            icon: <Svg src='/assets/icons/menu/license.svg' className='h-5 w-5' />,
            onClick: () => setOpenLicenseModal(true)
          }
        ]
      : []),
    ...(isAdmin(user) || user.personType === TYPE_PERSON['Sinh viên']
      ? [
          {
            key: '1',
            label: 'Đổi mật khẩu',
            icon: <Svg src='/assets/icons/menu/changepwd.svg' className='h-5 w-5' />,
            onClick: () => setOpenChangePasswordModal(true)
          }
        ]
      : []),
    {
      key: '2',
      label: 'Đăng xuất',
      icon: <Svg src='/assets/icons/menu/logout.svg' className='h-5 w-5' />,
      onClick: handleLogout
    }
  ]

  return (
    <>
      <ChangePasswordModal
        isModalOpen={openChangePasswordModal}
        handleCancel={() => setOpenChangePasswordModal(false)}
      />
      <LicenseModal isModalOpen={openLicenseModal} handleCancel={() => setOpenLicenseModal(false)} />
      <div className='flex items-center'>
        <Dropdown menu={{ items }} trigger={['click']} placement='bottomLeft'>
          <div className='cursor-pointer flex items-center gap-3'>
            <Avatar src={<Svg src='/assets/icons/common/avatar.svg' />} className='h-8 w-8' />
            <div className='hidden md:block'>
              <p className='ml-2 font-medium text-base truncate'>{user.fullName}</p>
              <p className='ml-2 font-normal text-xs text-text-2 capitalize'>{user.roles?.[0]?.name}</p>
            </div>
            <DownOutlined className='hidden md:block' />
          </div>
        </Dropdown>
      </div>
    </>
  )
}
