import { DatePicker, Pagination, Select, Spin, Table, TableProps } from 'antd'
import Svg from '../Svg'
import useLayoutStore from '@/stores/layout'
import { useEffect, useState } from 'react'
import dayjs, { Dayjs } from 'dayjs'
import { ResponseList } from '@/types/common'
import { initialProperty, pageSize } from '@/types/components'
import { RequestResponseLog } from '@/types/log'
import { MESSAGE_STATUS } from '@/constants'
import { showCustomNotification } from '@/common/Notification'
import { getSystemLogs } from '@/services/log'

const LogTable = () => {
  const { isMobile, isTablet } = useLayoutStore()
  const [pickedDates, setPickedDates] = useState<[Dayjs | null, Dayjs | null]>([dayjs().startOf('month'), dayjs()])
  const [dataSource, setDataSource] = useState<ResponseList<RequestResponseLog>>()
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [pageProperty, setPageProperty] = useState<{
    page: number
    maxSize: number
    totalElement: number
  }>(initialProperty)

  const getData = async () => {
    try {
      setIsLoading(true)
      const res = await getSystemLogs({
        ...pageProperty,
        startDate: dayjs(pickedDates[0]).format('DD/MM/YYYY'),
        endDate: dayjs(pickedDates[1]).format('DD/MM/YYYY')
      })
      if (res.message === MESSAGE_STATUS.SUCCESS) {
        setDataSource(res.object)
      }
    } catch {
      showCustomNotification({
        status: 'error',
        message: 'Thất bại',
        description: 'Lỗi khi lấy danh sách!'
      })
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    getData()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pageProperty, pickedDates])

  const columns: TableProps<RequestResponseLog>['columns'] = [
    {
      title: 'STT',
      dataIndex: 'key',
      key: 'key',
      align: 'center',
      width: 48,
      render: (_, __, index) => (dataSource ? (dataSource.page - 1) * dataSource.maxSize + (index + 1) : '')
    },
    {
      title: 'Thời gian',
      dataIndex: 'loggedAt',
      key: 'loggedAt'
    },
    {
      title: 'Tài khoản',
      dataIndex: 'username',
      key: 'username'
    },
    {
      title: 'Địa chỉ IP',
      dataIndex: 'ipAddress',
      key: 'ipAddress'
    },
    {
      title: 'Đường dẫn',
      dataIndex: 'path',
      key: 'path'
    },
    {
      title: 'Phương thức truy cập',
      dataIndex: 'method',
      key: 'method'
    },
    {
      title: 'Mã lỗi',
      dataIndex: 'statusCode',
      key: 'statusCode'
    }
  ]

  return (
    <>
      <Spin spinning={isLoading}>
        <div className='flex justify-between items-end gap-5 mb-5'>
          <div className='flex flex-col'>
            <label className='mb-0 min-w-[220px] w-full'>Chọn thời gian</label>
            <DatePicker.RangePicker
              size='large'
              className='border-neutral-7'
              value={pickedDates}
              format={'DD/MM/YYYY'}
              popupClassName={isMobile ? 'custom-range-picker' : undefined}
              suffixIcon={<Svg src='/assets/icons/common/datepicker.svg' className='h-4 w-4' />}
              onChange={(dates) => setPickedDates(dates || [dayjs().startOf('month'), dayjs()])}
            />
          </div>
        </div>
        <Table
          columns={columns}
          dataSource={dataSource?.data}
          pagination={false}
          rowKey={(record) => record.uuidRequest}
          scroll={{ x: isMobile || isTablet ? 700 : 470, y: 470 }}
        />
        {dataSource?.data && dataSource?.data.length > 0 && (
          <div className='mt-4 flex max-sm:flex-col items-center justify-between gap-4'>
            <div className='flex items-center gap-2'>
              <p className='text-xs font-normal text-gray-400'>Số bản ghi mỗi trang </p>
              <Select
                size='small'
                options={pageSize}
                value={pageProperty.maxSize}
                onChange={(e) => setPageProperty({ ...pageProperty, maxSize: e, page: 1 })}
              />
              <p className='text-xs font-normal text-gray-400 hidden sm:block'>
                trên tổng {dataSource.totalElement} dữ liệu{' '}
              </p>
            </div>
            <Pagination
              onChange={(page) => {
                setPageProperty({ ...pageProperty, page })
              }}
              align='end'
              current={dataSource.page}
              defaultCurrent={1}
              showSizeChanger={false}
              pageSize={dataSource.maxSize}
              defaultPageSize={10}
              total={dataSource.totalElement}
            />
          </div>
        )}
      </Spin>
    </>
  )
}

export default LogTable
