import { CustomField, Organization, ParamsSearch, Role } from './common'
import { Gender, Status, TYPE_PERSON } from './components'
import { Face, Person } from './person'

export interface ParamsLogin {
  username: string
  password: string
}

export interface ResponseData {
  accessToken: string
  refreshToken: string
  expireIn: string
}

export interface UserInfo {
  uuidPerson: string
  personType: TYPE_PERSON
  personCode: string
  fullName: string
  gender: Gender
  phoneNo: string
  email: string
  faces: Face[]
  username: string
  organization: Organization
  roles: Role[]
  featureActions: FeatureAction[]
  customFields?: CustomField[]
  status: Status
  dob: string
}
export interface User {
  key: string
  numberId: string
  avatar: string
  name: string
  status: string
  email?: string
  timestamp?: number
  course?: string
}

export interface Lecturer {
  uuidPerson: string
  personCode: string
  fullName: string
  username: string
}

export interface Feature {
  uuidFeature: string
  featureCode: string
  featureName: string
  uuidParentFeature: string
  featureActions: FeatureAction[]
}

export interface FeatureAction {
  uuidFeatureAction: string
  actionCode: string
  actionName: string
  uuidFeature: string
  shortName: string
}

export interface PermissionGroupParams extends ParamsSearch {
  uuidPerson?: string
}

export interface PermissionGroup {
  uuidPermissionGroup: string
  groupCode: string
  groupName: string
  allowModification: number
  featureActions?: FeatureAction[]
  description?: string
}

export interface PermissionGroupFeatureAction {
  uuidPermissionGroup: string
  uuidFeatureAction: string
}

export interface PersonPermissionGroup {
  uuidPerson: string
  uuidPermissionGroup: string
}

export interface PersonPermissionGroupData extends PersonPermissionGroup {
  person: Person
}

export interface PersonsPermissionGroupParams extends ParamsSearch {
  uuidPermissionGroup?: string
}

export interface PermissionGroupResource {
  uuidPermissionGroup: string
  uuidPermissionGroupResource?: string
  resourceType: number
  scopeType: number
  uuidResources?: string[]
}

export interface PermissionClassParams {
  scopeType: number
  uuidResources: string[]
}

export interface ResourcePermission {
  resourceType: number
  scopeType: number
  resources: Resource[]
}
export interface Resource {
  uuidResource: string
  resourceName: string
}

export interface PermissionGroupData {
  groupName: string
  uuidFeatureActions?: string[]
  description?: string
}

export interface Lisence {
  uuidLicense?: string
  licenseValue: string
  createdAt?: string
}

export interface PasswordParams {
  uuidPerson: string
  password: string
  newPassword: string
}
