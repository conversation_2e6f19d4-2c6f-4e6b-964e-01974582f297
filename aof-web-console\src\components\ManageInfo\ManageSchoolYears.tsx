import dayjs from 'dayjs'
import { useEffect, useState } from 'react'
import { useLocation, useNavigate, useParams } from 'react-router-dom'
import { Button, DatePicker, Form, Input, Spin, Steps } from 'antd'
import { PlusOutlined } from '@ant-design/icons'
import LayoutCotent from '@/layouts/LayoutCotent'
import { showCustomNotification } from '@/common/Notification'
import Svg from '@/components/Svg'
import { MESSAGE_STATUS } from '@/constants'
import { AcademicYear, Semester } from '@/types/infomation'
import { addNewAcademicYear, getDetailsAcademicYear, updateAcademicYear } from '@/services/academicYear'
import { addNewSemester, deleteSemester, updateSemester } from '@/services/group'
import ModalError from '../Modals/ErrorLoadingModal'
import { useSegmentStore } from '@/stores/useSegmentStore'
import { InfoType } from '@/types/components'
import useLayoutStore from '@/stores/layout'

const initialSemester = {
  uuidSemester: '',
  name: '',
  startDate: '',
  endDate: ''
}

const ManageSchoolYears = () => {
  const [form] = Form.useForm()
  const navigate = useNavigate()
  const { state } = useLocation()
  const { uuidAcademicYear } = useParams()

  const { isMobile } = useLayoutStore()
  const { setActiveInfo } = useSegmentStore()

  const [semesters, setSemesters] = useState<Semester[]>([initialSemester])
  const [dataState, setDataState] = useState<AcademicYear>({
    uuidAcademicYear: '',
    name: '',
    startYear: 0,
    endYear: 0,
    description: '',
    semesters: []
  })

  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [openErrorModal, setOpenErrorModal] = useState<boolean>(false)

  useEffect(() => {
    if (uuidAcademicYear) {
      getData()
      setActiveInfo(InfoType['Quản lý năm học'])
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [uuidAcademicYear])

  const getData = async () => {
    try {
      if (!uuidAcademicYear) return

      setIsLoading(true)
      const res = await getDetailsAcademicYear(uuidAcademicYear)
      if (res.message === MESSAGE_STATUS.SUCCESS) {
        setDataState(res.object)

        form.setFieldsValue({
          name: res.object.name,
          startYear: dayjs(String(res.object.startYear), 'YYYY'),
          endYear: dayjs(String(res.object.endYear), 'YYYY'),
          description: res.object.description
        })

        if (res.object.semesters) {
          const formattedSemesters = res.object.semesters.map((semester) => ({
            ...semester,
            startDate: dayjs(String(semester.startDate), 'DD/MM/YYYY'),
            endDate: dayjs(String(semester.endDate), 'DD/MM/YYYY')
          }))
          setSemesters(formattedSemesters as [])
          form.setFieldsValue({
            ...formattedSemesters.reduce((acc: { [key: string]: any }, cur, index) => {
              acc[`name-${index}`] = cur.name
              acc[`startDate-${index}`] = cur.startDate
              acc[`endDate-${index}`] = cur.endDate
              return acc
            }, {})
          })
        }
        setIsLoading(false)
      }
    } catch {
      setOpenErrorModal(true)
    }
  }

  const handleSave = async () => {
    await form.validateFields()

    try {
      setIsLoading(true)
      const data = {
        ...dataState,
        name: `${dataState.startYear}-${dataState.endYear}`,
        semesters: semesters.map((semester) => ({
          ...semester,
          startDate: semester.startDate ? dayjs(semester.startDate).format('DD/MM/YYYY') : '',
          endDate: semester.endDate ? dayjs(semester.endDate).format('DD/MM/YYYY') : ''
        }))
      }
      const dataUpdate = {
        ...dataState,
        name: `${dataState.startYear}-${dataState.endYear}`
      }
      if (state?.update && uuidAcademicYear) {
        await updateAcademicYear(uuidAcademicYear, dataUpdate)
        const newSemestersList = semesters.filter((_, index) => index >= dataState.semesters?.length)
        const deletedSemestersList = dataState.semesters.filter(
          (semester) => !semesters.some((s) => s.uuidSemester === semester.uuidSemester)
        )
        const updatedSemestersList = semesters.filter((semester) => {
          const originalSemester = dataState.semesters.find((s) => s.uuidSemester === semester.uuidSemester)
          return (
            originalSemester &&
            (originalSemester.name !== semester.name ||
              originalSemester.startDate !== semester.startDate ||
              originalSemester.endDate !== semester.endDate)
          )
        })
        // console.log(newSemesters)
        if (newSemestersList) {
          const data = newSemestersList.map((semester) => ({
            ...semester,
            startDate: semester.startDate ? dayjs(semester.startDate).format('DD/MM/YYYY') : '',
            endDate: semester.endDate ? dayjs(semester.endDate).format('DD/MM/YYYY') : '',
            uuidAcademicYear
          }))
          for (const semester of data) {
            await addNewSemester(semester)
          }
        }
        if (deletedSemestersList) {
          for (const semester of deletedSemestersList) {
            await deleteSemester(semester.uuidSemester)
          }
        }
        if (updatedSemestersList) {
          const data = updatedSemestersList.map((semester) => ({
            ...semester,
            startDate: semester.startDate ? dayjs(semester.startDate).format('DD/MM/YYYY') : '',
            endDate: semester.endDate ? dayjs(semester.endDate).format('DD/MM/YYYY') : ''
          }))
          for (const semester of data) {
            await updateSemester(semester.uuidSemester, semester)
          }
        }
        navigate('/manage-info')
        showCustomNotification({
          status: 'success',
          message: 'Thành công',
          description: 'Cập nhật năm học thành công!'
        })
      } else {
        await addNewAcademicYear(data)
        navigate('/manage-info')
        showCustomNotification({
          status: 'success',
          message: 'Thành công',
          description: 'Thêm mới năm học thành công!'
        })
      }
    } catch {
      showCustomNotification({
        status: 'error',
        message: 'Thất bại',
        description: state?.update ? 'Cập nhật năm học thất bại!' : 'Thêm mới năm học thất bại!'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleAddSemester = () => {
    setSemesters([...semesters, initialSemester])
  }

  const handleRemoveSemester = (index: number) => {
    setSemesters((prevSemesters) => prevSemesters.filter((_, i) => i !== index))
  }

  const handleSemesterChange = (index: number, field: string, value: any) => {
    const updatedSemesters = [...semesters]
    updatedSemesters[index] = { ...updatedSemesters[index], [field]: value }
    setSemesters(updatedSemesters)
  }

  const handleStartYearChange = (date: { year: () => any }) => {
    if (date) {
      const newStartYear = date.year()
      setDataState((prev) => ({
        ...prev,
        startYear: newStartYear,
        endYear: newStartYear + 1
      }))
    } else {
      setDataState((prev) => ({ ...prev, startYear: 0, endYear: 0 }))
    }
  }

  return (
    <>
      <ModalError
        handleCancel={() => {
          setOpenErrorModal(false)
          navigate('/manage-info')
        }}
        title='Thất bại!'
        subTitle='Lấy thông tin năm học thất'
        isModalOpen={openErrorModal}
      />
      <LayoutCotent
        title={[
          'Quản lý năm học',
          !uuidAcademicYear ? 'Thêm mới năm học' : `${state?.update ? 'Sửa năm học' : 'Xem chi tiết'}`
        ]}
        btnBack={true}
      >
        <Spin spinning={isLoading}>
          <Form form={form} layout='vertical' requiredMark={false} className={`${isMobile && 'px-3 mt-2'}`}>
            <Steps
              progressDot
              current={1}
              direction='vertical'
              items={[
                {
                  title: <h2 className='font-semibold text-base'>Thời gian</h2>,
                  description: (
                    <div className='flex xl:w-2/3 gap-2'>
                      <Form.Item
                        name='startYear'
                        className='w-full'
                        label={
                          <p>
                            Từ năm <span className='text-error'>*</span>
                          </p>
                        }
                        rules={[
                          {
                            required: true,
                            message: 'Vui lòng chọn năm bắt đầu!'
                          }
                        ]}
                      >
                        <DatePicker
                          picker='year'
                          size='large'
                          className='w-full'
                          suffixIcon={
                            <Svg src='/assets/icons/common/datepicker.svg' className='h-5 w-5 cursor-pointer' />
                          }
                          disabled={!!uuidAcademicYear && !state?.update}
                          onChange={handleStartYearChange}
                        />
                      </Form.Item>
                      <span className='mt-7'>-</span>
                      <div className='w-full'>
                        <p className='text-neutral-1'>
                          Đến năm <span className='text-error'>*</span>
                        </p>
                        <DatePicker
                          picker='year'
                          size='large'
                          className='w-full'
                          suffixIcon={
                            <Svg src='/assets/icons/common/datepicker.svg' className='h-5 w-5 cursor-pointer' />
                          }
                          value={dataState.endYear ? dayjs().year(dataState.endYear) : null}
                          disabled
                        />
                      </div>
                    </div>
                  )
                },
                {
                  title: <h2 className='font-semibold text-base'>Kỳ học</h2>,
                  description: (
                    <div className='xl:w-2/3 mt-3'>
                      <div className='bg-background-card p-1 rounded-lg'>
                        <h1 className='font-medium text-base px-6 py-3'>Danh sách các kỳ</h1>
                        {semesters.map((semester, index) => (
                          <div
                            key={index}
                            className={`bg-background-1 gird ${!isMobile && 'flex'}  gap-2 p-4 mb-4 rounded-lg`}
                          >
                            <div className={`${isMobile && 'flex gap-2'} w-full`}>
                              <Form.Item
                                name={`name-${index}`}
                                className='mb-2 xs:mb-0 w-full'
                                label={
                                  <p>
                                    Tên kỳ <span className='text-error'>*</span>
                                  </p>
                                }
                                rules={[
                                  {
                                    required: true,
                                    message: 'Vui lòng nhập tên kỳ!'
                                  }
                                ]}
                              >
                                <Input
                                  placeholder='Nhập tên kỳ'
                                  size='large'
                                  allowClear
                                  disabled={!!uuidAcademicYear && !state?.update}
                                  value={semester.name}
                                  onChange={(e) => handleSemesterChange(index, 'name', e.target.value)}
                                />
                              </Form.Item>
                              {(!uuidAcademicYear || state?.update) && isMobile && index > 0 && (
                                <Button
                                  size='large'
                                  className='mt-[22px]'
                                  icon={<Svg src='/assets/icons/common/trash.svg' className='h-5 w-5 mt-1' />}
                                  color='red'
                                  variant='outlined'
                                  onClick={() => handleRemoveSemester(index)}
                                />
                              )}
                            </div>
                            <Form.Item
                              name={`startDate-${index}`}
                              className='mb-2 xs:mb-0 w-full'
                              label={
                                <p>
                                  Thời gian từ <span className='text-error'>*</span>
                                </p>
                              }
                              rules={[
                                {
                                  required: true,
                                  message: 'Vui lòng chọn thời gian bắt đầu!'
                                }
                              ]}
                            >
                              <DatePicker
                                size='large'
                                format='DD/MM/YYYY'
                                className='w-full'
                                disabled={!!uuidAcademicYear && !state?.update}
                                placeholder='Thời gian bắt đầu'
                                value={semester.startDate}
                                disabledDate={(d) => d > dayjs(semester.endDate)}
                                onChange={(date) => handleSemesterChange(index, 'startDate', date)}
                                suffixIcon={
                                  <Svg src='/assets/icons/common/datepicker.svg' className='h-5 w-5 cursor-pointer' />
                                }
                              />
                            </Form.Item>
                            {!isMobile && <span className='mt-8'>-</span>}
                            <Form.Item
                              name={`endDate-${index}`}
                              className='mb-2 xs:mb-0 w-full'
                              label={
                                <p>
                                  Đến <span className='text-error'>*</span>
                                </p>
                              }
                              rules={[
                                {
                                  required: true,
                                  message: 'Vui lòng chọn thời gian kết thúc!'
                                }
                              ]}
                            >
                              <DatePicker
                                size='large'
                                format='DD/MM/YYYY'
                                className='w-full'
                                disabled={!!uuidAcademicYear && !state?.update}
                                disabledDate={(d) => d <= dayjs(semester.startDate)}
                                placeholder='Thời gian kết thúc'
                                value={semester.endDate}
                                onChange={(date) => handleSemesterChange(index, 'endDate', date)}
                                suffixIcon={
                                  <Svg src='/assets/icons/common/datepicker.svg' className='h-5 w-5 cursor-pointer' />
                                }
                              />
                            </Form.Item>
                            {(!uuidAcademicYear || state?.update) && !isMobile && index > 0 && (
                              <Button
                                size='large'
                                style={{ width: 140 }}
                                className='mt-[22px]'
                                icon={<Svg src='/assets/icons/common/trash.svg' className='h-5 w-5 mt-1' />}
                                color='red'
                                variant='outlined'
                                onClick={() => handleRemoveSemester(index)}
                              />
                            )}
                          </div>
                        ))}
                        {(!uuidAcademicYear || state?.update) && (
                          <Button
                            color='primary'
                            variant='outlined'
                            className='border-none !bg-inherit text-text-primary'
                            size='large'
                            icon={<PlusOutlined />}
                            onClick={handleAddSemester}
                          >
                            Thêm kỳ
                          </Button>
                        )}
                      </div>
                      {(!uuidAcademicYear || state?.update) && (
                        <div className='flex items-center justify-center mt-5 gap-5'>
                          <Button
                            variant='outlined'
                            color='primary'
                            size='large'
                            className='max-sm:!w-1/2'
                            style={{ width: 126 }}
                            onClick={() => navigate('/manage-info')}
                          >
                            Hủy
                          </Button>
                          <Button
                            type='primary'
                            size='large'
                            className='max-sm:!w-1/2'
                            style={{ width: 126 }}
                            onClick={() => {
                              handleSave()
                            }}
                          >
                            Lưu
                          </Button>
                        </div>
                      )}
                    </div>
                  )
                }
              ]}
            />
          </Form>
        </Spin>
      </LayoutCotent>
    </>
  )
}

export default ManageSchoolYears
