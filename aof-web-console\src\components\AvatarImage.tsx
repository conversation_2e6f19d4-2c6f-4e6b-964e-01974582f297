import React, { Suspense, lazy } from 'react'
import Svg from './Svg'
import { Person } from '@/types/person'

const LazyAvatarImage = lazy(() => Promise.resolve({ default: AvatarImage }))

const AvatarImage = ({ objectUrl }: { objectUrl: string }) => {
  if (!objectUrl) return null

  return (
    <img
      src={objectUrl}
      alt='avatar'
      className='rounded-full object-cover border'
      style={{ width: 40, height: 40 }}
      loading='lazy'
    />
  )
}

const Avatar = ({ person }: { person: Person }) => {
  return (
    <div className='flex justify-center'>
      <Suspense fallback={<Svg src='/assets/icons/common/image-avatar.svg' className='w-10 h-10' />}>
        <LazyAvatarImage objectUrl={person.faces?.[0]?.objectUrl || ''} />
      </Suspense>
    </div>
  )
}

export default Avatar
