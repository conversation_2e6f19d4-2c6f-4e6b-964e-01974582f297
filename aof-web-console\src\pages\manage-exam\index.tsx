import { showCustomNotification } from '@/common/Notification'
import ModalViolationInfo from '@/components/Modals/ViolationInfoModal'
import Svg from '@/components/Svg'
import ViolationCard from '@/components/ViolationCard'
import ViolationInfo from '@/components/ViolationInfo'
import { MESSAGE_STATUS } from '@/constants'
import LayoutCotent from '@/layouts/LayoutCotent'
import { getListExamCheats } from '@/services/person'
import { getListRooms } from '@/services/room_device'
import useLayoutStore from '@/stores/layout'
import { ResponseList } from '@/types/common'
import { initialProperty, pageSize, statusExam } from '@/types/components'
import { ExamCheating } from '@/types/person'
import { Location } from '@/types/room_device'
import { Button, Col, DatePicker, Empty, Pagination, Row, Select, Spin } from 'antd'
import dayjs, { Dayjs } from 'dayjs'
import { useEffect, useState } from 'react'

const { RangePicker } = DatePicker

const initial = {
  ...initialProperty,
  cheatingStatuses: '',
  uuidLocations: ''
}
const ManagExamination = () => {
  const { isMobile, isTablet } = useLayoutStore()
  const [params, setParams] = useState(initial)

  const [dataSource, setDataSource] = useState<ResponseList<ExamCheating>>()
  const [listRooms, setListRooms] = useState<Location[]>([])

  const [selectedCard, setSelectedCard] = useState<ExamCheating>()
  const defaultRange: [Dayjs, Dayjs] = [dayjs().startOf('month'), dayjs()]
  const [rangeTime, setRangeTime] = useState<[Dayjs, Dayjs]>(defaultRange)

  const [isLoadingRooms, setIsLoadingRooms] = useState<boolean>(false)
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [openModal, setOpenModal] = useState<boolean>(false)

  const getData = async () => {
    try {
      setIsLoading(true)
      const res = await getListExamCheats({
        ...params,
        startTime: dayjs(rangeTime[0]).format('DD/MM/YYYY HH:mm:ss'),
        endTime: dayjs(rangeTime[1]).format('DD/MM/YYYY HH:mm:ss')
      })
      if (res.message === MESSAGE_STATUS.SUCCESS) {
        setDataSource(res.object)
      }
    } catch {
      showCustomNotification({
        status: 'error',
        message: 'Thất bại!',
        description: 'Có lỗi xảy ra!'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const getRooms = async () => {
    try {
      setIsLoadingRooms(true)
      const res = await getListRooms({
        page: 1,
        maxSize: 999
      })
      if (res.message === MESSAGE_STATUS.SUCCESS) {
        const { data } = res.object
        setListRooms(data)
      }
    } catch {
      showCustomNotification({
        status: 'error',
        message: 'Thất bại',
        description: 'Lấy dữ liệu thất bại!'
      })
    } finally {
      setIsLoadingRooms(false)
    }
  }

  useEffect(() => {
    getData()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [params.page, params.maxSize])

  useEffect(() => {
    getRooms()
  }, [])

  return (
    <LayoutCotent title={['Quản lý gian lận phòng thi']}>
      <Spin spinning={isLoading}>
        {(isMobile || isTablet) && selectedCard && (
          <ModalViolationInfo
            isModalOpen={openModal}
            handleCancel={() => setOpenModal(false)}
            selectedCard={selectedCard}
            setSelectedCard={setSelectedCard}
            refreshData={getData}
          />
        )}
        <div className='grid grid-cols-10 sm:flex gap-1 xs:gap-3 items-end mb-5'>
          <div className='sm:w-full col-start-1 col-end-6 max-sm:col-span-full max-sm:pb-2'>
            <p>Chọn thời gian</p>
            <RangePicker
              size='large'
              className='w-full border-neutral-7'
              value={rangeTime}
              suffixIcon={<Svg src='/assets/icons/common/datepicker.svg' className='h-4 w-4' />}
              format='DD/MM/YYYY HH:mm:ss'
              showTime
              popupClassName={isMobile ? 'custom-range-picker' : undefined}
              onChange={(dates) => {
                if (dates && dates[0] && dates[1]) {
                  setRangeTime([dates[0], dates[1]])
                } else {
                  setRangeTime(defaultRange)
                }
              }}
            />
          </div>
          <div className='sm:w-1/2 col-start-1 col-end-6 max-sm:col-span-5 max-sm:pb-2'>
            <p>Phòng thi</p>
            <div>
              <Select
                loading={isLoadingRooms}
                size='large'
                allowClear
                className='w-full border-neutral-7'
                placeholder='Chọn phòng thi'
                onChange={(e) => setParams({ ...params, uuidLocations: String(e), page: 1, maxSize: 50 })}
                options={listRooms.map((room) => ({
                  label: room.name,
                  value: room.uuidLocation
                }))}
              />
            </div>
          </div>
          <div className='sm:w-1/2 col-start-1 col-end-6 max-sm:col-span-5 max-sm:pb-2'>
            <p>Trạng thái</p>
            <div>
              <Select
                size='large'
                className='w-full border-neutral-7'
                placeholder='Chọn trạng thái'
                defaultValue=''
                onChange={(e) => setParams({ ...params, cheatingStatuses: String(e), page: 1, maxSize: 50 })}
                options={statusExam}
              />
            </div>
          </div>
          <Button
            size='large'
            // style={{ width: 140 }}
            className='col-span-full content-center'
            type='primary'
            icon={<Svg src='/assets/icons/common/search.svg' className='h-5 w-5 mt-1' />}
            onClick={getData}
          >
            Tìm kiếm
          </Button>
        </div>
        <div className='flex flex-1'>
          <div className='w-full'>
            <h1 className='mb-2 text-lg font-semibold'>Danh sách vi phạm</h1>
            <Row
              gutter={[24, 20]}
              className='overflow-y-auto h-[calc(100vh-20px)] scrollbar'
              style={{ scrollbarWidth: 'thin' }}
            >
              {(!dataSource || dataSource.data.length === 0) && (
                <Col span={24} className='!w-full flex justify-center items-center'>
                  <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                </Col>
              )}
              {dataSource?.data &&
                dataSource?.data.map((data, index) => (
                  <Col
                    className='cursor-pointer hover:shadow-md transition'
                    span={isMobile || isTablet || selectedCard ? 24 : 12}
                    key={index}
                    onClick={() => {
                      setSelectedCard(data)
                      if (isMobile || isTablet) setOpenModal(true)
                    }}
                  >
                    <ViolationCard
                      status={true}
                      data={data}
                      refreshData={getData}
                      isSelected={selectedCard?.uuidExamCheating === data.uuidExamCheating}
                      setSelectedCard={setSelectedCard}
                    />
                  </Col>
                ))}
            </Row>
            {dataSource?.data && dataSource?.data.length > 0 && (
              <div className='mt-4 flex max-sm:flex-col items-center justify-between gap-4'>
                <div className='flex items-center gap-2'>
                  <p className='text-xs font-normal text-gray-400'>Số bản ghi mỗi trang </p>
                  <Select
                    size='small'
                    options={pageSize}
                    value={params.maxSize}
                    onChange={(e) => setParams({ ...params, maxSize: e })}
                  />
                  <p className='text-xs font-normal text-gray-400 hidden sm:block'>
                    trên tổng {dataSource.totalElement} dữ liệu{' '}
                  </p>
                </div>

                <Pagination
                  onChange={(page) => {
                    setParams({ ...params, page })
                  }}
                  align='end'
                  current={dataSource.page}
                  defaultCurrent={1}
                  showSizeChanger={false}
                  pageSize={dataSource.maxSize}
                  defaultPageSize={10}
                  total={dataSource.totalElement}
                />
              </div>
            )}
          </div>
          {selectedCard && !isMobile && !isTablet && (
            <Col span={13} className='pl-5 transition-all'>
              <ViolationInfo selectedCard={selectedCard} setSelectedCard={setSelectedCard} refreshData={getData} />
            </Col>
          )}
        </div>
      </Spin>
    </LayoutCotent>
  )
}

export default ManagExamination
