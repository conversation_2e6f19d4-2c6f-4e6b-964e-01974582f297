import { ApiResponse, ResponseList } from '@/types/common'
import {
  AttendanceRecord,
  Session,
  Attendance,
  SessionParamsSearch,
  RecordParams,
  SessionParams,
  SessionAttendanceParams,
  SessionAttendance,
  AttendanceRecordParams
} from '@/types/session'
import axiosInstance from '@/utils/axios'

//2.38. Thêm mới session.
export async function addSession(params: SessionParams): Promise<ApiResponse<any>> {
  const response = await axiosInstance.post('/web-service/v1/sessions', params)
  return response.data
}

//2.39. Danh sách session.
export async function getListSessions(params: SessionParamsSearch): Promise<ApiResponse<ResponseList<Session>>> {
  const response = await axiosInstance.get('/web-service/v1/sessions', { params })
  return response.data
}

//2.40. Danh sách session attendance.
export async function getListSessionAttendances(uuidSession: string): Promise<ApiResponse<Attendance[]>> {
  const response = await axiosInstance.get('/web-service/v1/session-attendances', { params: { uuidSession } })
  return response.data
}

//2.41. Thêm mới session attendance.
export async function addSessionAttendance(params: SessionAttendanceParams): Promise<ApiResponse<any>> {
  const response = await axiosInstance.post('/web-service/v1/session-attendances', params)
  return response.data
}

//2.42. Chỉnh sửa session attendance.
export async function updateSessionAttendance(
  uuidSessionAttendance: string,
  params: SessionAttendance
): Promise<ApiResponse<any>> {
  const response = await axiosInstance.put(`/web-service/v1/session-attendances/${uuidSessionAttendance}`, params)
  return response.data
}

//2.43. Danh sách attendance record.
export async function getListAttendanceRecords(
  params: RecordParams
): Promise<ApiResponse<ResponseList<AttendanceRecord>>> {
  const response = await axiosInstance.get('/web-service/v1/attendance-records', { params })
  return response.data
}

//2.44. Thêm mới nhiều attendance record
export async function addMultipleAttendanceRecord(
  attendanceRecords: AttendanceRecordParams[]
): Promise<ApiResponse<any>> {
  const response = await axiosInstance.post('/web-service/v1/attendance-records/multiple', {
    attendanceRecords
  })
  return response.data
}

//2.45. Chỉnh sửa nhiều attendance record
export async function updateMultipleAttendanceRecord(
  attendanceRecords: AttendanceRecordParams[]
): Promise<ApiResponse<any>> {
  const response = await axiosInstance.put('/web-service/v1/attendance-records/multiple', {
    attendanceRecords
  })
  return response.data
}

//2.46. Danh sách preview attendance record.
export async function getPreviewAttendanceRecord(
  params: SessionParamsSearch
): Promise<ApiResponse<ResponseList<AttendanceRecord>>> {
  const response = await axiosInstance.get('/web-service/v1/attendance-records/preview', { params })
  return response.data
}
