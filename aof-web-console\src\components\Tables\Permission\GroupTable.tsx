import { MoreOutlined, PlusOutlined } from '@ant-design/icons'
import { Button, Dropdown, MenuProps, Spin, Table, TableProps } from 'antd'
import { useEffect, useState } from 'react'
import Svg from '../../Svg'
import { MESSAGE_STATUS } from '@/constants'
import { showCustomNotification } from '@/common/Notification'
import { useNavigate } from 'react-router-dom'
import ModalDelete from '../../Modals/DeleteModal'
import useLayoutStore from '@/stores/layout'
import { PermissionGroup } from '@/types/user'
import { useDeleteItem } from '@/hooks/useDeleteItem'
import PermissionGroupModal from '../../Modals/PermissionGroupModal'
import { deletePermissionGroup, getListPermissionGroup, updatePermissionGroup } from '@/services/auth'
import { ResponseList } from '@/types/common'
import useAuthStore from '@/stores/auth'
import { checkFeature, FF_DEV_0022 } from '@/utils/feature-flags'

export interface InfoTableProps {
  page: number
  maxSize: number
  uuidP?: string
  dataSource?: ResponseList<any>
  setIsLoading: (value: boolean) => void
  setDataSource: (value: ResponseList<any>) => void
}

const PermissionGroupTable = ({ page, maxSize, uuidP, dataSource, setIsLoading, setDataSource }: InfoTableProps) => {
  const navigate = useNavigate()
  const { isMobile, isTablet } = useLayoutStore()
  const { user, featFlagsAction } = useAuthStore()
  const isFFA = (code: string) => checkFeature(code, user, featFlagsAction)

  const [uuid, setUuid] = useState<string>('')
  const [permissrionData, setPermissionData] = useState({ groupName: '', description: '' })
  const [isUpdate, setIsUpdate] = useState<boolean>(false)
  const [openPermissionModal, setOpenPermissionModal] = useState<boolean>(false)

  const { loading, openDeleteModal, setOpenDeleteModal, handleDelete } = useDeleteItem(
    deletePermissionGroup,
    `Xóa nhóm quyền thành công!`,
    `Xóa nhóm quyền thất bại!`
  )

  useEffect(() => {
    getData()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [page, maxSize])

  const getData = async () => {
    try {
      setIsLoading(true)
      const res = await getListPermissionGroup({ page, maxSize, uuidPerson: uuidP })
      if (res.message === MESSAGE_STATUS.SUCCESS) {
        setDataSource(res.object)
      }
    } catch {
      showCustomNotification({ status: 'error', message: 'Thất bại', description: 'Có lỗi xảy ra!' })
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    if (!isUpdate) setPermissionData({ groupName: '', description: '' })
  }, [isUpdate])

  const handleOk = async (data: any) => {
    if (isUpdate) {
      try {
        const res = await updatePermissionGroup(uuid, data)
        if (res.message === MESSAGE_STATUS.SUCCESS) {
          getData()
          setOpenPermissionModal(false)
          showCustomNotification({
            status: 'success',
            message: 'Thành công',
            description: 'Cập nhật nhóm quyền thành công!'
          })
        }
      } catch {
        showCustomNotification({ status: 'error', message: 'Thất bại', description: 'Có lỗi xảy ra!' })
      }
    } else {
      navigate('/manage-feature/add', { state: { groupName: data.groupName, description: data.description } })
    }
  }

  const items = (record: PermissionGroup): MenuProps['items'] =>
    record.allowModification
      ? [
          {
            key: 1,
            label: (
              <div className='flex items-center gap-2' onClick={() => navigate(`/manage-feature/${uuid}`)}>
                <Svg src='/assets/icons/common/permission.svg' className='h-5 w-5 text-neutral-4' />
                Phân quyền
              </div>
            )
          },
          {
            key: 2,
            label: (
              <div
                className='flex items-center gap-2'
                onClick={() => {
                  setIsUpdate(true)
                  setPermissionData({ groupName: record.groupName, description: record.description || '' })
                  setOpenPermissionModal(true)
                }}
              >
                <Svg src='/assets/icons/common/edit.svg' className='h-5 w-5 text-neutral-4' />
                Sửa
              </div>
            )
          },
          {
            key: 3,
            label: (
              <div className='flex items-center gap-2' onClick={() => setOpenDeleteModal(true)}>
                <Svg src='/assets/icons/common/trash.svg' className='h-5 w-5 text-neutral-4' />
                Xóa
              </div>
            )
          }
        ]
      : [
          {
            key: 1,
            label: (
              <div className='flex items-center gap-2' onClick={() => navigate(`/manage-feature/${uuid}`)}>
                <Svg src='/assets/icons/common/permission.svg' className='h-5 w-5 text-neutral-4' />
                Xem chi tiết
              </div>
            )
          }
        ]

  const columns: TableProps<PermissionGroup>['columns'] = [
    { title: 'STT', align: 'center', width: 48, render: (_, __, index) => (page - 1) * maxSize + (index + 1) },
    {
      title: 'Nhóm quyền',
      dataIndex: 'groupName',
      key: 'groupName'
    },
    {
      title: 'Mô tả',
      dataIndex: 'description',
      key: 'description'
    },
    ...(isFFA(FF_DEV_0022)
      ? [
          {
            title: 'Thao tác',
            width: isMobile || isTablet ? 60 : 80,
            key: 'action',
            hidden: uuidP ? true : false,
            render: (_: any, record: PermissionGroup) => (
              <Dropdown menu={{ items: items(record) }} trigger={['click']} placement='bottomRight'>
                <a
                  onClick={(e) => {
                    e.preventDefault()
                    setUuid(record.uuidPermissionGroup || '')
                  }}
                  className='flex justify-center'
                >
                  <MoreOutlined />
                </a>
              </Dropdown>
            )
          }
        ]
      : [])
  ]

  return (
    <>
      <PermissionGroupModal
        isModalOpen={openPermissionModal}
        update={isUpdate}
        handleOk={handleOk}
        permissrionData={permissrionData}
        handleCancel={() => setOpenPermissionModal(false)}
      />
      <ModalDelete
        isLoading={loading}
        isModalOpen={openDeleteModal}
        handleCancel={() => {
          setOpenDeleteModal(false)
        }}
        title='Xóa'
        handleOk={() => handleDelete(uuid, getData)}
        subTitle='Bạn có chắc chắn muốn xóa nhóm quyền này?'
      />
      <Spin spinning={loading}>
        {isFFA(FF_DEV_0022) && (
          <div className='mb-4 flex items-center justify-end'>
            <Button
              hidden={uuidP ? true : false}
              size='large'
              type='primary'
              icon={<PlusOutlined />}
              onClick={() => {
                setOpenPermissionModal(true)
                setIsUpdate(false)
              }}
            >
              Thêm mới
            </Button>
          </div>
        )}

        <Table
          columns={columns}
          dataSource={dataSource?.data}
          pagination={false}
          scroll={{ y: 530 }}
          rowKey={(record) => record.uuidPermissionGroup}
        />
      </Spin>
    </>
  )
}

export default PermissionGroupTable
