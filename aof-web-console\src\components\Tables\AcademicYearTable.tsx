import { MoreOutlined } from '@ant-design/icons'
import { Dropdown, MenuProps, Table, TableProps } from 'antd'
import { useState } from 'react'
import Svg from '../Svg'
import { AcademicYear } from '@/types/infomation'
import ModalDelete from '../Modals/DeleteModal'
import { MESSAGE_STATUS } from '@/constants'
import { showCustomNotification } from '@/common/Notification'
import { useNavigate } from 'react-router-dom'
import { deleteAcademicYear } from '@/services/academicYear'
import useLayoutStore from '@/stores/layout'
import useAuthStore from '@/stores/auth'
import { checkFeature, FF_DEV_0033, FF_DEV_0035, FF_DEV_0036 } from '@/utils/feature-flags'

export interface InfoTableProps {
  data: AcademicYear[]
  page: number
  maxSize: number
  isLoading: boolean
  setIsLoading: (value: boolean) => void
  refreshData: () => Promise<void>
}

const AcademicYearTable = ({ data, page, maxSize, isLoading, setIsLoading, refreshData }: InfoTableProps) => {
  const navigate = useNavigate()

  const { isMobile } = useLayoutStore()
  const { user, featFlagsAction } = useAuthStore()
  const isFFA = (code: string) => checkFeature(code, user, featFlagsAction)

  const [uuid, setUuid] = useState<string>('')

  const [openDeleteModal, setOpenDeleteModal] = useState<boolean>(false)

  const handleDeleteAcademicYear = async () => {
    try {
      setIsLoading(true)
      const res = await deleteAcademicYear(uuid)
      if (res.message === MESSAGE_STATUS.SUCCESS) {
        refreshData()
        showCustomNotification({
          status: 'success',
          message: 'Thành công',
          description: 'Xóa khóa thành công!'
        })
      }
    } catch {
      showCustomNotification({
        status: 'error',
        message: 'Thất bại',
        description: 'Xóa khóa thất bại!'
      })
    } finally {
      setIsLoading(false)
      setOpenDeleteModal(false)
    }
  }

  const items: MenuProps['items'] = [
    ...(isFFA(FF_DEV_0033)
      ? [
          {
            key: 1,
            label: (
              <div
                className='flex items-center gap-2'
                onClick={() => navigate(`/manage-info/academic-year/${uuid}`, { state: { update: false } })}
              >
                <Svg src='/assets/icons/common/detail.svg' className='h-5 w-5' />
                Xem chi tiết
              </div>
            )
          }
        ]
      : []),
    ...(isFFA(FF_DEV_0035)
      ? [
          {
            key: 2,
            label: (
              <div
                className='flex items-center gap-2'
                onClick={() => navigate(`/manage-info/academic-year/${uuid}`, { state: { update: true } })}
              >
                <Svg src='/assets/icons/common/edit.svg' className='h-5 w-5' />
                Sửa
              </div>
            )
          }
        ]
      : []),
    ...(isFFA(FF_DEV_0036)
      ? [
          {
            key: 3,
            label: (
              <div
                className='flex items-center gap-2'
                onClick={() => {
                  setOpenDeleteModal(true)
                }}
              >
                <Svg src='/assets/icons/common/trash.svg' className='h-5 w-5 text-neutral-4' />
                Xóa
              </div>
            )
          }
        ]
      : [])
  ]

  const columns: TableProps<AcademicYear>['columns'] = [
    {
      title: 'STT',
      key: 'key',
      width: 48,
      align: 'center',
      render: (_, __, index) => (page - 1) * maxSize + (index + 1)
    },
    {
      title: 'Năm học',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: 'Số kỳ học',
      dataIndex: 'semesterCount',
      key: 'semesterCount'
    },
    {
      title: 'Thao tác',
      width: isMobile ? 60 : 80,
      key: 'action',
      align: 'center',
      render: (_, record: AcademicYear) => (
        <Dropdown menu={{ items }} trigger={['click']}>
          <a
            onClick={(e) => {
              e.preventDefault()
              setUuid(record.uuidAcademicYear)
            }}
          >
            <MoreOutlined />
          </a>
        </Dropdown>
      )
    }
  ]

  return (
    <>
      <ModalDelete
        isLoading={isLoading}
        isModalOpen={openDeleteModal}
        handleCancel={() => {
          setOpenDeleteModal(false)
        }}
        title='Xóa'
        handleOk={handleDeleteAcademicYear}
        subTitle='Bạn có chắc chắn muốn xóa năm học?'
      />
      <Table
        loading={isLoading}
        columns={columns}
        dataSource={data || []}
        pagination={false}
        rowKey={(record) => record.uuidAcademicYear}
      />
    </>
  )
}

export default AcademicYearTable
