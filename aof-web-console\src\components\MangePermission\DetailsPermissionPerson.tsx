import LayoutCotent from '@/layouts/LayoutCotent'
import { useSegmentStore } from '@/stores/useSegmentStore'
import { permissionPersonList } from '@/types/components'
import { Segmented, Spin } from 'antd'
import { useEffect, useState } from 'react'
import { useLocation, useParams } from 'react-router-dom'
import Class from './Permission/Class'
import { PermissionGroup } from '@/types/user'
import PermissionGroupTable from '../Tables/Permission/GroupTable'
import { ResponseList } from '@/types/common'
import FeatureGroup from './Permission/Feature'

const DetailsPermissionPerson = () => {
  const { uuidPerson } = useParams()
  const { state } = useLocation()
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [permissionGroup] = useState<PermissionGroup>({} as PermissionGroup)
  const [dataSource, setDataSource] = useState<ResponseList<any>>()

  const { activePermissionPerson, setActivePermissionPerson, personName, setPersonName } = useSegmentStore()
  useEffect(() => {
    if (state) {
      setPersonName(state?.fullName || '')
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [state])

  const view = () => {
    switch (activePermissionPerson) {
      case 'Danh sách nhóm quyền':
        return (
          <PermissionGroupTable
            page={1}
            maxSize={10}
            dataSource={dataSource}
            uuidP={uuidPerson}
            setDataSource={setDataSource}
            setIsLoading={setIsLoading}
          />
        )
      case 'Phân quyền chức năng':
        return (
          <FeatureGroup
            state={state}
            permissionGroup={permissionGroup}
            uuidP={uuidPerson}
            setIsLoading={setIsLoading}
            // refreshData={getDetails}
            uuidPermissionGroup={''}
            setUuidPermission={() => {}}
          />
        )
      case 'Phân quyền lớp học':
        return (
          <Class
            setIsLoading={setIsLoading}
            uuidP={uuidPerson}
            uuidPermissionGroup={''}
            permissionGroup={permissionGroup}
          />
        )
      default:
        return (
          <PermissionGroupTable
            page={1}
            maxSize={10}
            dataSource={dataSource}
            setDataSource={setDataSource}
            setIsLoading={setIsLoading}
          />
        )
    }
  }
  return (
    <>
      <LayoutCotent title={['Phân quyền', 'Danh sách người dùng', personName]} btnBack={true}>
        <Spin spinning={isLoading}>
          <div className='overflow-x-auto' style={{ scrollbarWidth: 'none' }}>
            <Segmented
              value={activePermissionPerson}
              onChange={setActivePermissionPerson}
              size='large'
              options={permissionPersonList}
              className='mb-5 border'
            />
          </div>
          {view()}
        </Spin>
      </LayoutCotent>
    </>
  )
}
export default DetailsPermissionPerson
