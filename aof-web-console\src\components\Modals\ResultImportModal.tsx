import { Divider, Modal } from 'antd'
import Svg from '../Svg'
import { ModalProps } from '@/types/common'

interface Props extends ModalProps {
  numRecordSuccess: number
  numRecordFail: number
}

const ModalResultImport = ({ isModalOpen, handleCancel, numRecordSuccess, numRecordFail }: Props) => {
  return (
    <>
      <Modal
        open={isModalOpen}
        onCancel={handleCancel}
        width={444}
        footer={false}
        title={<p className='text-md font-semibold'>Kết quả import</p>}
      >
        <Divider className='mt-3 mb-5' />
        <div className='flex flex-col gap-3'>
          <div className='flex items-center justify-between'>
            <div className='flex items-center gap-2'>
              <Svg src='/assets/icons/common/file.svg' className='h-5 w-5' />
              <h2 className='font-medium text-base'>Bản ghi bị lỗi</h2>
            </div>
            <p className='font-medium text-sm text-error'>{numRecordFail}</p>
          </div>
          <div className='flex items-center justify-between'>
            <div className='flex items-center gap-2'>
              <Svg src='/assets/icons/common/file.svg' className='h-5 w-5' />
              <h2 className='font-medium text-base'>Bản ghi thành công</h2>
            </div>
            <p className='font-medium text-sm text-text-primary'>{numRecordSuccess}</p>
          </div>
        </div>
      </Modal>
    </>
  )
}

export default ModalResultImport
