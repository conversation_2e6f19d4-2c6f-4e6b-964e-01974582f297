import { CalendarEvent } from './calendar'
import { CustomField, ParamsSearch } from './common'
import { ClassStatus } from './components'
import { AcademicYear, Semester } from './infomation'
import { Lecturer } from './user'

export interface Group {
  uuidGroup: string
  groupCode: string
  groupName: string
  groupType: number
  uuidOrganization: string
  studentCount: number
  startDate: string
  endDate: string
  progressStatus: ClassStatus
  customFields: CustomField[]
  lecturers: Lecturer[]
  uuidCalendar?: string
  semester?: Semester[]
  calendarEvents?: CalendarEvent[]
  academicYear?: AcademicYear
  numberLessons?: number
  numberCredits?: number
  schoolYear?: string
  numberSemesters?: number
  time?: string
}

export interface GroupParams {
  groupName: string
  groupType: number
  groupCode?: string
  uuidSemester?: string
  startDate?: string
  endDate?: string
  customFields?: CustomField[]
}

export interface GroupParamsSearch extends ParamsSearch {
  countStudent?: number
  uuidAcademicYear?: string
  uuidSemester?: string
  uuidLecturer?: string
  progressStatus?: number
  groupType: number
}

export interface AcademicYearParamsSearch extends ParamsSearch {
  countSemester?: number
}

export interface TimeTable {
  dayOfWeek: string
  duration: string
  room: string
  leturer?: string
  timeSlot: string
}

export interface GroupParamsUpdate {
  groupName: string
  uuidSemester?: string
  startDate?: string
  endDate?: string
}
