import { OptionCustomeType } from './common'

export const TYPE_COURSE = 1
export const TYPE_CLASS_CREDIT = 2
export const TYPE_CLASS_EXAM = 3
export const MAX_SIZE_PAGE = 200
export enum TYPE_PERSON {
  'Sinh viên' = 1,
  'Giảng viên' = 2,
  'Nhân viên chức năng' = 3,
  'Phê duyệt ảnh' = 4
}

export enum InfoType {
  'Quản lý khóa' = 1,
  'Quản lý năm học' = 4
}

export enum PremissionType {
  'Danh sách nhóm quyền',
  'Danh sách người dùng'
}

export type URL =
  | '/manage-user'
  | '/manage-class'
  | '/manage-room-device'
  | '/manage-exam'
  | '/manage-info'
  | '/personal-info'
  | '/manage-feature'
  | '/settings'

export type ClassType = 'Lớp học' | 'Lớp thi'
export type Premission = 'Danh sách nhóm quyền' | 'Danh sách người dùng'
export type ClassInfo =
  | 'Thông tin chung'
  | 'Thời khóa biểu'
  | 'Danh sách sinh viên'
  | 'Giảng viên'
  | 'Điểm danh'
  | 'Lịch thi'
  | 'Cán bộ trông thi'
export type PermissionInfo =
  | 'Danh sách nhóm quyền'
  | 'Phân quyền chức năng'
  | 'Phân quyền lớp học'
  | 'Phân quyền người dùng'

export type SystemInfo = 'Nhật ký hệ thống' | 'Cấu hình'
export type Room_Device = 'Quản lý phòng học' | 'Máy điểm danh' | 'Camera'
export type Info = 'Quản lý khóa' | 'Quản lý năm học'
export type CustomFields = 'department' | 'specialization' | 'school' | 'numberOfClassPeriods' | 'numberOfCredits'
export type Grant_type = 'authorization_code' | 'refresh_token'

export enum Status {
  'Deactive',
  'Active'
}

export enum UserStatus {
  'Hoạt động' = 1,
  'Bảo lưu' = 2,
  'Thôi học' = 3,
  'Đã tốt nghiệp' = 4,
  'Đã nghỉ' = 5
}

export enum CheckinStatus {
  'Đã điểm danh máy' = 1,
  'Chưa điểm danh' = 2
}

export enum AttendanceStatus {
  'Defalt' = 0,
  'Đi học' = 1,
  'Nghỉ học' = 2,
  'Đi thi' = 3,
  'Vắng thi' = 4
}

export enum BehaviorStatus {
  'Sử dụng điện thoại' = 1,
  'Trao đổi bài' = 2
}

export enum ImageStatus {
  'Chờ duyệt' = 1,
  'Đã duyệt' = 2,
  'Từ chối' = 3
}

export enum ExamStatus {
  'Tất cả' = '',
  'Chưa xác nhận' = 1,
  'Đã xác nhận' = 2
}

export enum ClassStatus {
  'Chưa bắt đầu' = 1,
  'Đang thi',
  'Đang học' = 2,
  'Kết thúc' = 3
}

export enum Gender {
  'Khác' = 0,
  'Nam' = 1,
  'Nữ' = 2
}

export enum Device {
  'Không xác định' = 0,
  'Hoạt động' = 1,
  'Không hoạt động' = 2
}

export enum EntityType {
  'Person' = 1,
  'Group' = 2
}

export const customFieldPersonList: CustomFields[] = ['department', 'specialization', 'school']
export const customFieldGroupList: CustomFields[] = ['numberOfCredits', 'numberOfClassPeriods']
export const classList: ClassType[] = ['Lớp học', 'Lớp thi']
export const accessList: Premission[] = ['Danh sách nhóm quyền', 'Danh sách người dùng']
export const classInfoList: ClassInfo[] = [
  'Thông tin chung',
  'Thời khóa biểu',
  'Lịch thi',
  'Danh sách sinh viên',
  'Cán bộ trông thi',
  'Giảng viên',
  'Điểm danh'
]
export const permissionList: PermissionInfo[] = ['Phân quyền chức năng', 'Phân quyền lớp học', 'Phân quyền người dùng']
export const permissionPersonList: PermissionInfo[] = [
  'Danh sách nhóm quyền',
  'Phân quyền chức năng',
  'Phân quyền lớp học'
]
export const roomDeviceList: Room_Device[] = ['Quản lý phòng học', 'Máy điểm danh', 'Camera']
export const infoList: Info[] = ['Quản lý khóa', 'Quản lý năm học']
export const logList: SystemInfo[] = ['Nhật ký hệ thống', 'Cấu hình']

export const statusStudent: OptionCustomeType<UserStatus>[] = [
  {
    value: UserStatus['Hoạt động'],
    label: 'Hoạt động'
  },
  {
    value: UserStatus['Đã tốt nghiệp'],
    label: 'Đã tốt nghiệp'
  },
  {
    value: UserStatus['Bảo lưu'],
    label: 'Bảo lưu'
  },
  {
    value: UserStatus['Thôi học'],
    label: 'Thôi học'
  }
]

export const statusOther: OptionCustomeType<UserStatus>[] = [
  {
    value: UserStatus['Hoạt động'],
    label: 'Hoạt động'
  },
  {
    value: UserStatus['Bảo lưu'],
    label: 'Bảo lưu'
  },
  {
    value: UserStatus['Đã nghỉ'],
    label: 'Đã nghỉ'
  }
]

export const statusImage: OptionCustomeType<ImageStatus>[] = [
  {
    value: ImageStatus['Chờ duyệt'],
    label: 'Chờ duyệt'
  },
  {
    value: ImageStatus['Đã duyệt'],
    label: 'Đã duyệt'
  },
  {
    value: ImageStatus['Từ chối'],
    label: 'Từ chối'
  }
]

export const statusCreditClass: OptionCustomeType<ClassStatus>[] = [
  {
    value: ClassStatus['Chưa bắt đầu'],
    label: 'Chưa bắt đầu'
  },
  {
    value: ClassStatus['Đang học'],
    label: 'Đang học'
  },
  {
    value: ClassStatus['Kết thúc'],
    label: 'Kết thúc'
  }
]

export const statusExamClass: OptionCustomeType<ClassStatus>[] = [
  {
    value: ClassStatus['Chưa bắt đầu'],
    label: 'Chưa bắt đầu'
  },
  {
    value: ClassStatus['Đang thi'],
    label: 'Đang thi'
  },
  {
    value: ClassStatus['Kết thúc'],
    label: 'Kết thúc'
  }
]

export const gender: OptionCustomeType<Gender>[] = [
  {
    value: Gender.Nam,
    label: 'Nam'
  },
  {
    value: Gender.Nữ,
    label: 'Nữ'
  },
  {
    value: Gender.Khác,
    label: 'Khác'
  }
]
export const attendanceStatus: OptionCustomeType<AttendanceStatus>[] = [
  {
    value: AttendanceStatus['Nghỉ học'],
    label: 'Nghỉ học'
  },
  {
    value: AttendanceStatus['Đi học'],
    label: 'Đi học'
  }
  // {
  //   value: AttendanceStatus['Defalt'],
  //   label: 'Nghỉ học'
  // }
]
export const attendanceExamStatus: OptionCustomeType<AttendanceStatus>[] = [
  {
    value: AttendanceStatus['Đi thi'],
    label: 'Đi thi'
  },
  {
    value: AttendanceStatus['Vắng thi'],
    label: 'Vắng thi'
  }
]
export const statusPresenceReason = ['Vào muộn', 'Không nhận diện được', 'Lý do khác'].map((value) => ({ value }))

export const statusAbsenceReason = ['Vắng giữa giờ', 'Muộn quá thời gian quy định', 'Lý do khác'].map((value) => ({
  value
}))

export const statusExam: OptionCustomeType<ExamStatus>[] = [
  {
    value: ExamStatus['Tất cả'],
    label: 'Tất cả'
  },
  {
    value: ExamStatus['Đã xác nhận'],
    label: 'Đã xác nhận'
  },
  {
    value: ExamStatus['Chưa xác nhận'],
    label: 'Chưa xác nhận'
  }
]

export const pageSize = [50, 100, 150, 200].map((size) => ({
  label: `${size} / trang`,
  value: size
}))

export const initialValue = { keySearch: '', status: 1, uuidGroups: '' }
export const initialProperty = { page: 1, maxSize: 50, totalElement: 10 }
export const initialValues = {
  groupName: '',
  groupCode: '',
  numberOfCredits: '',
  numberOfLessons: '',
  studentCount: '',
  academicYear: '',
  uuidSemester: '',
  rangeTime: '',
  progressStatus: ''
}

export const dayLearn = ['Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7', 'Chủ nhật']
export const dayMap: Record<string, string> = {
  'Thứ 2': 'MO',
  'Thứ 3': 'TU',
  'Thứ 4': 'WE',
  'Thứ 5': 'TH',
  'Thứ 6': 'FR',
  'Thứ 7': 'SA',
  'Chủ nhật': 'SU'
}
export const reversedDayMap = Object.fromEntries(Object.entries(dayMap).map(([key, value]) => [value, key]))
