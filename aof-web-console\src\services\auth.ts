import { ApiResponse, BodyData, Params, ResponseList, ResponseToken } from '@/types/common'
import {
  Lisence,
  ParamsLogin,
  PasswordParams,
  PermissionClassParams,
  PermissionGroup,
  PermissionGroupData,
  PermissionGroupFeatureAction,
  PermissionGroupParams,
  PermissionGroupResource,
  PersonPermissionGroup,
  PersonPermissionGroupData,
  PersonsPermissionGroupParams,
  ResourcePermission,
  ResponseData,
  UserInfo
} from '@/types/user'
import axiosInstance from '@/utils/axios'

//2.1. Lấy token truy cập api.
export async function login(params: ParamsLogin): Promise<ApiResponse<ResponseData>> {
  const response = await axiosInstance.post('/web-service/v1/auth/token', params)
  return response.data
}

//2.2. L<PERSON>y thông tin tài khoản đang đăng nhập.
export async function getInfo(): Promise<ApiResponse<UserInfo>> {
  const response = await axiosInstance.get('/web-service/v1/people/self-info')
  return response.data
}
//2.57. Lấy token của sso.
export async function getTokenSSO(params: Params): Promise<ApiResponse<BodyData>> {
  const response = await axiosInstance.post('/web-service/v1/sso/oauth2/token', params, {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
  return response.data
}

//2.58. Lấy token truy cập api thông qua token của sso.
export async function getToken(accessToken: string): Promise<ApiResponse<ResponseToken>> {
  const response = await axiosInstance.post('/web-service/v1/auth/token/sso', { accessToken })
  return response.data
}

//2.59. Danh sách tính năng.
export async function getFeatures(): Promise<ApiResponse<any>> {
  const response = await axiosInstance.get('/web-service/v1/features')
  return response.data
}

//2.60. Thêm mới permission group (nhóm quyền).
export async function addPermissionGroup(body: PermissionGroupData): Promise<ApiResponse<any>> {
  const response = await axiosInstance.post('/web-service/v1/permission-groups', body)
  return response.data
}

//2.61. Xem chi tiết permission group (nhóm quyền).
export async function getPermissionGroupDetail(uuidPermissionGroup: string): Promise<ApiResponse<PermissionGroup>> {
  const response = await axiosInstance.get(`/web-service/v1/permission-groups/${uuidPermissionGroup}`)
  return response.data
}

//2.62. Chỉnh sửa permission group (nhóm quyền).
export async function updatePermissionGroup(
  uuidPermissionGroup: string,
  body: PermissionGroupData
): Promise<ApiResponse<any>> {
  const response = await axiosInstance.put(`/web-service/v1/permission-groups/${uuidPermissionGroup}`, body)
  return response.data
}

//2.63. Xóa permission group (nhóm quyền).
export async function deletePermissionGroup(uuidPermissionGroup: string): Promise<ApiResponse<any>> {
  const response = await axiosInstance.delete(`/web-service/v1/permission-groups/${uuidPermissionGroup}`)
  return response.data
}

//2.64. Danh sách permission group.
export async function getListPermissionGroup(
  params: PermissionGroupParams
): Promise<ApiResponse<ResponseList<PermissionGroup>>> {
  const response = await axiosInstance.get('/web-service/v1/permission-groups', { params })
  return response.data
}

//2.65. Thêm mới nhiều permission group feature action (cấp quyền chức năng cho nhóm quyền)
export async function addMultiplePermissionGroup(
  permissionGroupFeatureActions: PermissionGroupFeatureAction[]
): Promise<ApiResponse<any>> {
  const response = await axiosInstance.post('/web-service/v1/permission-group-feature-actions/multiple', {
    permissionGroupFeatureActions
  })
  return response.data
}

//2.66. Xóa nhiều permission group feature action
export async function deleteMultiplePermissionGroup(
  permissionGroupFeatureActions: PermissionGroupFeatureAction[]
): Promise<ApiResponse<any>> {
  const response = await axiosInstance.post('/web-service/v1/permission-group-feature-actions/multiple/deletion', {
    permissionGroupFeatureActions
  })
  return response.data
}

//2.67. Thêm mới nhiều person permission group (thêm người dùng vào nhóm quyền)
export async function addPersonsToPermissionGroup(
  personPermissionGroups: PersonPermissionGroup[]
): Promise<ApiResponse<any>> {
  const response = await axiosInstance.post('/web-service/v1/person-permission-groups/multiple', {
    personPermissionGroups
  })
  return response.data
}

//2.68. Xóa nhiều person permission group (xóa người dùng khỏi nhóm quyền)
export async function deletePersonsToPermissionGroup(
  personPermissionGroups: PersonPermissionGroup[]
): Promise<ApiResponse<any>> {
  const response = await axiosInstance.post('/web-service/v1/person-permission-groups/multiple/deletion', {
    personPermissionGroups
  })
  return response.data
}

//2.69. Danh sách person permission group (danh sách người dùng thuộc nhóm quyền).
export async function getListPersonsPermissionGroup(
  params: PersonsPermissionGroupParams
): Promise<ApiResponse<ResponseList<PersonPermissionGroupData>>> {
  const response = await axiosInstance.get('/web-service/v1/person-permission-groups', { params })
  return response.data
}

//2.70. Thêm mới permission group resource (Phân nhóm quyền theo lớp).
export async function permissionToGroup(personPermissionGroups: PermissionGroupResource): Promise<ApiResponse<any>> {
  const response = await axiosInstance.post('/web-service/v1/permission-group-resources', personPermissionGroups)
  return response.data
}

//2.71. Xem chi tiết permission group resource (Phân quyền lớp).
export async function getPermissionToGroup(
  uuidPermissionGroupResource: string
): Promise<ApiResponse<PermissionGroupResource>> {
  const response = await axiosInstance.get(`/web-service/v1/permission-group-resources/${uuidPermissionGroupResource}`)
  return response.data
}

//2.72. Sửa permission group resource (Phân nhóm quyền theo lớp).
export async function updatePermissionToGroup(
  uuidPermissionGroupResource: string,
  body: PermissionClassParams
): Promise<ApiResponse<any>> {
  const response = await axiosInstance.put(
    `/web-service/v1/permission-group-resources/${uuidPermissionGroupResource}`,
    body
  )
  return response.data
}

//2.73. Danh sách permission group resource (Phân quyền lớp).
export async function getListPermissionToGroup(
  params: PersonsPermissionGroupParams
): Promise<ApiResponse<ResponseList<PermissionGroupResource>>> {
  const response = await axiosInstance.get(`/web-service/v1/permission-group-resources`, { params })
  return response.data
}

//2.74. Danh sách feature action được grant cho một person.
export async function getListFeatureGrantPerson(uuidPerson: string): Promise<ApiResponse<any>> {
  const response = await axiosInstance.get(`/web-service/v1/feature-actions/people/${uuidPerson}`)
  return response.data
}

//2.75. Xem chi tiết permission group resource của một person.
export async function getDetailFeatureGrantPerson(
  uuidPerson: string,
  resourceType: number
): Promise<ApiResponse<ResourcePermission>> {
  const response = await axiosInstance.get(`/web-service/v1/permission-group-resources/people/${uuidPerson}`, {
    params: { resourceType }
  })
  return response.data
}

//2.79. Thêm mới nhiều lisense
export async function addMultipleLicense(licenses: Lisence[]): Promise<ApiResponse<any>> {
  const response = await axiosInstance.post('/web-service/v1/licenses/multiple', { licenses })
  return response.data
}

//2.80. Danh sách lisense
export async function getListLicense(): Promise<ApiResponse<ResponseList<Lisence>>> {
  const response = await axiosInstance.get('/web-service/v1/licenses')
  return response.data
}

//2.81. Xem trạng thái của license
export async function getLicenseStatus(): Promise<ApiResponse<any>> {
  const response = await axiosInstance.get('/web-service/v1/licenses/status')
  return response.data
}

//2.85. Đổi mật khẩu.
export async function changePassword(data: PasswordParams): Promise<ApiResponse<any>> {
  const response = await axiosInstance.post('/web-service/v1/auth/password/change', data)
  return response.data
}

//2.86. Lấy thông tin user của sso.
export async function checkInforUser(accessToken: string): Promise<ApiResponse<any>> {
  const response = await axiosInstance.post('/web-service/v1/sso/oauth2/userinfo', { accessToken })
  return response.data
}

//2.87. Revoke token của sso.
export async function revokeToken(token: string, token_type_hint: string): Promise<ApiResponse<any>> {
  const response = await axiosInstance.post(
    '/web-service/v1/sso/oauth2/revoke',
    { token, token_type_hint },
    {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    }
  )
  return response.data
}
