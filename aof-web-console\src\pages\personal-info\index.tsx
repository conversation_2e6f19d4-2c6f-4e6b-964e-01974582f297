import { UserStatusField } from '@/components/StatusField'
import Svg from '@/components/Svg'
import LayoutCotent from '@/layouts/LayoutCotent'
import { Button, Col, Row, Card } from 'antd'
import { Fragment, useMemo, useState } from 'react'
import useAuthStore from '@/stores/auth'
import { customFieldPersonList, CustomFields, TYPE_PERSON } from '@/types/components'
import WebcamModal from '@/components/Modals/WebcamModal'

const PersonalInfo = () => {
  const [openCameraModal, setOpenCameraModal] = useState(false)
  const { user } = useAuthStore()
  const previewImg = useMemo(() => {
    return user.faces?.[0]?.objectUrl
  }, [user])
  const TYPE_PERSON_LABEL = Object.fromEntries(Object.entries(TYPE_PERSON).map(([label, value]) => [value, label]))

  return (
    <>
      <WebcamModal isModalOpen={openCameraModal} handleOk={() => {}} handleCancel={() => setOpenCameraModal(false)} />
      <LayoutCotent title={['Thông tin cá nhân']}>
        <div className='flex flex-col md:flex-row gap-4 w-full'>
          <Card
            className='w-full md:max-w-60 border border-[#DCDEEF]'
            title={<span className='text-base font-semibold'>Ảnh đại diện</span>}
            type='inner'
            bordered
          >
            <div className='flex flex-col items-center'>
              {previewImg ? (
                <div className='w-40 h-40 rounded-full overflow-hidden my-4'>
                  <img src={previewImg} alt='avatar' className='w-full h-full object-cover' />
                </div>
              ) : (
                <Svg src='/assets/icons/common/image-avatar.svg' className='w-32 h-32 mb-4' />
              )}
              {user.personType === TYPE_PERSON['Sinh viên'] && (
                <Button type='primary' size='large' className='w-40 mt-5' onClick={() => setOpenCameraModal(true)}>
                  Thay ảnh đại diện
                </Button>
              )}
            </div>
          </Card>

          <div className='grid grid-cols-1 gap-4'>
            <Card
              title={<span className='text-base font-semibold'>Thông tin cá nhân</span>}
              type='inner'
              className='border border-[#DCDEEF]'
              bordered
            >
              <Row gutter={[24, 8]}>
                <Col span={8} className='text-neutral-4 '>
                  Họ tên
                </Col>
                <Col span={16}>{user.fullName}</Col>
                <Col span={8} className='text-neutral-4 '>
                  Email
                </Col>
                <Col span={16}>{user.email}</Col>
                <Col span={8} className='text-neutral-4 '>
                  Ngày sinh
                </Col>
                <Col span={16}>{user.dob}</Col>
                <Col span={8} className='text-neutral-4 '>
                  Giới tính
                </Col>
                <Col span={16}>{user.gender === 1 ? 'Nam' : 'Nữ'}</Col>
              </Row>
            </Card>
            <Card
              title={
                <span className='text-base font-semibold'>
                  Thông tin {TYPE_PERSON_LABEL[user.personType]?.toLowerCase()}
                </span>
              }
              type='inner'
              className='border border-[#DCDEEF]'
              bordered
            >
              <Row gutter={[24, 8]}>
                <Col span={8} className='text-neutral-4 '>
                  ID
                </Col>
                <Col span={16}>{user.personCode?.toUpperCase()}</Col>
                {user?.customFields
                  ?.filter((item) => customFieldPersonList.includes(item.customFieldName as CustomFields))
                  .map((item, index) => (
                    <Fragment key={index}>
                      <Col span={8} className='text-neutral-4 '>
                        {item.customFieldName === 'department' && 'Đơn vị'}
                        {item.customFieldName === 'school' && 'Khoa'}
                        {item.customFieldName === 'specialization' && 'Chuyên ngành'}
                      </Col>
                      <Col span={16}>{item.customFieldValue}</Col>
                    </Fragment>
                  ))}
                <Col span={8} className='text-neutral-4 '>
                  Số điện thoại
                </Col>
                <Col span={16}>{user?.phoneNo}</Col>
                <Col span={8} className='text-neutral-4 '>
                  Trạng thái tài khoản
                </Col>
                <Col span={16}>
                  <UserStatusField status={user.status || 1} />
                </Col>
              </Row>
            </Card>
          </div>
        </div>
      </LayoutCotent>
    </>
  )
}

export default PersonalInfo
