import { ROLE_TITLE } from '@/constants'

export const FF_DEV_0000 = 'person_info'

export const FF_DEV_0001 = 'info_management'
export const FF_DEV_0002 = 'person_management'
export const FF_DEV_0003 = 'class_management'
export const FF_DEV_0004 = 'infrastructure_management'
export const FF_DEV_0005 = 'exam_cheating_management'
export const FF_DEV_0006 = 'permission_management'
export const FF_DEV_0045 = 'system_log'

export const FF_DEV_0007 = 'person_management_child'
export const FF_DEV_0008 = 'image_collection'
export const FF_DEV_0009 = 'cohort_management'
export const FF_DEV_0010 = 'academic_year_management'
export const FF_DEV_0011 = 'classroom_management'
export const FF_DEV_0012 = 'acs_dev_management'
export const FF_DEV_0013 = 'camera_management'
export const FF_DEV_0014 = 'class_management_child'
export const FF_DEV_0015 = 'attendance_management'

export const FF_DEV_0016 = 'read_acs_dev'
export const FF_DEV_0017 = 'read_classroom'
export const FF_DEV_0018 = 'read_exam_cheating'
export const FF_DEV_0019 = 'update_exam_cheating'
export const FF_DEV_0020 = 'delete_exam_cheating'
export const FF_DEV_0021 = 'read_permission'
export const FF_DEV_0022 = 'create_permission'
export const FF_DEV_0023 = 'read_person'
export const FF_DEV_0024 = 'create_person'
export const FF_DEV_0025 = 'update_person'
export const FF_DEV_0026 = 'delete_person'
export const FF_DEV_0027 = 'read_image_collection'
export const FF_DEV_0028 = 'update_image_collection'
export const FF_DEV_0029 = 'read_cohort'
export const FF_DEV_0030 = 'create_cohort'
export const FF_DEV_0031 = 'update_cohort'
export const FF_DEV_0032 = 'delete_cohort'
export const FF_DEV_0033 = 'read_academic_year'
export const FF_DEV_0034 = 'create_academic_year'
export const FF_DEV_0035 = 'update_academic_year'
export const FF_DEV_0036 = 'delete_academic_year'
export const FF_DEV_0037 = 'read_camera'
export const FF_DEV_0038 = 'update_camera'
export const FF_DEV_0039 = 'read_class'
export const FF_DEV_0040 = 'create_class'
export const FF_DEV_0041 = 'update_class'
export const FF_DEV_0042 = 'delete_class'
export const FF_DEV_0043 = 'read_attendance'
export const FF_DEV_0044 = 'create_attendance'
export const FF_DEV_0046 = 'create_camera'
export const FF_DEV_0047 = 'delete_camera'
//add new feature flags here, please follow the naming convention FF_DEV_XXXX, please add the feature flag to the list above in constants/feature-flags.ts

export const isAdmin = (user: any): boolean => user.roles?.[0]?.name?.toUpperCase() === ROLE_TITLE.ADMIN

export const checkFeature = (code: string, user: any, flags: Record<string, { on: boolean }> | undefined): boolean => {
  return isAdmin(user) || flags?.[code]?.on === true
}
