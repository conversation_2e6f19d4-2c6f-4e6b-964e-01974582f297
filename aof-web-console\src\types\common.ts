import { Grant_type, URL } from './components'
export interface ApiResponse<T> {
  message: string
  object: T
}

export interface ParamsSearch {
  page: number
  maxSize: number
  keySearch?: string
}

export type ResponseList<T> = {
  page: number
  maxSize: number
  totalElement: number
  totalPages: number
  data: T[]
}

export type PATH = {
  label: string
  icon: string
  activeIcon: string
  url: URL
  actionCode: string
  showAction?: boolean
  children?: PATH
}

export interface ModalProps {
  isModalOpen: boolean
  update?: boolean
  handleOk?: (data?: any) => void
  handleCancel: (e?: any) => void
  subTitle?: string
  isLoading?: boolean
  title?: string
  image?: string
}

export type OptionCustomeType<T> = { value: T; label: string }

export interface Organization {
  uuidOrganization: string
  orgCode: string
  orgName: string
}

export interface Role {
  uuidRole: string
  name: string
}

export interface CustomField {
  uuidCustomField: string
  customFieldValue?: string
  customFieldName?: string
  customFieldType?: number
  entityType?: number
}

export interface Image {
  formData: Map<string, string>
  bucketName: string
  objectName: string
  contentType: string
  postUrl: string
}

export interface Params {
  grant_type: Grant_type
  redirect_uri?: string
  code?: string
  refresh_token?: string
}

export interface BodyData {
  access_token: string
  refresh_token: string
  id_token: string
  token_type?: string
  expire_in: number
}

export interface ResponseToken {
  accessToken: string
  refreshToken: string
  expire_in: number
}
