import { showCustomNotification } from '@/common/Notification'
import { MESSAGE_STATUS } from '@/constants'
import { useState } from 'react'

export function useDeleteItem(
  deleteFunction: (id: string) => Promise<any>,
  successMessage: string,
  errorMessage: string
) {
  const [loading, setLoading] = useState(false)
  const [openDeleteModal, setOpenDeleteModal] = useState(false)

  const handleDelete = async (id: string, onSuccess?: () => void) => {
    try {
      setLoading(true)
      const res = await deleteFunction(id)
      if (res.message === MESSAGE_STATUS.SUCCESS) {
        showCustomNotification({
          status: 'success',
          message: 'Thành công',
          description: successMessage
        })
        onSuccess?.() // Gọi hàm callback sau khi xóa thành công
      }
    } catch {
      showCustomNotification({
        status: 'error',
        message: 'Th<PERSON><PERSON> bạ<PERSON>',
        description: errorMessage
      })
    } finally {
      setLoading(false)
      setOpenDeleteModal(false)
    }
  }

  return {
    loading,
    openDeleteModal,
    setOpenDeleteModal,
    handleDelete
  }
}
