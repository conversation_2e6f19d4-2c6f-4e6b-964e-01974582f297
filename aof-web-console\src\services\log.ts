import { ApiResponse, ResponseList } from '@/types/common'
import { LogParamsSearch, RequestResponseLog, SystemConfig } from '@/types/log'
import axiosInstance from '@/utils/axios'

//2.76 Danh sách nhật ký hệ thống
export async function getSystemLogs(params: LogParamsSearch): Promise<ApiResponse<ResponseList<RequestResponseLog>>> {
  const response = await axiosInstance.get('/web-service/v1/request-response-logs', { params })
  return response.data
}

//2.77 Danh sách cấu hình hệ thống
export async function getSystemConfigs(): Promise<ApiResponse<{ data: SystemConfig[] }>> {
  const response = await axiosInstance.get('/web-service/v1/system-configs')
  return response.data
}

//2.78 Chỉnh sửa cấu hình hệ thống
export async function updateSystemConfigs(uuidSystemConfig: string, value: string): Promise<ApiResponse<any>> {
  const response = await axiosInstance.put(`/web-service/v1/system-configs/${uuidSystemConfig}`, value)
  return response.data
}
