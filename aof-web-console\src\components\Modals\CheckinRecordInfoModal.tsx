import { ModalProps } from '@/types/common'
import {
  attendanceStatus,
  AttendanceStatus,
  statusAbsenceReason,
  statusPresenceReason,
  TYPE_PERSON
} from '@/types/components'
import { Button, Col, Input, Modal, Row, Spin, Image, Select } from 'antd'
import { useCallback, useEffect, useState } from 'react'
import { CheckinStatusField } from '../StatusField'
import dayjs, { Dayjs } from 'dayjs'
import { AttendanceRecord } from '@/types/session'
import { getImageFromHik } from '@/services/common'
import useAuthStore from '@/stores/auth'

interface Props extends ModalProps {
  personAttendace: AttendanceRecord
  listAttendanceRecord?: AttendanceRecord[]
  sessionAttendaceStatus?: boolean
  setData: (e: any) => void
  setListAttendanceRecord?: (e: any) => void
  setPersonAttendace: (e: any) => void
}
const ModalCheckinRecordInfo = ({
  isModalOpen,
  personAttendace,
  listAttendanceRecord,
  sessionAttendaceStatus,
  handleCancel,
  setData,
  setListAttendanceRecord,
  setPersonAttendace
}: Props) => {
  const { user } = useAuthStore()
  const [reason, setReason] = useState('')
  const [imageRecord, setImageRecord] = useState('')
  const [selectedStatus, setSelectedStatus] = useState<number>(AttendanceStatus['Nghỉ học'])
  const [selectedReason, setSelectedReason] = useState<string>()
  const [sessionState, setSessionState] = useState({
    date: dayjs(),
    pickTime: null as [Dayjs | null, Dayjs | null] | null,
    isLoading: false
  })

  useEffect(() => {
    setSelectedStatus(personAttendace.attendanceStatus)
  }, [personAttendace])

  const getImage = useCallback(async () => {
    try {
      setSessionState((prev) => ({ ...prev, isLoading: true }))
      if (!personAttendace.checkInRecord) return setImageRecord('')
      const res = await getImageFromHik(personAttendace.checkInRecord?.picUri)
      setImageRecord(res.object.data)
    } finally {
      setSessionState((prev) => ({ ...prev, isLoading: false }))
    }
  }, [personAttendace])

  useEffect(() => {
    getImage()
  }, [getImage])

  const handleStatusChange = (value: number) => {
    setSelectedStatus(value)
    setSelectedReason(selectedStatus === 2 ? 'Vào muộn' : 'Vắng giữa giờ')
    setReason('')
  }

  const updateAttendanceData = (updatedAttendance: any) => {
    setData((prevData: any) => ({
      ...prevData,
      data: prevData.data.map((attendance: AttendanceRecord) =>
        attendance.uuidPerson === personAttendace.uuidPerson ? { ...attendance, ...updatedAttendance } : attendance
      )
    }))
  }

  const handleSave = () => {
    if (selectedStatus === personAttendace.attendanceStatus) {
      setPersonAttendace({})
      return
    }
    const updatedAttendance = {
      attendanceStatus: selectedStatus,
      approvalReason: reason ? reason : selectedReason === 'Lý do khác' ? '' : selectedReason
    }

    if (listAttendanceRecord) {
      const existingRecord = listAttendanceRecord.some(
        (attendance) => attendance.uuidPerson === personAttendace.uuidPerson
      )

      if (existingRecord) {
        const updatedRecords = listAttendanceRecord.map((attendance) =>
          attendance.uuidPerson === personAttendace.uuidPerson ? { ...attendance, ...updatedAttendance } : attendance
        )
        setListAttendanceRecord?.(updatedRecords)
      } else {
        updateAttendanceData(updatedAttendance)
      }
    } else {
      updateAttendanceData(updatedAttendance)
    }

    setPersonAttendace({})
  }

  const imageUrl = personAttendace.person?.faces?.[0]?.objectUrl || '/assets/icons/common/avatar-checkin.svg'
  const imageUrlCheckIn = imageRecord || '/assets/icons/common/avatar-checkin.svg'

  return (
    <Modal
      open={isModalOpen}
      onCancel={handleCancel}
      closable={false}
      width={444}
      title={
        <div
          className='bg-background-tick rounded-t-2xl text-center flex items-center justify-center'
          style={{ height: '3.4rem' }}
        >
          <h1 className='text-lg font-semibold'>Thông tin điểm danh</h1>
        </div>
      }
      className='customer-model rounded-2xl'
      footer={false}
    >
      <Spin spinning={sessionState.isLoading}>
        <div className='p-4 flex justify-evenly gap-4'>
          <Image
            src={imageUrl}
            className='rounded-2xl object-cover'
            style={{ width: 150, height: 200 }}
            preview={false}
          />
          <Image
            src={imageUrlCheckIn}
            className='rounded-2xl object-cover'
            style={{ width: 150, height: 200 }}
            preview={false}
          />
        </div>
        <div className='p-3 rounded-t-2xl grid grid-cols-2 text-center'>
          <h1 className='text-sm font-semibold'>Ảnh đại diện</h1>
          <h1 className='text-sm font-semibold'>Ảnh điểm danh</h1>
        </div>

        <div className='p-4'>
          <Row gutter={[24, 8]}>
            <Col span={9}>Họ và tên:</Col>
            <Col span={15} className='text-neutral-2'>
              {personAttendace.person?.fullName || '-'}
            </Col>

            <Col span={9}>ID:</Col>
            <Col span={15} className='text-neutral-2'>
              {personAttendace.person?.personCode.toLocaleUpperCase() || '-'}
            </Col>

            <Col span={9}>Thời gian điểm danh:</Col>
            <Col span={15} className='text-neutral-2'>
              {personAttendace.person?.dob || '-'}
            </Col>

            <Col span={9}>Trạng thái điểm danh:</Col>
            <Col span={15} className='text-neutral-2'>
              <CheckinStatusField status={personAttendace.checkInStatus} />
            </Col>

            <Col span={9} className='flex items-center'>
              Giảng viên xác nhận:
            </Col>
            <Col span={15} className='flex items-center'>
              <Select
                size='large'
                value={selectedStatus}
                options={attendanceStatus}
                className='w-full'
                disabled={sessionAttendaceStatus || user.personType === TYPE_PERSON['Sinh viên']}
                onChange={handleStatusChange}
              />
            </Col>
            {selectedStatus !== personAttendace.attendanceStatus && (
              <>
                <Col span={9} className='flex items-center'>
                  Lý do xác nhận: <span className='text-error'>*</span>
                </Col>
                <Col span={15} className='flex items-center'>
                  <Select
                    size='large'
                    value={selectedReason}
                    options={selectedStatus === 1 ? statusPresenceReason : statusAbsenceReason}
                    className='w-full'
                    disabled={sessionAttendaceStatus || user.personType === TYPE_PERSON['Sinh viên']}
                    onChange={(value) => setSelectedReason(value)}
                  />
                </Col>
                {selectedReason === 'Lý do khác' && (
                  <>
                    <Col span={9} className='flex items-center'>
                      Chi tiết nội dung: <span className='text-error'>*</span>
                    </Col>
                    <Col span={15} className='flex items-center relative'>
                      <Input.TextArea
                        placeholder='Ghi lý do'
                        maxLength={100}
                        value={reason}
                        required
                        onChange={(e) => setReason(e.target.value)}
                      />
                      <span className='absolute right-5 bottom-1 [color:#c4c4c4]'>({reason.length}/100)</span>
                    </Col>
                  </>
                )}
              </>
            )}
            <Col span={24}>
              <div className='flex items-center justify-end mt-2 gap-4'>
                <Button
                  variant='outlined'
                  color='primary'
                  size='large'
                  className='flex-1'
                  style={{ width: 90 }}
                  onClick={() => setPersonAttendace({})}
                >
                  Hủy
                </Button>
                {user.personType !== TYPE_PERSON['Sinh viên'] && !sessionAttendaceStatus && (
                  <Button type='primary' size='large' className='flex-1' style={{ width: 90 }} onClick={handleSave}>
                    Lưu
                  </Button>
                )}
              </div>
            </Col>
          </Row>
        </div>
      </Spin>
    </Modal>
  )
}

export default ModalCheckinRecordInfo
